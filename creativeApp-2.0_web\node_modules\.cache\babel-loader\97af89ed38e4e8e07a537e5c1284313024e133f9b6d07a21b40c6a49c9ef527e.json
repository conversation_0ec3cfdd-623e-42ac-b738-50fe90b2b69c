{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\components\\\\password-manager\\\\AddPasswordCardForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\n\n// Main component for the Add Password Card form\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddPasswordCardForm = ({\n  onSubmit,\n  onCancel,\n  generatedPassword,\n  passwordStrength\n}) => {\n  _s();\n  var _teamsByDepartment$fo;\n  // State to hold form data\n  const [formData, setFormData] = useState({\n    title: '',\n    password: '',\n    team: '',\n    department: '',\n    strength: 'Weak Password'\n  });\n\n  // Department and Team options for the select dropdowns\n  const departments = ['IT', 'Development', 'DevOps', 'Project Management', 'HR', 'Database', 'Marketing', 'Sales'];\n  const teamsByDepartment = {\n    'IT': ['IT Support', 'System Admin', 'Network Team'],\n    'Development': ['Frontend Team', 'Backend Team', 'Full Stack Team'],\n    'DevOps': ['Infrastructure Team', 'CI/CD Team', 'Cloud Team'],\n    'Project Management': ['Scrum Masters', 'Product Owners', 'PMO'],\n    'HR': ['Recruitment', 'Employee Relations', 'Payroll'],\n    'Database': ['DBA Team', 'Data Analytics', 'Data Engineering'],\n    'Marketing': ['Digital Marketing', 'Content Team', 'SEO Team'],\n    'Sales': ['Inside Sales', 'Field Sales', 'Sales Support']\n  };\n\n  // State for showing/hiding the password, individual field errors, and a general form error\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [formError, setFormError] = useState(''); // **NEW**: State for the main error message\n\n  // Effect to update the form's password field when a new password is generated\n  useEffect(() => {\n    if (generatedPassword) {\n      setFormData(prev => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength\n      }));\n    }\n  }, [generatedPassword, passwordStrength]);\n\n  // Handles input changes for all form fields\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Reset team selection when the department changes\n    if (name === 'department') {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value,\n        team: ''\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n\n    // Clear the error for a field when the user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Automatically calculate password strength as the user types\n    if (name === 'password') {\n      const strength = calculatePasswordStrength(value);\n      setFormData(prev => ({\n        ...prev,\n        strength: strength\n      }));\n    }\n  };\n\n  // Calculates the strength of a given password\n  const calculatePasswordStrength = password => {\n    if (!password) return 'Weak Password';\n    let score = 0;\n    if (password.length >= 12) score += 2;else if (password.length >= 8) score += 1;\n    if (/[a-z]/.test(password)) score += 1;\n    if (/[A-Z]/.test(password)) score += 1;\n    if (/[0-9]/.test(password)) score += 1;\n    if (/[^A-Za-z0-9]/.test(password)) score += 1;\n    if (password.length >= 16) score += 1;\n    if (score >= 6) return 'Strong Password';\n    if (score >= 4) return 'Moderate Password';\n    return 'Weak Password';\n  };\n\n  // Returns Tailwind CSS classes based on password strength for styling\n  const getStrengthColor = strength => {\n    switch (strength) {\n      case 'Strong Password':\n        return 'bg-green-100 text-green-600 border-green-300';\n      case 'Moderate Password':\n        return 'bg-yellow-100 text-yellow-600 border-yellow-300';\n      case 'Weak Password':\n        return 'bg-red-100 text-red-600 border-red-300';\n      default:\n        return 'bg-gray-100 text-gray-600 border-gray-300';\n    }\n  };\n\n  // Validates the form fields before submission\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.department.trim()) newErrors.department = 'Department is required';\n    if (!formData.team.trim()) newErrors.team = 'Team is required';\n    if (!formData.title.trim()) newErrors.title = 'Title is required';\n    if (!formData.password.trim()) newErrors.password = 'Password is required';\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Handles the form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n    setFormError(''); // Clear previous general errors\n\n    // **MODIFICATION**: Check validation and set a general error if it fails\n    if (validateForm()) {\n      const strengthColor = getStrengthColor(formData.strength);\n      const cardData = {\n        ...formData,\n        strengthColor\n      };\n      onSubmit(cardData); // This function is passed from the parent component\n\n      // Reset form after successful submission\n      setFormData({\n        title: '',\n        password: '',\n        team: '',\n        department: '',\n        strength: 'Weak Password'\n      });\n    } else {\n      // **NEW**: Set a clear error message for the user\n      setFormError('Please fill out all required fields before saving.');\n    }\n  };\n\n  // Toggles password visibility\n  const togglePasswordVisibility = () => setShowPassword(!showPassword);\n\n  // Sets the form password to the generated password\n  const useGeneratedPassword = () => {\n    if (generatedPassword) {\n      setFormData(prev => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"rounded-lg shadow-md w-full max-w-4xl relative bg-neutral-50 m-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-4 bg-gray-100 px-4 py-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-medium text-left text-gray-800\",\n          children: \"Add New Password Card\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-3xl text-gray-500 hover:text-gray-800\",\n          onClick: onCancel,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-4 p-6 overflow-y-auto max-h-[80vh] scrollbar-vertical\",\n          children: [formError && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full p-3 mb-2 text-sm text-red-800 rounded-lg bg-red-100 border border-red-300\",\n            role: \"alert\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"Action Required:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this), \" \", formError]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 w-full md:max-w-[48%] text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"department\",\n              className: \"block pb-2 text-base text-gray-600\",\n              children: [\"Department \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-red-600\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 101\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"department\",\n              name: \"department\",\n              value: formData.department,\n              onChange: handleInputChange,\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.department ? 'border-red-500' : 'border-gray-300'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Department\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), departments.map(dept => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: dept,\n                children: dept\n              }, dept, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 43\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), errors.department && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.department\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 w-full md:max-w-[48%] text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"team\",\n              className: \"block pb-2 text-base text-gray-600\",\n              children: [\"Team \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-red-600\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 89\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"team\",\n              name: \"team\",\n              value: formData.team,\n              onChange: handleInputChange,\n              disabled: !formData.department,\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.team ? 'border-red-500' : 'border-gray-300'} ${!formData.department ? 'bg-gray-100 cursor-not-allowed' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), formData.department && ((_teamsByDepartment$fo = teamsByDepartment[formData.department]) === null || _teamsByDepartment$fo === void 0 ? void 0 : _teamsByDepartment$fo.map(team => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: team,\n                children: team\n              }, team, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 94\n              }, this)))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), errors.team && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.team\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 31\n            }, this), !formData.department && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-500\",\n              children: \"Please select a department first\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 w-full md:max-w-[48%] text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"title\",\n              className: \"block pb-2 text-base text-gray-600\",\n              children: [\"Platform/Service Title \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-red-600\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 108\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"title\",\n              name: \"title\",\n              value: formData.title,\n              onChange: handleInputChange,\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.title ? 'border-red-500' : 'border-gray-300'}`,\n              placeholder: \"e.g., Gmail, GitHub, AWS Console\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), errors.title && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 32\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 w-full md:max-w-[48%] text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"block pb-2 text-base text-gray-600\",\n              children: [\"Password \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-red-600\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 97\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: showPassword ? \"text\" : \"password\",\n                id: \"password\",\n                name: \"password\",\n                value: formData.password,\n                onChange: handleInputChange,\n                className: `w-full px-3 py-2 pr-24 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.password ? 'border-red-500' : 'border-gray-300'}`,\n                placeholder: \"Enter password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 right-0 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: useGeneratedPassword,\n                  className: \"px-2 text-xs text-blue-600 hover:text-blue-800 border-r border-gray-300\",\n                  title: \"Use generated password from above\",\n                  children: \"Use Gen\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: togglePasswordVisibility,\n                  className: \"pr-3 pl-2 flex items-center text-gray-400 hover:text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-symbols-rounded text-sm\",\n                    children: showPassword ? 'visibility_off' : 'visibility'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.password\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 35\n            }, this), formData.password && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-block px-3 py-1 text-xs font-medium rounded-full border ${getStrengthColor(formData.strength)}`,\n                children: formData.strength\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 60\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-4 p-6 bg-gray-50 border-t\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: onCancel,\n            className: \"px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"px-6 py-2 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\",\n            children: \"Save Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 5\n  }, this);\n};\n_s(AddPasswordCardForm, \"3787KOf8uhpWGbGU1x4TE0Ur1oU=\");\n_c = AddPasswordCardForm;\nexport default AddPasswordCardForm;\nvar _c;\n$RefreshReg$(_c, \"AddPasswordCardForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AddPasswordCardForm", "onSubmit", "onCancel", "generatedPassword", "passwordStrength", "_s", "_teamsByDepartment$fo", "formData", "setFormData", "title", "password", "team", "department", "strength", "departments", "teamsByDepartment", "showPassword", "setShowPassword", "errors", "setErrors", "formError", "setFormError", "prev", "handleInputChange", "e", "name", "value", "target", "calculatePasswordStrength", "score", "length", "test", "getStrengthColor", "validateForm", "newErrors", "trim", "Object", "keys", "handleSubmit", "preventDefault", "strengthColor", "cardData", "togglePasswordVisibility", "useGeneratedPassword", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "role", "htmlFor", "id", "onChange", "map", "dept", "disabled", "type", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/AddPasswordCardForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\n// Main component for the Add Password Card form\nconst AddPasswordCardForm = ({ onSubmit, onCancel, generatedPassword, passwordStrength }) => {\n  // State to hold form data\n  const [formData, setFormData] = useState({\n    title: '',\n    password: '',\n    team: '',\n    department: '',\n    strength: 'Weak Password'\n  });\n\n  // Department and Team options for the select dropdowns\n  const departments = [\n    'IT', 'Development', 'DevOps', 'Project Management', 'HR', 'Database', 'Marketing', 'Sales'\n  ];\n\n  const teamsByDepartment = {\n    'IT': ['IT Support', 'System Admin', 'Network Team'],\n    'Development': ['Frontend Team', 'Backend Team', 'Full Stack Team'],\n    'DevOps': ['Infrastructure Team', 'CI/CD Team', 'Cloud Team'],\n    'Project Management': ['Scrum Masters', 'Product Owners', 'PMO'],\n    'HR': ['Recruitment', 'Employee Relations', 'Payroll'],\n    'Database': ['DBA Team', 'Data Analytics', 'Data Engineering'],\n    'Marketing': ['Digital Marketing', 'Content Team', 'SEO Team'],\n    'Sales': ['Inside Sales', 'Field Sales', 'Sales Support']\n  };\n\n  // State for showing/hiding the password, individual field errors, and a general form error\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [formError, setFormError] = useState(''); // **NEW**: State for the main error message\n\n  // Effect to update the form's password field when a new password is generated\n  useEffect(() => {\n    if (generatedPassword) {\n      setFormData(prev => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength\n      }));\n    }\n  }, [generatedPassword, passwordStrength]);\n\n  // Handles input changes for all form fields\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n\n    // Reset team selection when the department changes\n    if (name === 'department') {\n      setFormData(prev => ({ ...prev, [name]: value, team: '' }));\n    } else {\n      setFormData(prev => ({ ...prev, [name]: value }));\n    }\n\n    // Clear the error for a field when the user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n\n    // Automatically calculate password strength as the user types\n    if (name === 'password') {\n      const strength = calculatePasswordStrength(value);\n      setFormData(prev => ({ ...prev, strength: strength }));\n    }\n  };\n\n  // Calculates the strength of a given password\n  const calculatePasswordStrength = (password) => {\n    if (!password) return 'Weak Password';\n    let score = 0;\n    if (password.length >= 12) score += 2;\n    else if (password.length >= 8) score += 1;\n    if (/[a-z]/.test(password)) score += 1;\n    if (/[A-Z]/.test(password)) score += 1;\n    if (/[0-9]/.test(password)) score += 1;\n    if (/[^A-Za-z0-9]/.test(password)) score += 1;\n    if (password.length >= 16) score += 1;\n    if (score >= 6) return 'Strong Password';\n    if (score >= 4) return 'Moderate Password';\n    return 'Weak Password';\n  };\n\n  // Returns Tailwind CSS classes based on password strength for styling\n  const getStrengthColor = (strength) => {\n    switch (strength) {\n      case 'Strong Password': return 'bg-green-100 text-green-600 border-green-300';\n      case 'Moderate Password': return 'bg-yellow-100 text-yellow-600 border-yellow-300';\n      case 'Weak Password': return 'bg-red-100 text-red-600 border-red-300';\n      default: return 'bg-gray-100 text-gray-600 border-gray-300';\n    }\n  };\n\n  // Validates the form fields before submission\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.department.trim()) newErrors.department = 'Department is required';\n    if (!formData.team.trim()) newErrors.team = 'Team is required';\n    if (!formData.title.trim()) newErrors.title = 'Title is required';\n    if (!formData.password.trim()) newErrors.password = 'Password is required';\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Handles the form submission\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    setFormError(''); // Clear previous general errors\n\n    // **MODIFICATION**: Check validation and set a general error if it fails\n    if (validateForm()) {\n      const strengthColor = getStrengthColor(formData.strength);\n      const cardData = { ...formData, strengthColor };\n      onSubmit(cardData); // This function is passed from the parent component\n      \n      // Reset form after successful submission\n      setFormData({ title: '', password: '', team: '', department: '', strength: 'Weak Password' });\n    } else {\n      // **NEW**: Set a clear error message for the user\n      setFormError('Please fill out all required fields before saving.');\n    }\n  };\n\n  // Toggles password visibility\n  const togglePasswordVisibility = () => setShowPassword(!showPassword);\n\n  // Sets the form password to the generated password\n  const useGeneratedPassword = () => {\n    if (generatedPassword) {\n      setFormData(prev => ({ ...prev, password: generatedPassword, strength: passwordStrength }));\n    }\n  };\n\n  return (\n    <div className=\"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\">\n      <div className=\"rounded-lg shadow-md w-full max-w-4xl relative bg-neutral-50 m-4\">\n        <div className=\"flex justify-between items-center mb-4 bg-gray-100 px-4 py-3\">\n          <h4 className=\"text-lg font-medium text-left text-gray-800\">Add New Password Card</h4>\n          <button className=\"text-3xl text-gray-500 hover:text-gray-800\" onClick={onCancel}>&times;</button>\n        </div>\n\n        <form onSubmit={handleSubmit}>\n          <div className='flex flex-wrap gap-4 p-6 overflow-y-auto max-h-[80vh] scrollbar-vertical'>\n            \n            {/* **NEW**: Display general form error message here */}\n            {formError && (\n              <div className=\"w-full p-3 mb-2 text-sm text-red-800 rounded-lg bg-red-100 border border-red-300\" role=\"alert\">\n                <span className=\"font-medium\">Action Required:</span> {formError}\n              </div>\n            )}\n\n            {/* Department Dropdown */}\n            <div className=\"mb-4 w-full md:max-w-[48%] text-left\">\n              <label htmlFor=\"department\" className=\"block pb-2 text-base text-gray-600\">Department <span className='text-red-600'>*</span></label>\n              <select id=\"department\" name=\"department\" value={formData.department} onChange={handleInputChange} className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.department ? 'border-red-500' : 'border-gray-300'}`}>\n                <option value=\"\">Select Department</option>\n                {departments.map(dept => (<option key={dept} value={dept}>{dept}</option>))}\n              </select>\n              {errors.department && <p className=\"mt-1 text-sm text-red-600\">{errors.department}</p>}\n            </div>\n\n            {/* Team Dropdown */}\n            <div className=\"mb-4 w-full md:max-w-[48%] text-left\">\n              <label htmlFor=\"team\" className=\"block pb-2 text-base text-gray-600\">Team <span className='text-red-600'>*</span></label>\n              <select id=\"team\" name=\"team\" value={formData.team} onChange={handleInputChange} disabled={!formData.department} className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.team ? 'border-red-500' : 'border-gray-300'} ${!formData.department ? 'bg-gray-100 cursor-not-allowed' : ''}`}>\n                <option value=\"\">Select Team</option>\n                {formData.department && teamsByDepartment[formData.department]?.map(team => (<option key={team} value={team}>{team}</option>))}\n              </select>\n              {errors.team && <p className=\"mt-1 text-sm text-red-600\">{errors.team}</p>}\n              {!formData.department && (<p className=\"mt-1 text-sm text-gray-500\">Please select a department first</p>)}\n            </div>\n            \n            {/* Platform/Service Title Input */}\n            <div className=\"mb-4 w-full md:max-w-[48%] text-left\">\n              <label htmlFor=\"title\" className=\"block pb-2 text-base text-gray-600\">Platform/Service Title <span className='text-red-600'>*</span></label>\n              <input type=\"text\" id=\"title\" name=\"title\" value={formData.title} onChange={handleInputChange} className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.title ? 'border-red-500' : 'border-gray-300'}`} placeholder=\"e.g., Gmail, GitHub, AWS Console\" />\n              {errors.title && <p className=\"mt-1 text-sm text-red-600\">{errors.title}</p>}\n            </div>\n\n            {/* Password Input */}\n            <div className=\"mb-4 w-full md:max-w-[48%] text-left\">\n              <label htmlFor=\"password\" className=\"block pb-2 text-base text-gray-600\">Password <span className='text-red-600'>*</span></label>\n              <div className=\"relative\">\n                <input type={showPassword ? \"text\" : \"password\"} id=\"password\" name=\"password\" value={formData.password} onChange={handleInputChange} className={`w-full px-3 py-2 pr-24 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.password ? 'border-red-500' : 'border-gray-300'}`} placeholder=\"Enter password\" />\n                <div className=\"absolute inset-y-0 right-0 flex items-center\">\n                  <button type=\"button\" onClick={useGeneratedPassword} className=\"px-2 text-xs text-blue-600 hover:text-blue-800 border-r border-gray-300\" title=\"Use generated password from above\">Use Gen</button>\n                  <button type=\"button\" onClick={togglePasswordVisibility} className=\"pr-3 pl-2 flex items-center text-gray-400 hover:text-gray-600\">\n                    <span className=\"material-symbols-rounded text-sm\">{showPassword ? 'visibility_off' : 'visibility'}</span>\n                  </button>\n                </div>\n              </div>\n              {errors.password && <p className=\"mt-1 text-sm text-red-600\">{errors.password}</p>}\n              {formData.password && (<div className=\"mt-2\"><span className={`inline-block px-3 py-1 text-xs font-medium rounded-full border ${getStrengthColor(formData.strength)}`}>{formData.strength}</span></div>)}\n            </div>\n          </div>\n\n          {/* Form Action Buttons */}\n          <div className=\"flex justify-end space-x-4 p-6 bg-gray-50 border-t\">\n            <button type=\"button\" onClick={onCancel} className=\"px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\">Cancel</button>\n            <button type=\"submit\" className=\"px-6 py-2 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\">Save Password</button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default AddPasswordCardForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;;AAElD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,iBAAiB;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAC3F;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC;IACvCa,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMC,WAAW,GAAG,CAClB,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,oBAAoB,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAC5F;EAED,MAAMC,iBAAiB,GAAG;IACxB,IAAI,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC;IACpD,aAAa,EAAE,CAAC,eAAe,EAAE,cAAc,EAAE,iBAAiB,CAAC;IACnE,QAAQ,EAAE,CAAC,qBAAqB,EAAE,YAAY,EAAE,YAAY,CAAC;IAC7D,oBAAoB,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,KAAK,CAAC;IAChE,IAAI,EAAE,CAAC,aAAa,EAAE,oBAAoB,EAAE,SAAS,CAAC;IACtD,UAAU,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,kBAAkB,CAAC;IAC9D,WAAW,EAAE,CAAC,mBAAmB,EAAE,cAAc,EAAE,UAAU,CAAC;IAC9D,OAAO,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,eAAe;EAC1D,CAAC;;EAED;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIM,iBAAiB,EAAE;MACrBK,WAAW,CAACc,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPZ,QAAQ,EAAEP,iBAAiB;QAC3BU,QAAQ,EAAET;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACD,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;;EAEzC;EACA,MAAMmB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA,IAAIF,IAAI,KAAK,YAAY,EAAE;MACzBjB,WAAW,CAACc,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACG,IAAI,GAAGC,KAAK;QAAEf,IAAI,EAAE;MAAG,CAAC,CAAC,CAAC;IAC7D,CAAC,MAAM;MACLH,WAAW,CAACc,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACG,IAAI,GAAGC;MAAM,CAAC,CAAC,CAAC;IACnD;;IAEA;IACA,IAAIR,MAAM,CAACO,IAAI,CAAC,EAAE;MAChBN,SAAS,CAACG,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACG,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IAC9C;;IAEA;IACA,IAAIA,IAAI,KAAK,UAAU,EAAE;MACvB,MAAMZ,QAAQ,GAAGe,yBAAyB,CAACF,KAAK,CAAC;MACjDlB,WAAW,CAACc,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAET,QAAQ,EAAEA;MAAS,CAAC,CAAC,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMe,yBAAyB,GAAIlB,QAAQ,IAAK;IAC9C,IAAI,CAACA,QAAQ,EAAE,OAAO,eAAe;IACrC,IAAImB,KAAK,GAAG,CAAC;IACb,IAAInB,QAAQ,CAACoB,MAAM,IAAI,EAAE,EAAED,KAAK,IAAI,CAAC,CAAC,KACjC,IAAInB,QAAQ,CAACoB,MAAM,IAAI,CAAC,EAAED,KAAK,IAAI,CAAC;IACzC,IAAI,OAAO,CAACE,IAAI,CAACrB,QAAQ,CAAC,EAAEmB,KAAK,IAAI,CAAC;IACtC,IAAI,OAAO,CAACE,IAAI,CAACrB,QAAQ,CAAC,EAAEmB,KAAK,IAAI,CAAC;IACtC,IAAI,OAAO,CAACE,IAAI,CAACrB,QAAQ,CAAC,EAAEmB,KAAK,IAAI,CAAC;IACtC,IAAI,cAAc,CAACE,IAAI,CAACrB,QAAQ,CAAC,EAAEmB,KAAK,IAAI,CAAC;IAC7C,IAAInB,QAAQ,CAACoB,MAAM,IAAI,EAAE,EAAED,KAAK,IAAI,CAAC;IACrC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,iBAAiB;IACxC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,mBAAmB;IAC1C,OAAO,eAAe;EACxB,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAInB,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,iBAAiB;QAAE,OAAO,8CAA8C;MAC7E,KAAK,mBAAmB;QAAE,OAAO,iDAAiD;MAClF,KAAK,eAAe;QAAE,OAAO,wCAAwC;MACrE;QAAS,OAAO,2CAA2C;IAC7D;EACF,CAAC;;EAED;EACA,MAAMoB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpB,IAAI,CAAC3B,QAAQ,CAACK,UAAU,CAACuB,IAAI,CAAC,CAAC,EAAED,SAAS,CAACtB,UAAU,GAAG,wBAAwB;IAChF,IAAI,CAACL,QAAQ,CAACI,IAAI,CAACwB,IAAI,CAAC,CAAC,EAAED,SAAS,CAACvB,IAAI,GAAG,kBAAkB;IAC9D,IAAI,CAACJ,QAAQ,CAACE,KAAK,CAAC0B,IAAI,CAAC,CAAC,EAAED,SAAS,CAACzB,KAAK,GAAG,mBAAmB;IACjE,IAAI,CAACF,QAAQ,CAACG,QAAQ,CAACyB,IAAI,CAAC,CAAC,EAAED,SAAS,CAACxB,QAAQ,GAAG,sBAAsB;IAC1ES,SAAS,CAACe,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACJ,MAAM,KAAK,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMQ,YAAY,GAAId,CAAC,IAAK;IAC1BA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClBlB,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;;IAElB;IACA,IAAIY,YAAY,CAAC,CAAC,EAAE;MAClB,MAAMO,aAAa,GAAGR,gBAAgB,CAACzB,QAAQ,CAACM,QAAQ,CAAC;MACzD,MAAM4B,QAAQ,GAAG;QAAE,GAAGlC,QAAQ;QAAEiC;MAAc,CAAC;MAC/CvC,QAAQ,CAACwC,QAAQ,CAAC,CAAC,CAAC;;MAEpB;MACAjC,WAAW,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,IAAI,EAAE,EAAE;QAAEC,UAAU,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAgB,CAAC,CAAC;IAC/F,CAAC,MAAM;MACL;MACAQ,YAAY,CAAC,oDAAoD,CAAC;IACpE;EACF,CAAC;;EAED;EACA,MAAMqB,wBAAwB,GAAGA,CAAA,KAAMzB,eAAe,CAAC,CAACD,YAAY,CAAC;;EAErE;EACA,MAAM2B,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIxC,iBAAiB,EAAE;MACrBK,WAAW,CAACc,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEZ,QAAQ,EAAEP,iBAAiB;QAAEU,QAAQ,EAAET;MAAiB,CAAC,CAAC,CAAC;IAC7F;EACF,CAAC;EAED,oBACEL,OAAA;IAAK6C,SAAS,EAAC,kHAAkH;IAAAC,QAAA,eAC/H9C,OAAA;MAAK6C,SAAS,EAAC,kEAAkE;MAAAC,QAAA,gBAC/E9C,OAAA;QAAK6C,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3E9C,OAAA;UAAI6C,SAAS,EAAC,6CAA6C;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtFlD,OAAA;UAAQ6C,SAAS,EAAC,4CAA4C;UAACM,OAAO,EAAEhD,QAAS;UAAA2C,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC,eAENlD,OAAA;QAAME,QAAQ,EAAEqC,YAAa;QAAAO,QAAA,gBAC3B9C,OAAA;UAAK6C,SAAS,EAAC,0EAA0E;UAAAC,QAAA,GAGtFzB,SAAS,iBACRrB,OAAA;YAAK6C,SAAS,EAAC,kFAAkF;YAACO,IAAI,EAAC,OAAO;YAAAN,QAAA,gBAC5G9C,OAAA;cAAM6C,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAAC,EAAC7B,SAAS;UAAA;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CACN,eAGDlD,OAAA;YAAK6C,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACnD9C,OAAA;cAAOqD,OAAO,EAAC,YAAY;cAACR,SAAS,EAAC,oCAAoC;cAAAC,QAAA,GAAC,aAAW,eAAA9C,OAAA;gBAAM6C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrIlD,OAAA;cAAQsD,EAAE,EAAC,YAAY;cAAC5B,IAAI,EAAC,YAAY;cAACC,KAAK,EAAEnB,QAAQ,CAACK,UAAW;cAAC0C,QAAQ,EAAE/B,iBAAkB;cAACqB,SAAS,EAAE,0HAA0H1B,MAAM,CAACN,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,EAAG;cAAAiC,QAAA,gBACjS9C,OAAA;gBAAQ2B,KAAK,EAAC,EAAE;gBAAAmB,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC1CnC,WAAW,CAACyC,GAAG,CAACC,IAAI,iBAAKzD,OAAA;gBAAmB2B,KAAK,EAAE8B,IAAK;gBAAAX,QAAA,EAAEW;cAAI,GAAxBA,IAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA6B,CAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,EACR/B,MAAM,CAACN,UAAU,iBAAIb,OAAA;cAAG6C,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE3B,MAAM,CAACN;YAAU;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eAGNlD,OAAA;YAAK6C,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACnD9C,OAAA;cAAOqD,OAAO,EAAC,MAAM;cAACR,SAAS,EAAC,oCAAoC;cAAAC,QAAA,GAAC,OAAK,eAAA9C,OAAA;gBAAM6C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzHlD,OAAA;cAAQsD,EAAE,EAAC,MAAM;cAAC5B,IAAI,EAAC,MAAM;cAACC,KAAK,EAAEnB,QAAQ,CAACI,IAAK;cAAC2C,QAAQ,EAAE/B,iBAAkB;cAACkC,QAAQ,EAAE,CAAClD,QAAQ,CAACK,UAAW;cAACgC,SAAS,EAAE,0HAA0H1B,MAAM,CAACP,IAAI,GAAG,gBAAgB,GAAG,iBAAiB,IAAI,CAACJ,QAAQ,CAACK,UAAU,GAAG,gCAAgC,GAAG,EAAE,EAAG;cAAAiC,QAAA,gBACzW9C,OAAA;gBAAQ2B,KAAK,EAAC,EAAE;gBAAAmB,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACpC1C,QAAQ,CAACK,UAAU,MAAAN,qBAAA,GAAIS,iBAAiB,CAACR,QAAQ,CAACK,UAAU,CAAC,cAAAN,qBAAA,uBAAtCA,qBAAA,CAAwCiD,GAAG,CAAC5C,IAAI,iBAAKZ,OAAA;gBAAmB2B,KAAK,EAAEf,IAAK;gBAAAkC,QAAA,EAAElC;cAAI,GAAxBA,IAAI;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA6B,CAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxH,CAAC,EACR/B,MAAM,CAACP,IAAI,iBAAIZ,OAAA;cAAG6C,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE3B,MAAM,CAACP;YAAI;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACzE,CAAC1C,QAAQ,CAACK,UAAU,iBAAKb,OAAA;cAAG6C,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CAAC,eAGNlD,OAAA;YAAK6C,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACnD9C,OAAA;cAAOqD,OAAO,EAAC,OAAO;cAACR,SAAS,EAAC,oCAAoC;cAAAC,QAAA,GAAC,yBAAuB,eAAA9C,OAAA;gBAAM6C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5IlD,OAAA;cAAO2D,IAAI,EAAC,MAAM;cAACL,EAAE,EAAC,OAAO;cAAC5B,IAAI,EAAC,OAAO;cAACC,KAAK,EAAEnB,QAAQ,CAACE,KAAM;cAAC6C,QAAQ,EAAE/B,iBAAkB;cAACqB,SAAS,EAAE,0HAA0H1B,MAAM,CAACT,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,EAAG;cAACkD,WAAW,EAAC;YAAkC;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC3U/B,MAAM,CAACT,KAAK,iBAAIV,OAAA;cAAG6C,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE3B,MAAM,CAACT;YAAK;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAGNlD,OAAA;YAAK6C,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACnD9C,OAAA;cAAOqD,OAAO,EAAC,UAAU;cAACR,SAAS,EAAC,oCAAoC;cAAAC,QAAA,GAAC,WAAS,eAAA9C,OAAA;gBAAM6C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjIlD,OAAA;cAAK6C,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB9C,OAAA;gBAAO2D,IAAI,EAAE1C,YAAY,GAAG,MAAM,GAAG,UAAW;gBAACqC,EAAE,EAAC,UAAU;gBAAC5B,IAAI,EAAC,UAAU;gBAACC,KAAK,EAAEnB,QAAQ,CAACG,QAAS;gBAAC4C,QAAQ,EAAE/B,iBAAkB;gBAACqB,SAAS,EAAE,gIAAgI1B,MAAM,CAACR,QAAQ,GAAG,gBAAgB,GAAG,iBAAiB,EAAG;gBAACiD,WAAW,EAAC;cAAgB;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1WlD,OAAA;gBAAK6C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,gBAC3D9C,OAAA;kBAAQ2D,IAAI,EAAC,QAAQ;kBAACR,OAAO,EAAEP,oBAAqB;kBAACC,SAAS,EAAC,yEAAyE;kBAACnC,KAAK,EAAC,mCAAmC;kBAAAoC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnMlD,OAAA;kBAAQ2D,IAAI,EAAC,QAAQ;kBAACR,OAAO,EAAER,wBAAyB;kBAACE,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,eAChI9C,OAAA;oBAAM6C,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAE7B,YAAY,GAAG,gBAAgB,GAAG;kBAAY;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACL/B,MAAM,CAACR,QAAQ,iBAAIX,OAAA;cAAG6C,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE3B,MAAM,CAACR;YAAQ;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACjF1C,QAAQ,CAACG,QAAQ,iBAAKX,OAAA;cAAK6C,SAAS,EAAC,MAAM;cAAAC,QAAA,eAAC9C,OAAA;gBAAM6C,SAAS,EAAE,kEAAkEZ,gBAAgB,CAACzB,QAAQ,CAACM,QAAQ,CAAC,EAAG;gBAAAgC,QAAA,EAAEtC,QAAQ,CAACM;cAAQ;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlD,OAAA;UAAK6C,SAAS,EAAC,oDAAoD;UAAAC,QAAA,gBACjE9C,OAAA;YAAQ2D,IAAI,EAAC,QAAQ;YAACR,OAAO,EAAEhD,QAAS;YAAC0C,SAAS,EAAC,wKAAwK;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC3OlD,OAAA;YAAQ2D,IAAI,EAAC,QAAQ;YAACd,SAAS,EAAC,8KAA8K;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5C,EAAA,CA3MIL,mBAAmB;AAAA4D,EAAA,GAAnB5D,mBAAmB;AA6MzB,eAAeA,mBAAmB;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}