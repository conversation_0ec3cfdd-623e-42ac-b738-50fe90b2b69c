{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\components\\\\password-manager\\\\PasswordGenerator.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { RefreshCw } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PasswordGenerator = ({\n  onPasswordGenerated\n}) => {\n  _s();\n  const [password, setPassword] = useState('xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn');\n  const [length, setLength] = useState(64);\n  const [options, setOptions] = useState({\n    uppercase: true,\n    lowercase: true,\n    numbers: true,\n    symbols: true\n  });\n  const [strength, setStrength] = useState('Strong Password');\n  const generatePassword = () => {\n    let charset = '';\n    if (options.uppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';\n    if (options.lowercase) charset += 'abcdefghijklmnopqrstuvwxyz';\n    if (options.numbers) charset += '0123456789';\n    if (options.symbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';\n    if (charset === '') {\n      setPassword('');\n      return;\n    }\n    let newPassword = '';\n    for (let i = 0; i < length; i++) {\n      newPassword += charset.charAt(Math.floor(Math.random() * charset.length));\n    }\n    setPassword(newPassword);\n\n    // Calculate strength\n    const newStrength = calculatePasswordStrength(newPassword);\n    setStrength(newStrength);\n\n    // Notify parent component\n    if (onPasswordGenerated) {\n      onPasswordGenerated(newPassword, newStrength);\n    }\n  };\n  const calculatePasswordStrength = pwd => {\n    let score = 0;\n\n    // Length check\n    if (pwd.length >= 12) score += 2;else if (pwd.length >= 8) score += 1;\n\n    // Character variety checks\n    if (/[a-z]/.test(pwd)) score += 1;\n    if (/[A-Z]/.test(pwd)) score += 1;\n    if (/[0-9]/.test(pwd)) score += 1;\n    if (/[^A-Za-z0-9]/.test(pwd)) score += 1;\n\n    // Additional complexity\n    if (pwd.length >= 16) score += 1;\n    if (score >= 6) return 'Strong Password';\n    if (score >= 4) return 'Moderate Password';\n    return 'Weak Password';\n  };\n  const getStrengthColor = () => {\n    switch (strength) {\n      case 'Strong Password':\n        return 'bg-green/10 text-[#22C55E]';\n      case 'Moderate Password':\n        return 'bg-yellow/10 text-[#F59E0B]';\n      case 'Weak Password':\n        return 'bg-red/10 text-[#EF4444]';\n      default:\n        return 'bg-gray-500 text-white';\n    }\n  };\n  const copyToClipboard = () => {\n    navigator.clipboard.writeText(password);\n    // You could add a toast notification here\n  };\n  const handleOptionChange = option => {\n    const newOptions = {\n      ...options,\n      [option]: !options[option]\n    };\n    setOptions(newOptions);\n  };\n  const handleLengthChange = e => {\n    const newLength = parseInt(e.target.value);\n    setLength(newLength);\n  };\n\n  // Auto-generate when options change\n  useEffect(() => {\n    generatePassword();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [length, options]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-left text-2xl font-bold text-gray-900 mb-6\",\n      children: \"Password Manager\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"rounded-2xl border border-[#DFECF1] p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8 rounded-2xl p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative inline-block w-full max-w-6xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: password,\n            readOnly: true,\n            className: \"w-full px-6 py-2 pr-16 bg-gray-50 border border-gray-200 rounded-full text-lg text-gray-700 font-mono text-center\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: generatePassword,\n            className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mt-4 max-w-6xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `px-4 py-2 rounded-full text-sm font-medium ${getStrengthColor()}`,\n            children: strength\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: copyToClipboard,\n            className: \"px-12 py-3 bg-[#FF9F19] hover:bg-yellow-500 text-white rounded-lg text-sm font-medium transition-colors\",\n            children: \"Copy Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col gap-2 max-w-2xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg font-semibold text-primary-600 whitespace-nowrap\",\n              children: [\"Password Length: \", length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"range\",\n              min: \"4\",\n              max: \"128\",\n              value: length,\n              onChange: handleLengthChange,\n              className: \"slider w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\",\n              style: {\n                background: `linear-gradient(to right, #006f99 0%, #006f99 ${(length - 4) / (128 - 4) * 100}%, #e5e7eb ${(length - 4) / (128 - 4) * 100}%, #e5e7eb 100%)`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 7\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 5\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600 text-right\",\n            children: \"*Between 4 and 128 characters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 5\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 3\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 1\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center gap-8 max-w-2xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"uppercase\",\n              type: \"checkbox\",\n              checked: options.uppercase,\n              onChange: () => handleOptionChange(\"uppercase\"),\n              className: \"w-5 h-5 appearance-none rounded border-2 border-gray-300 bg-white checked:bg-primary checked:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"uppercase\",\n            className: \"ml-2 text-sm font-medium text-gray-700 whitespace-nowrap\",\n            children: \"Uppercase (A-Z)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"lowercase\",\n              type: \"checkbox\",\n              checked: options.lowercase,\n              onChange: () => handleOptionChange(\"lowercase\"),\n              className: \"w-5 h-5 appearance-none rounded border-2 border-gray-300 bg-prio checked:bg-primary checked:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"lowercase\",\n            className: \"ml-2 text-sm font-medium text-gray-700 whitespace-nowrap\",\n            children: \"Lowercase (a-z)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"numbers\",\n              type: \"checkbox\",\n              checked: options.numbers,\n              onChange: () => handleOptionChange(\"numbers\"),\n              className: \"w-5 h-5 appearance-none rounded border-2 border-gray-300 bg-white checked:bg-primary checked:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"numbers\",\n            className: \"ml-2 text-sm font-medium text-gray-700 whitespace-nowrap\",\n            children: \"Number (0-9)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"symbols\",\n              type: \"checkbox\",\n              checked: options.symbols,\n              onChange: () => handleOptionChange(\"symbols\"),\n              className: \"w-5 h-5 appearance-none rounded border-2 border-gray-300 bg-white checked:bg-primary checked:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"symbols\",\n            className: \"ml-2 text-sm font-medium text-gray-700 whitespace-nowrap\",\n            children: \"Symbols (!@#$%^&*)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 5\n  }, this);\n};\n_s(PasswordGenerator, \"S7FQOxsUEc+C8Pl+C38VgBkDDTk=\");\n_c = PasswordGenerator;\nexport default PasswordGenerator;\nvar _c;\n$RefreshReg$(_c, \"PasswordGenerator\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "RefreshCw", "jsxDEV", "_jsxDEV", "PasswordGenerator", "onPasswordGenerated", "_s", "password", "setPassword", "length", "<PERSON><PERSON><PERSON><PERSON>", "options", "setOptions", "uppercase", "lowercase", "numbers", "symbols", "strength", "setStrength", "generatePassword", "charset", "newPassword", "i", "char<PERSON>t", "Math", "floor", "random", "newStrength", "calculatePasswordStrength", "pwd", "score", "test", "getStrengthColor", "copyToClipboard", "navigator", "clipboard", "writeText", "handleOptionChange", "option", "newOptions", "handleLengthChange", "e", "<PERSON><PERSON><PERSON><PERSON>", "parseInt", "target", "value", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "readOnly", "onClick", "min", "max", "onChange", "style", "background", "id", "checked", "htmlFor", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/PasswordGenerator.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { RefreshCw } from 'lucide-react';\n\nconst PasswordGenerator = ({ onPasswordGenerated }) => {\n  const [password, setPassword] = useState('xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn');\n  const [length, setLength] = useState(64);\n  const [options, setOptions] = useState({\n    uppercase: true,\n    lowercase: true,\n    numbers: true,\n    symbols: true\n  });\n  const [strength, setStrength] = useState('Strong Password');\n\n  const generatePassword = () => {\n    let charset = '';\n    if (options.uppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';\n    if (options.lowercase) charset += 'abcdefghijklmnopqrstuvwxyz';\n    if (options.numbers) charset += '0123456789';\n    if (options.symbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';\n\n    if (charset === '') {\n      setPassword('');\n      return;\n    }\n\n    let newPassword = '';\n    for (let i = 0; i < length; i++) {\n      newPassword += charset.charAt(Math.floor(Math.random() * charset.length));\n    }\n\n    setPassword(newPassword);\n    \n    // Calculate strength\n    const newStrength = calculatePasswordStrength(newPassword);\n    setStrength(newStrength);\n    \n    // Notify parent component\n    if (onPasswordGenerated) {\n      onPasswordGenerated(newPassword, newStrength);\n    }\n  };\n\n  const calculatePasswordStrength = (pwd) => {\n    let score = 0;\n    \n    // Length check\n    if (pwd.length >= 12) score += 2;\n    else if (pwd.length >= 8) score += 1;\n    \n    // Character variety checks\n    if (/[a-z]/.test(pwd)) score += 1;\n    if (/[A-Z]/.test(pwd)) score += 1;\n    if (/[0-9]/.test(pwd)) score += 1;\n    if (/[^A-Za-z0-9]/.test(pwd)) score += 1;\n    \n    // Additional complexity\n    if (pwd.length >= 16) score += 1;\n    \n    if (score >= 6) return 'Strong Password';\n    if (score >= 4) return 'Moderate Password';\n    return 'Weak Password';\n  };\n\n  const getStrengthColor = () => {\n    switch (strength) {\n      case 'Strong Password':\n        return 'bg-green/10 text-[#22C55E]';\n      case 'Moderate Password':\n        return 'bg-yellow/10 text-[#F59E0B]';\n      case 'Weak Password':\n        return 'bg-red/10 text-[#EF4444]';\n      default:\n        return 'bg-gray-500 text-white';\n    }\n  };\n\n  const copyToClipboard = () => {\n    navigator.clipboard.writeText(password);\n    // You could add a toast notification here\n  };\n\n  const handleOptionChange = (option) => {\n    const newOptions = { ...options, [option]: !options[option] };\n    setOptions(newOptions);\n  };\n\n  const handleLengthChange = (e) => {\n    const newLength = parseInt(e.target.value);\n    setLength(newLength);\n  };\n\n  // Auto-generate when options change\n  useEffect(() => {\n    generatePassword();\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [length, options]);\n\n  return (\n    <div className=\"\">\n      {/* Title outside container */}\n      <h1 className=\"text-left text-2xl font-bold text-gray-900 mb-6\">\n        Password Manager\n      </h1>\n\n      {/* Main rounded container */}\n      <div className=\"rounded-2xl border border-[#DFECF1] p-8\">\n        {/* Password Generator Section - Center */}\n        <div className=\"text-center mb-8 rounded-2xl p-6\">\n          <div className=\"relative inline-block w-full max-w-6xl\">\n            <input\n              type=\"text\"\n              value={password}\n              readOnly\n              className=\"w-full px-6 py-2 pr-16 bg-gray-50 border border-gray-200 rounded-full text-lg text-gray-700 font-mono text-center\"\n            />\n            <button\n              onClick={generatePassword}\n              className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors\"\n            >\n              <RefreshCw className=\"w-5 h-5\" />\n            </button>\n          </div>\n\n          {/* Strong Password badge (bottom-left) and Copy Password button (right) */}\n          <div className=\"flex items-center justify-between mt-4 max-w-6xl mx-auto\">\n            <span\n              className={`px-4 py-2 rounded-full text-sm font-medium ${getStrengthColor()}`}\n            >\n              {strength}\n            </span>\n            <button\n              onClick={copyToClipboard}\n              className=\"px-12 py-3 bg-[#FF9F19] hover:bg-yellow-500 text-white rounded-lg text-sm font-medium transition-colors\"\n            >\n              Copy Password\n            </button>\n          </div>\n        </div>\n\n       {/* Password Length Section */}\n<div className=\"mb-8\">\n  <div className=\"flex flex-col gap-2 max-w-2xl mx-auto\">\n    \n    {/* Row: Label and Slider in the same line */}\n    <div className=\"flex items-center gap-4\">\n      <span className=\"text-lg font-semibold text-primary-600 whitespace-nowrap\">\n        Password Length: {length}\n      </span>\n\n      <input\n        type=\"range\"\n        min=\"4\"\n        max=\"128\"\n        value={length}\n        onChange={handleLengthChange}\n        className=\"slider w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\"\n        style={{\n          background: `linear-gradient(to right, #006f99 0%, #006f99 ${\n            ((length - 4) / (128 - 4)) * 100\n          }%, #e5e7eb ${\n            ((length - 4) / (128 - 4)) * 100\n          }%, #e5e7eb 100%)`,\n        }}\n      />\n    </div>\n\n    {/* Hint row */}\n    <div className=\"text-sm text-gray-600 text-right\">\n      *Between 4 and 128 characters\n    </div>\n  </div>\n</div>\n\n\n        {/* Character Options - Single Row */}\n        <div className=\"flex items-center justify-center gap-8 max-w-2xl mx-auto\">\n          <div className=\"flex items-center\">\n            <div className=\"relative\">\n              <input\n                id=\"uppercase\"\n                type=\"checkbox\"\n                checked={options.uppercase}\n                onChange={() => handleOptionChange(\"uppercase\")}\n                className=\"w-5 h-5 appearance-none rounded border-2 border-gray-300 bg-white checked:bg-primary checked:border-transparent\"\n              />\n              {/* {options.uppercase && (\n                <svg\n                  className=\"\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n              )} */}\n            </div>\n            <label\n              htmlFor=\"uppercase\"\n              className=\"ml-2 text-sm font-medium text-gray-700 whitespace-nowrap\"\n            >\n              Uppercase (A-Z)\n            </label>\n          </div>\n\n          <div className=\"flex items-center\">\n            <div className=\"relative\">\n              <input\n                id=\"lowercase\"\n                type=\"checkbox\"\n                checked={options.lowercase}\n                onChange={() => handleOptionChange(\"lowercase\")}\n                className=\"w-5 h-5 appearance-none rounded border-2 border-gray-300 bg-prio checked:bg-primary checked:border-transparent\"\n              />\n              {/* {options.lowercase && (\n                <svg\n                  className=\"absolute top-0.5 left-0.5 w-4 h-4 text-white pointer-events-none\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n              )} */}\n            </div>\n            <label\n              htmlFor=\"lowercase\"\n              className=\"ml-2 text-sm font-medium text-gray-700 whitespace-nowrap\"\n            >\n              Lowercase (a-z)\n            </label>\n          </div>\n\n          <div className=\"flex items-center\">\n            <div className=\"relative\">\n              <input\n                id=\"numbers\"\n                type=\"checkbox\"\n                checked={options.numbers}\n                onChange={() => handleOptionChange(\"numbers\")}\n                className=\"w-5 h-5 appearance-none rounded border-2 border-gray-300 bg-white checked:bg-primary checked:border-transparent\"\n              />\n              {/* {options.numbers && (\n                <svg\n                  className=\"absolute top-0.5 left-0.5 w-4 h-4 text-white pointer-events-none\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n              )} */}\n            </div>\n            <label\n              htmlFor=\"numbers\"\n              className=\"ml-2 text-sm font-medium text-gray-700 whitespace-nowrap\"\n            >\n              Number (0-9)\n            </label>\n          </div>\n\n          <div className=\"flex items-center\">\n            <div className=\"relative\">\n              <input\n                id=\"symbols\"\n                type=\"checkbox\"\n                checked={options.symbols}\n                onChange={() => handleOptionChange(\"symbols\")}\n                className=\"w-5 h-5 appearance-none rounded border-2 border-gray-300 bg-white checked:bg-primary checked:border-transparent\"\n              />\n              {/* {options.symbols && (\n                <svg\n                  className=\"absolute top-0.5 left-0.5 w-4 h-4 text-white pointer-events-none\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n              )} */}\n            </div>\n            <label\n              htmlFor=\"symbols\"\n              className=\"ml-2 text-sm font-medium text-gray-700 whitespace-nowrap\"\n            >\n              Symbols (!@#$%^&*)\n            </label>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PasswordGenerator;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,6CAA6C,CAAC;EACvF,MAAM,CAACU,MAAM,EAAEC,SAAS,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC;IACrCc,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,iBAAiB,CAAC;EAE3D,MAAMoB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIT,OAAO,CAACE,SAAS,EAAEO,OAAO,IAAI,4BAA4B;IAC9D,IAAIT,OAAO,CAACG,SAAS,EAAEM,OAAO,IAAI,4BAA4B;IAC9D,IAAIT,OAAO,CAACI,OAAO,EAAEK,OAAO,IAAI,YAAY;IAC5C,IAAIT,OAAO,CAACK,OAAO,EAAEI,OAAO,IAAI,4BAA4B;IAE5D,IAAIA,OAAO,KAAK,EAAE,EAAE;MAClBZ,WAAW,CAAC,EAAE,CAAC;MACf;IACF;IAEA,IAAIa,WAAW,GAAG,EAAE;IACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,MAAM,EAAEa,CAAC,EAAE,EAAE;MAC/BD,WAAW,IAAID,OAAO,CAACG,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGN,OAAO,CAACX,MAAM,CAAC,CAAC;IAC3E;IAEAD,WAAW,CAACa,WAAW,CAAC;;IAExB;IACA,MAAMM,WAAW,GAAGC,yBAAyB,CAACP,WAAW,CAAC;IAC1DH,WAAW,CAACS,WAAW,CAAC;;IAExB;IACA,IAAItB,mBAAmB,EAAE;MACvBA,mBAAmB,CAACgB,WAAW,EAAEM,WAAW,CAAC;IAC/C;EACF,CAAC;EAED,MAAMC,yBAAyB,GAAIC,GAAG,IAAK;IACzC,IAAIC,KAAK,GAAG,CAAC;;IAEb;IACA,IAAID,GAAG,CAACpB,MAAM,IAAI,EAAE,EAAEqB,KAAK,IAAI,CAAC,CAAC,KAC5B,IAAID,GAAG,CAACpB,MAAM,IAAI,CAAC,EAAEqB,KAAK,IAAI,CAAC;;IAEpC;IACA,IAAI,OAAO,CAACC,IAAI,CAACF,GAAG,CAAC,EAAEC,KAAK,IAAI,CAAC;IACjC,IAAI,OAAO,CAACC,IAAI,CAACF,GAAG,CAAC,EAAEC,KAAK,IAAI,CAAC;IACjC,IAAI,OAAO,CAACC,IAAI,CAACF,GAAG,CAAC,EAAEC,KAAK,IAAI,CAAC;IACjC,IAAI,cAAc,CAACC,IAAI,CAACF,GAAG,CAAC,EAAEC,KAAK,IAAI,CAAC;;IAExC;IACA,IAAID,GAAG,CAACpB,MAAM,IAAI,EAAE,EAAEqB,KAAK,IAAI,CAAC;IAEhC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,iBAAiB;IACxC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,mBAAmB;IAC1C,OAAO,eAAe;EACxB,CAAC;EAED,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQf,QAAQ;MACd,KAAK,iBAAiB;QACpB,OAAO,4BAA4B;MACrC,KAAK,mBAAmB;QACtB,OAAO,6BAA6B;MACtC,KAAK,eAAe;QAClB,OAAO,0BAA0B;MACnC;QACE,OAAO,wBAAwB;IACnC;EACF,CAAC;EAED,MAAMgB,eAAe,GAAGA,CAAA,KAAM;IAC5BC,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC7B,QAAQ,CAAC;IACvC;EACF,CAAC;EAED,MAAM8B,kBAAkB,GAAIC,MAAM,IAAK;IACrC,MAAMC,UAAU,GAAG;MAAE,GAAG5B,OAAO;MAAE,CAAC2B,MAAM,GAAG,CAAC3B,OAAO,CAAC2B,MAAM;IAAE,CAAC;IAC7D1B,UAAU,CAAC2B,UAAU,CAAC;EACxB,CAAC;EAED,MAAMC,kBAAkB,GAAIC,CAAC,IAAK;IAChC,MAAMC,SAAS,GAAGC,QAAQ,CAACF,CAAC,CAACG,MAAM,CAACC,KAAK,CAAC;IAC1CnC,SAAS,CAACgC,SAAS,CAAC;EACtB,CAAC;;EAED;EACA1C,SAAS,CAAC,MAAM;IACdmB,gBAAgB,CAAC,CAAC;IACpB;EACA,CAAC,EAAE,CAACV,MAAM,EAAEE,OAAO,CAAC,CAAC;EAErB,oBACER,OAAA;IAAK2C,SAAS,EAAC,EAAE;IAAAC,QAAA,gBAEf5C,OAAA;MAAI2C,SAAS,EAAC,iDAAiD;MAAAC,QAAA,EAAC;IAEhE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGLhD,OAAA;MAAK2C,SAAS,EAAC,yCAAyC;MAAAC,QAAA,gBAEtD5C,OAAA;QAAK2C,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/C5C,OAAA;UAAK2C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD5C,OAAA;YACEiD,IAAI,EAAC,MAAM;YACXP,KAAK,EAAEtC,QAAS;YAChB8C,QAAQ;YACRP,SAAS,EAAC;UAAmH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9H,CAAC,eACFhD,OAAA;YACEmD,OAAO,EAAEnC,gBAAiB;YAC1B2B,SAAS,EAAC,yGAAyG;YAAAC,QAAA,eAEnH5C,OAAA,CAACF,SAAS;cAAC6C,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNhD,OAAA;UAAK2C,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACvE5C,OAAA;YACE2C,SAAS,EAAE,8CAA8Cd,gBAAgB,CAAC,CAAC,EAAG;YAAAe,QAAA,EAE7E9B;UAAQ;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACPhD,OAAA;YACEmD,OAAO,EAAErB,eAAgB;YACzBa,SAAS,EAAC,yGAAyG;YAAAC,QAAA,EACpH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGdhD,OAAA;QAAK2C,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB5C,OAAA;UAAK2C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAGpD5C,OAAA;YAAK2C,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC5C,OAAA;cAAM2C,SAAS,EAAC,0DAA0D;cAAAC,QAAA,GAAC,mBACxD,EAACtC,MAAM;YAAA;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eAEPhD,OAAA;cACEiD,IAAI,EAAC,OAAO;cACZG,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC,KAAK;cACTX,KAAK,EAAEpC,MAAO;cACdgD,QAAQ,EAAEjB,kBAAmB;cAC7BM,SAAS,EAAC,yEAAyE;cACnFY,KAAK,EAAE;gBACLC,UAAU,EAAE,iDACT,CAAClD,MAAM,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,GAAI,GAAG,cAE/B,CAACA,MAAM,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,GAAI,GAAG;cAEpC;YAAE;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNhD,OAAA;YAAK2C,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAElD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAIEhD,OAAA;QAAK2C,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE5C,OAAA;UAAK2C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5C,OAAA;YAAK2C,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvB5C,OAAA;cACEyD,EAAE,EAAC,WAAW;cACdR,IAAI,EAAC,UAAU;cACfS,OAAO,EAAElD,OAAO,CAACE,SAAU;cAC3B4C,QAAQ,EAAEA,CAAA,KAAMpB,kBAAkB,CAAC,WAAW,CAAE;cAChDS,SAAS,EAAC;YAAiH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5H;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcC,CAAC,eACNhD,OAAA;YACE2D,OAAO,EAAC,WAAW;YACnBhB,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EACrE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENhD,OAAA;UAAK2C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5C,OAAA;YAAK2C,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvB5C,OAAA;cACEyD,EAAE,EAAC,WAAW;cACdR,IAAI,EAAC,UAAU;cACfS,OAAO,EAAElD,OAAO,CAACG,SAAU;cAC3B2C,QAAQ,EAAEA,CAAA,KAAMpB,kBAAkB,CAAC,WAAW,CAAE;cAChDS,SAAS,EAAC;YAAgH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3H;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcC,CAAC,eACNhD,OAAA;YACE2D,OAAO,EAAC,WAAW;YACnBhB,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EACrE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENhD,OAAA;UAAK2C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5C,OAAA;YAAK2C,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvB5C,OAAA;cACEyD,EAAE,EAAC,SAAS;cACZR,IAAI,EAAC,UAAU;cACfS,OAAO,EAAElD,OAAO,CAACI,OAAQ;cACzB0C,QAAQ,EAAEA,CAAA,KAAMpB,kBAAkB,CAAC,SAAS,CAAE;cAC9CS,SAAS,EAAC;YAAiH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5H;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcC,CAAC,eACNhD,OAAA;YACE2D,OAAO,EAAC,SAAS;YACjBhB,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EACrE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENhD,OAAA;UAAK2C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5C,OAAA;YAAK2C,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvB5C,OAAA;cACEyD,EAAE,EAAC,SAAS;cACZR,IAAI,EAAC,UAAU;cACfS,OAAO,EAAElD,OAAO,CAACK,OAAQ;cACzByC,QAAQ,EAAEA,CAAA,KAAMpB,kBAAkB,CAAC,SAAS,CAAE;cAC9CS,SAAS,EAAC;YAAiH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5H;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcC,CAAC,eACNhD,OAAA;YACE2D,OAAO,EAAC,SAAS;YACjBhB,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EACrE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7C,EAAA,CA7SIF,iBAAiB;AAAA2D,EAAA,GAAjB3D,iBAAiB;AA+SvB,eAAeA,iBAAiB;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}