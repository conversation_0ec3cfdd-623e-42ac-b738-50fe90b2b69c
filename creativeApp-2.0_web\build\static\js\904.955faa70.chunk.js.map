{"version": 3, "file": "static/js/904.955faa70.chunk.js", "mappings": "wLAIA,MA2EMA,EAAiBC,IACrB,MAAMC,EAAS,CACbC,QAAS,cACTC,QAAS,gBACTC,MAAO,cACPC,OAAQ,0BAGV,GAAIJ,EAAOD,GACT,OAAOC,EAAOD,GAIhB,MAAMM,EAAe,CACnB,aAAc,gBAAiB,gBAAiB,cAChD,gBAAiB,cAAe,cAAe,eAKjD,OAAOA,EADMN,EAAUO,MAAM,IAAIC,QAAO,CAACC,EAAKC,IAASD,EAAMC,EAAKC,WAAW,IAAI,GACtDL,EAAaM,SAAW,aAAa,EAK5DC,EAAYC,IAAA,IAAC,MAAEC,EAAK,KAAEC,EAAI,cAAEC,EAAa,QAAEC,GAASJ,EAAA,OACxDK,EAAAA,EAAAA,MAAA,OAAKC,UAAW,GAAGF,iFAAuFG,SAAA,EACxGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yBAAwBC,SAAEN,KACxCO,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yBAAwBC,SACpCJ,EAAcM,KAAI,CAACC,EAAMC,KACxBH,EAAAA,EAAAA,KAACI,EAAI,CAAWX,MAAO,SAASS,EAAKG,OAAO,GAAGC,cAAgBJ,EAAKK,MAAM,KAAMC,MAAOd,EAAKQ,IAAS,GAA1FC,SAGX,EAIFC,EAAOK,IAAA,IAAC,MAAEhB,EAAK,MAAEe,GAAOC,EAAA,OAC5BZ,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0EAAyEC,SAAA,EACtFC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,oBAAmBC,SAAEN,KACrCO,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0BAAyBC,SAAES,MACpC,EAGR,EAvHkBE,KAChB,MAAOC,EAAOC,IAAYC,EAAAA,EAAAA,UAAS,KAC5BC,EAAWC,IAAgBF,EAAAA,EAAAA,UAAS,CAAC,IACrClB,EAAeqB,IAAoBH,EAAAA,EAAAA,UAAS,IAAII,KAEjDC,EAAQC,aAAaC,QAAQ,UAC3B1B,KAAM2B,EAAS,MAAEC,IAAUC,EAAAA,EAAAA,GAAgB,GAAGC,EAAAA,SAAgBN,GAmDtE,OAjDAO,EAAAA,EAAAA,YAAU,KACJH,GACFI,QAAQJ,MAAM,aAAcA,EAC9B,GACC,CAACA,KAEJG,EAAAA,EAAAA,YAAU,KACJE,MAAMC,QAAQP,IAAcA,EAAU/B,OAAS,GACjDoC,QAAQG,IAAI,iBAAkBR,GAC9BT,EAASS,IAETK,QAAQI,KAAK,uCACf,GACC,CAACT,KAEJI,EAAAA,EAAAA,YAAU,KACR,IAAKE,MAAMC,QAAQjB,IAA2B,IAAjBA,EAAMrB,OAAc,OAEjD,MAAMyC,EAAc,CAAC,EACfC,EAAsB,IAAIf,IAEhCN,EAAMsB,SAASC,IACRA,EAAKC,WAAcR,MAAMC,QAAQM,EAAKC,YAE3CD,EAAKC,UAAUF,SAASG,IAAc,IAADC,EACnC,MAAM3D,GAAoB,OAAR0D,QAAQ,IAARA,GAAoB,QAAZC,EAARD,EAAUE,kBAAU,IAAAD,OAAZ,EAARA,EAAsBE,gBAAiB,SAEpDR,EAAYrD,KACfqD,EAAYrD,GAAa,CAAC,GAGvBwD,EAAKM,gBAAmBb,MAAMC,QAAQM,EAAKM,iBAEhDN,EAAKM,eAAeP,SAASQ,IAAc,IAADC,EACxC,MAAMC,GAAe,OAARF,QAAQ,IAARA,GAAc,QAANC,EAARD,EAAUG,YAAI,IAAAF,OAAN,EAARA,EAAgBH,gBAAiB,UAC9CP,EAAoBa,IAAIF,GAEnBZ,EAAYrD,GAAWiE,KAC1BZ,EAAYrD,GAAWiE,GAAQ,GAEjCZ,EAAYrD,GAAWiE,IAAO,GAC9B,GACF,IAGJ5B,EAAagB,GACbf,EAAiBgB,EAAoB,GACpC,CAACrB,KAGFX,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sBAAqBC,UAClCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mCAAkCC,SAC9C+C,OAAOC,QAAQjC,GAAWb,KAAI,CAAA+C,EAAoBC,KAAK,IAAvBvE,EAAWgB,GAAKsD,EAAA,OAC/ChD,EAAAA,EAAAA,KAACT,EAAS,CAERE,MAAO,GAAGf,EAAU2B,OAAO,GAAGC,gBAAgB5B,EAAU6B,MAAM,KAC9Db,KAAMA,EACNC,cAAegC,MAAMuB,KAAKvD,GAC1BC,QAASnB,EAAcC,IAJlBuE,EAKL,OAGF,C", "sources": ["pages/teamsnapshot/ShiftArea.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport useFetchApiData from \"../../common/fetchData/useFetchApiData.jsx\";\r\nimport { API_URL } from \"../../common/fetchData/apiConfig.js\";\r\n\r\nconst ShiftArea = () => {\r\n  const [users, setUsers] = useState([]);\r\n  const [shiftData, setShiftData] = useState({});\r\n  const [resourceTypes, setResourceTypes] = useState(new Set());\r\n\r\n  const token = localStorage.getItem(\"token\");\r\n  const { data: usersData, error } = useFetchApiData(`${API_URL}users`, token);\r\n\r\n  useEffect(() => {\r\n    if (error) {\r\n      console.error(\"API Error:\", error);\r\n    }\r\n  }, [error]);\r\n\r\n  useEffect(() => {\r\n    if (Array.isArray(usersData) && usersData.length > 0) {\r\n      console.log(\"Fetched Users:\", usersData);\r\n      setUsers(usersData);\r\n    } else {\r\n      console.warn(\"No valid users found in API response\");\r\n    }\r\n  }, [usersData]);\r\n\r\n  useEffect(() => {\r\n    if (!Array.isArray(users) || users.length === 0) return;\r\n\r\n    const shiftCounts = {};\r\n    const uniqueResourceTypes = new Set(); // Store unique resource types dynamically\r\n\r\n    users.forEach((user) => {\r\n      if (!user.schedules || !Array.isArray(user.schedules)) return;\r\n\r\n      user.schedules.forEach((schedule) => {\r\n        const shiftName = schedule?.shift_name?.toLowerCase() || \"custom\";\r\n\r\n        if (!shiftCounts[shiftName]) {\r\n          shiftCounts[shiftName] = {}; // Dynamically initialize\r\n        }\r\n\r\n        if (!user.resource_types || !Array.isArray(user.resource_types)) return;\r\n\r\n        user.resource_types.forEach((resource) => {\r\n          const role = resource?.name?.toLowerCase() || \"unknown\";\r\n          uniqueResourceTypes.add(role); // Store unique roles dynamically\r\n\r\n          if (!shiftCounts[shiftName][role]) {\r\n            shiftCounts[shiftName][role] = 0; // Initialize role count\r\n          }\r\n          shiftCounts[shiftName][role]++;\r\n        });\r\n      });\r\n    });\r\n\r\n    setShiftData(shiftCounts);\r\n    setResourceTypes(uniqueResourceTypes); // Update state with unique roles\r\n  }, [users]);\r\n\r\n  return (\r\n    <div className=\"p-6 overflow-x-auto\">\r\n      <div className=\"flex gap-6 mb-8 text-start w-max\">\r\n        {Object.entries(shiftData).map(([shiftName, data], index) => (\r\n          <ShiftCard\r\n            key={index}\r\n            title={`${shiftName.charAt(0).toUpperCase()}${shiftName.slice(1)}`}\r\n            data={data}\r\n            resourceTypes={Array.from(resourceTypes)} // Pass dynamic resource types\r\n            bgColor={getShiftColor(shiftName)}\r\n          />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Function to assign colors dynamically based on shift name\r\nconst getShiftColor = (shiftName) => {\r\n  const colors = {\r\n    morning: \"bg-blue-500\",\r\n    evening: \"bg-orange-500\",\r\n    night: \"bg-blue-900\",\r\n    custom: \"bg-gray-200 text-black\",\r\n  };\r\n\r\n  if (colors[shiftName]) {\r\n    return colors[shiftName]; // Return predefined color if exists\r\n  }\r\n\r\n  // Generate a dynamic color for new shift names\r\n  const colorPalette = [\r\n    \"bg-red-500\", \"bg-yellow-500\", \"bg-purple-500\", \"bg-pink-500\",\r\n    \"bg-indigo-500\", \"bg-teal-500\", \"bg-cyan-500\", \"bg-lime-500\"\r\n  ];\r\n\r\n  // Simple hash function to assign colors based on shift name\r\n  const hash = shiftName.split(\"\").reduce((acc, char) => acc + char.charCodeAt(0), 0);\r\n  return colorPalette[hash % colorPalette.length] || \"bg-gray-500\"; // Default color\r\n};\r\n\r\n\r\n// Shift Card Component\r\nconst ShiftCard = ({ title, data, resourceTypes, bgColor }) => (\r\n  <div className={`${bgColor} text-white p-6 rounded-2xl shadow-lg w-[420px] overflow-y-auto flex flex-col`}>\r\n    <h3 className=\"text-xl font-bold mb-4\">{title}</h3>\r\n    <div className=\"grid grid-cols-2 gap-4\">\r\n      {resourceTypes.map((type, idx) => (\r\n        <Card key={idx} title={`Total ${type.charAt(0).toUpperCase() + type.slice(1)}`} count={data[type] || 0} />\r\n      ))}\r\n    </div>\r\n  </div>\r\n);\r\n\r\n// Card Component\r\nconst Card = ({ title, count }) => (\r\n  <div className=\"bg-white text-black p-4 rounded-lg shadow-md flex flex-col items-center\">\r\n    <span className=\"text-sm font-bold\">{title}</span>\r\n    <p className=\"text-2xl font-bold mt-2\">{count}</p>\r\n  </div>\r\n);\r\n\r\nexport default ShiftArea;\r\n"], "names": ["getShiftColor", "shiftName", "colors", "morning", "evening", "night", "custom", "colorPalette", "split", "reduce", "acc", "char", "charCodeAt", "length", "ShiftCard", "_ref2", "title", "data", "resourceTypes", "bgColor", "_jsxs", "className", "children", "_jsx", "map", "type", "idx", "Card", "char<PERSON>t", "toUpperCase", "slice", "count", "_ref3", "ShiftArea", "users", "setUsers", "useState", "shiftData", "setShiftData", "setResourceTypes", "Set", "token", "localStorage", "getItem", "usersData", "error", "useFetchApiData", "API_URL", "useEffect", "console", "Array", "isArray", "log", "warn", "shiftCounts", "uniqueResourceTypes", "for<PERSON>ach", "user", "schedules", "schedule", "_schedule$shift_name", "shift_name", "toLowerCase", "resource_types", "resource", "_resource$name", "role", "name", "add", "Object", "entries", "_ref", "index", "from"], "sourceRoot": ""}