"use strict";(self.webpackChunkcreativeapp=self.webpackChunkcreativeapp||[]).push([[904],{75904:(e,r,t)=>{t.r(r),t.d(r,{default:()=>d});var s=t(65043),a=t(28352),l=t(32029),o=t(70579);const c=e=>{const r={morning:"bg-blue-500",evening:"bg-orange-500",night:"bg-blue-900",custom:"bg-gray-200 text-black"};if(r[e])return r[e];const t=["bg-red-500","bg-yellow-500","bg-purple-500","bg-pink-500","bg-indigo-500","bg-teal-500","bg-cyan-500","bg-lime-500"];return t[e.split("").reduce(((e,r)=>e+r.charCodeAt(0)),0)%t.length]||"bg-gray-500"},n=e=>{let{title:r,data:t,resourceTypes:s,bgColor:a}=e;return(0,o.jsxs)("div",{className:`${a} text-white p-6 rounded-2xl shadow-lg w-[420px] overflow-y-auto flex flex-col`,children:[(0,o.jsx)("h3",{className:"text-xl font-bold mb-4",children:r}),(0,o.jsx)("div",{className:"grid grid-cols-2 gap-4",children:s.map(((e,r)=>(0,o.jsx)(i,{title:`Total ${e.charAt(0).toUpperCase()+e.slice(1)}`,count:t[e]||0},r)))})]})},i=e=>{let{title:r,count:t}=e;return(0,o.jsxs)("div",{className:"bg-white text-black p-4 rounded-lg shadow-md flex flex-col items-center",children:[(0,o.jsx)("span",{className:"text-sm font-bold",children:r}),(0,o.jsx)("p",{className:"text-2xl font-bold mt-2",children:t})]})},d=()=>{const[e,r]=(0,s.useState)([]),[t,i]=(0,s.useState)({}),[d,u]=(0,s.useState)(new Set),g=localStorage.getItem("token"),{data:h,error:p}=(0,a.A)(`${l.H}users`,g);return(0,s.useEffect)((()=>{p&&console.error("API Error:",p)}),[p]),(0,s.useEffect)((()=>{Array.isArray(h)&&h.length>0?(console.log("Fetched Users:",h),r(h)):console.warn("No valid users found in API response")}),[h]),(0,s.useEffect)((()=>{if(!Array.isArray(e)||0===e.length)return;const r={},t=new Set;e.forEach((e=>{e.schedules&&Array.isArray(e.schedules)&&e.schedules.forEach((s=>{var a;const l=(null===s||void 0===s||null===(a=s.shift_name)||void 0===a?void 0:a.toLowerCase())||"custom";r[l]||(r[l]={}),e.resource_types&&Array.isArray(e.resource_types)&&e.resource_types.forEach((e=>{var s;const a=(null===e||void 0===e||null===(s=e.name)||void 0===s?void 0:s.toLowerCase())||"unknown";t.add(a),r[l][a]||(r[l][a]=0),r[l][a]++}))}))})),i(r),u(t)}),[e]),(0,o.jsx)("div",{className:"p-6 overflow-x-auto",children:(0,o.jsx)("div",{className:"flex gap-6 mb-8 text-start w-max",children:Object.entries(t).map(((e,r)=>{let[t,s]=e;return(0,o.jsx)(n,{title:`${t.charAt(0).toUpperCase()}${t.slice(1)}`,data:s,resourceTypes:Array.from(d),bgColor:c(t)},r)}))})})}}}]);
//# sourceMappingURL=904.955faa70.chunk.js.map