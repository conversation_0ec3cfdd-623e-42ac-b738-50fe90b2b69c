{"ast": null, "code": "import React,{useState,useEffect}from'react';// Main component for the Add Password Card form\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AddPasswordCardForm=_ref=>{var _teamsByDepartment$fo;let{onSubmit,onCancel,generatedPassword,passwordStrength}=_ref;// State to hold form data\nconst[formData,setFormData]=useState({title:'',password:'',team:'',department:'',strength:'Weak Password'});// Department and Team options for the select dropdowns\nconst departments=['IT','Development','DevOps','Project Management','HR','Database','Marketing','Sales'];const teamsByDepartment={'IT':['IT Support','System Admin','Network Team'],'Development':['Frontend Team','Backend Team','Full Stack Team'],'DevOps':['Infrastructure Team','CI/CD Team','Cloud Team'],'Project Management':['Scrum Masters','Product Owners','PMO'],'HR':['Recruitment','Employee Relations','Payroll'],'Database':['DBA Team','Data Analytics','Data Engineering'],'Marketing':['Digital Marketing','Content Team','SEO Team'],'Sales':['Inside Sales','Field Sales','Sales Support']};// State for showing/hiding the password, individual field errors, and a general form error\nconst[showPassword,setShowPassword]=useState(false);const[errors,setErrors]=useState({});const[formError,setFormError]=useState('');// **NEW**: State for the main error message\n// Effect to update the form's password field when a new password is generated\nuseEffect(()=>{if(generatedPassword){setFormData(prev=>({...prev,password:generatedPassword,strength:passwordStrength}));}},[generatedPassword,passwordStrength]);// Handles input changes for all form fields\nconst handleInputChange=e=>{const{name,value}=e.target;// Reset team selection when the department changes\nif(name==='department'){setFormData(prev=>({...prev,[name]:value,team:''}));}else{setFormData(prev=>({...prev,[name]:value}));}// Clear the error for a field when the user starts typing\nif(errors[name]){setErrors(prev=>({...prev,[name]:''}));}// Automatically calculate password strength as the user types\nif(name==='password'){const strength=calculatePasswordStrength(value);setFormData(prev=>({...prev,strength:strength}));}};// Calculates the strength of a given password\nconst calculatePasswordStrength=password=>{if(!password)return'Weak Password';let score=0;if(password.length>=12)score+=2;else if(password.length>=8)score+=1;if(/[a-z]/.test(password))score+=1;if(/[A-Z]/.test(password))score+=1;if(/[0-9]/.test(password))score+=1;if(/[^A-Za-z0-9]/.test(password))score+=1;if(password.length>=16)score+=1;if(score>=6)return'Strong Password';if(score>=4)return'Moderate Password';return'Weak Password';};// Returns Tailwind CSS classes based on password strength for styling\nconst getStrengthColor=strength=>{switch(strength){case'Strong Password':return'bg-green-100 text-green-600 border-green-300';case'Moderate Password':return'bg-yellow-100 text-yellow-600 border-yellow-300';case'Weak Password':return'bg-red-100 text-red-600 border-red-300';default:return'bg-gray-100 text-gray-600 border-gray-300';}};// Validates the form fields before submission\nconst validateForm=()=>{const newErrors={};if(!formData.department.trim())newErrors.department='Department is required';if(!formData.team.trim())newErrors.team='Team is required';if(!formData.title.trim())newErrors.title='Title is required';if(!formData.password.trim())newErrors.password='Password is required';setErrors(newErrors);return Object.keys(newErrors).length===0;};// Handles the form submission\nconst handleSubmit=e=>{e.preventDefault();setFormError('');// Clear previous general errors\n// **MODIFICATION**: Check validation and set a general error if it fails\nif(validateForm()){const strengthColor=getStrengthColor(formData.strength);const cardData={...formData,strengthColor};onSubmit(cardData);// This function is passed from the parent component\n// Reset form after successful submission\nsetFormData({title:'',password:'',team:'',department:'',strength:'Weak Password'});}else{// **NEW**: Set a clear error message for the user\nsetFormError('Please fill out all required fields before saving.');}};// Toggles password visibility\nconst togglePasswordVisibility=()=>setShowPassword(!showPassword);// Sets the form password to the generated password\nconst useGeneratedPassword=()=>{if(generatedPassword){setFormData(prev=>({...prev,password:generatedPassword,strength:passwordStrength}));}};return/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-lg shadow-md w-full max-w-4xl relative bg-neutral-50 m-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-4 bg-gray-100 px-4 py-3\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-lg font-medium text-left text-gray-800\",children:\"Add New Password Card\"}),/*#__PURE__*/_jsx(\"button\",{className:\"text-3xl text-gray-500 hover:text-gray-800\",onClick:onCancel,children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-wrap gap-4 p-6 overflow-y-auto max-h-[80vh] scrollbar-vertical\",children:[formError&&/*#__PURE__*/_jsxs(\"div\",{className:\"w-full p-3 mb-2 text-sm text-red-800 rounded-lg bg-red-100 border border-red-300\",role:\"alert\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"Action Required:\"}),\" \",formError]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 w-full md:max-w-[48%] text-left\",children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"department\",className:\"block pb-2 text-base text-gray-600\",children:[\"Department \",/*#__PURE__*/_jsx(\"span\",{className:\"text-red-600\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"select\",{id:\"department\",name:\"department\",value:formData.department,onChange:handleInputChange,className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.department?'border-red-500':'border-gray-300'}`,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Department\"}),departments.map(dept=>/*#__PURE__*/_jsx(\"option\",{value:dept,children:dept},dept))]}),errors.department&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-red-600\",children:errors.department})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 w-full md:max-w-[48%] text-left\",children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"team\",className:\"block pb-2 text-base text-gray-600\",children:[\"Team \",/*#__PURE__*/_jsx(\"span\",{className:\"text-red-600\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"select\",{id:\"team\",name:\"team\",value:formData.team,onChange:handleInputChange,disabled:!formData.department,className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.team?'border-red-500':'border-gray-300'} ${!formData.department?'bg-gray-100 cursor-not-allowed':''}`,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Team\"}),formData.department&&((_teamsByDepartment$fo=teamsByDepartment[formData.department])===null||_teamsByDepartment$fo===void 0?void 0:_teamsByDepartment$fo.map(team=>/*#__PURE__*/_jsx(\"option\",{value:team,children:team},team)))]}),errors.team&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-red-600\",children:errors.team}),!formData.department&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:\"Please select a department first\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 w-full md:max-w-[48%] text-left\",children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"title\",className:\"block pb-2 text-base text-gray-600\",children:[\"Platform/Service Title \",/*#__PURE__*/_jsx(\"span\",{className:\"text-red-600\",children:\"*\"})]}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"title\",name:\"title\",value:formData.title,onChange:handleInputChange,className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.title?'border-red-500':'border-gray-300'}`,placeholder:\"e.g., Gmail, GitHub, AWS Console\"}),errors.title&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-red-600\",children:errors.title})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 w-full md:max-w-[48%] text-left\",children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"password\",className:\"block pb-2 text-base text-gray-600\",children:[\"Password \",/*#__PURE__*/_jsx(\"span\",{className:\"text-red-600\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"input\",{type:showPassword?\"text\":\"password\",id:\"password\",name:\"password\",value:formData.password,onChange:handleInputChange,className:`w-full px-3 py-2 pr-24 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.password?'border-red-500':'border-gray-300'}`,placeholder:\"Enter password\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"absolute inset-y-0 right-0 flex items-center\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:useGeneratedPassword,className:\"px-2 text-xs text-blue-600 hover:text-blue-800 border-r border-gray-300\",title:\"Use generated password from above\",children:\"Use Gen\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:togglePasswordVisibility,className:\"pr-3 pl-2 flex items-center text-gray-400 hover:text-gray-600\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-rounded text-sm\",children:showPassword?'visibility_off':'visibility'})})]})]}),errors.password&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-red-600\",children:errors.password}),formData.password&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-2\",children:/*#__PURE__*/_jsx(\"span\",{className:`inline-block px-3 py-1 text-xs font-medium rounded-full border ${getStrengthColor(formData.strength)}`,children:formData.strength})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-4 p-6 bg-gray-50 border-t\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:onCancel,className:\"px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",children:\"Save Password Card\"})]})]})]})});};export default AddPasswordCardForm;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "AddPasswordCardForm", "_ref", "_teamsByDepartment$fo", "onSubmit", "onCancel", "generatedPassword", "passwordStrength", "formData", "setFormData", "title", "password", "team", "department", "strength", "departments", "teamsByDepartment", "showPassword", "setShowPassword", "errors", "setErrors", "formError", "setFormError", "prev", "handleInputChange", "e", "name", "value", "target", "calculatePasswordStrength", "score", "length", "test", "getStrengthColor", "validateForm", "newErrors", "trim", "Object", "keys", "handleSubmit", "preventDefault", "strengthColor", "cardData", "togglePasswordVisibility", "useGeneratedPassword", "className", "children", "onClick", "role", "htmlFor", "id", "onChange", "map", "dept", "disabled", "type", "placeholder"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/AddPasswordCardForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\n// Main component for the Add Password Card form\nconst AddPasswordCardForm = ({ onSubmit, onCancel, generatedPassword, passwordStrength }) => {\n  // State to hold form data\n  const [formData, setFormData] = useState({\n    title: '',\n    password: '',\n    team: '',\n    department: '',\n    strength: 'Weak Password'\n  });\n\n  // Department and Team options for the select dropdowns\n  const departments = [\n    'IT', 'Development', 'DevOps', 'Project Management', 'HR', 'Database', 'Marketing', 'Sales'\n  ];\n\n  const teamsByDepartment = {\n    'IT': ['IT Support', 'System Admin', 'Network Team'],\n    'Development': ['Frontend Team', 'Backend Team', 'Full Stack Team'],\n    'DevOps': ['Infrastructure Team', 'CI/CD Team', 'Cloud Team'],\n    'Project Management': ['Scrum Masters', 'Product Owners', 'PMO'],\n    'HR': ['Recruitment', 'Employee Relations', 'Payroll'],\n    'Database': ['DBA Team', 'Data Analytics', 'Data Engineering'],\n    'Marketing': ['Digital Marketing', 'Content Team', 'SEO Team'],\n    'Sales': ['Inside Sales', 'Field Sales', 'Sales Support']\n  };\n\n  // State for showing/hiding the password, individual field errors, and a general form error\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [formError, setFormError] = useState(''); // **NEW**: State for the main error message\n\n  // Effect to update the form's password field when a new password is generated\n  useEffect(() => {\n    if (generatedPassword) {\n      setFormData(prev => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength\n      }));\n    }\n  }, [generatedPassword, passwordStrength]);\n\n  // Handles input changes for all form fields\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n\n    // Reset team selection when the department changes\n    if (name === 'department') {\n      setFormData(prev => ({ ...prev, [name]: value, team: '' }));\n    } else {\n      setFormData(prev => ({ ...prev, [name]: value }));\n    }\n\n    // Clear the error for a field when the user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n\n    // Automatically calculate password strength as the user types\n    if (name === 'password') {\n      const strength = calculatePasswordStrength(value);\n      setFormData(prev => ({ ...prev, strength: strength }));\n    }\n  };\n\n  // Calculates the strength of a given password\n  const calculatePasswordStrength = (password) => {\n    if (!password) return 'Weak Password';\n    let score = 0;\n    if (password.length >= 12) score += 2;\n    else if (password.length >= 8) score += 1;\n    if (/[a-z]/.test(password)) score += 1;\n    if (/[A-Z]/.test(password)) score += 1;\n    if (/[0-9]/.test(password)) score += 1;\n    if (/[^A-Za-z0-9]/.test(password)) score += 1;\n    if (password.length >= 16) score += 1;\n    if (score >= 6) return 'Strong Password';\n    if (score >= 4) return 'Moderate Password';\n    return 'Weak Password';\n  };\n\n  // Returns Tailwind CSS classes based on password strength for styling\n  const getStrengthColor = (strength) => {\n    switch (strength) {\n      case 'Strong Password': return 'bg-green-100 text-green-600 border-green-300';\n      case 'Moderate Password': return 'bg-yellow-100 text-yellow-600 border-yellow-300';\n      case 'Weak Password': return 'bg-red-100 text-red-600 border-red-300';\n      default: return 'bg-gray-100 text-gray-600 border-gray-300';\n    }\n  };\n\n  // Validates the form fields before submission\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.department.trim()) newErrors.department = 'Department is required';\n    if (!formData.team.trim()) newErrors.team = 'Team is required';\n    if (!formData.title.trim()) newErrors.title = 'Title is required';\n    if (!formData.password.trim()) newErrors.password = 'Password is required';\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Handles the form submission\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    setFormError(''); // Clear previous general errors\n\n    // **MODIFICATION**: Check validation and set a general error if it fails\n    if (validateForm()) {\n      const strengthColor = getStrengthColor(formData.strength);\n      const cardData = { ...formData, strengthColor };\n      onSubmit(cardData); // This function is passed from the parent component\n      \n      // Reset form after successful submission\n      setFormData({ title: '', password: '', team: '', department: '', strength: 'Weak Password' });\n    } else {\n      // **NEW**: Set a clear error message for the user\n      setFormError('Please fill out all required fields before saving.');\n    }\n  };\n\n  // Toggles password visibility\n  const togglePasswordVisibility = () => setShowPassword(!showPassword);\n\n  // Sets the form password to the generated password\n  const useGeneratedPassword = () => {\n    if (generatedPassword) {\n      setFormData(prev => ({ ...prev, password: generatedPassword, strength: passwordStrength }));\n    }\n  };\n\n  return (\n    <div className=\"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\">\n      <div className=\"rounded-lg shadow-md w-full max-w-4xl relative bg-neutral-50 m-4\">\n        <div className=\"flex justify-between items-center mb-4 bg-gray-100 px-4 py-3\">\n          <h4 className=\"text-lg font-medium text-left text-gray-800\">Add New Password Card</h4>\n          <button className=\"text-3xl text-gray-500 hover:text-gray-800\" onClick={onCancel}>&times;</button>\n        </div>\n\n        <form onSubmit={handleSubmit}>\n          <div className='flex flex-wrap gap-4 p-6 overflow-y-auto max-h-[80vh] scrollbar-vertical'>\n            \n            {/* **NEW**: Display general form error message here */}\n            {formError && (\n              <div className=\"w-full p-3 mb-2 text-sm text-red-800 rounded-lg bg-red-100 border border-red-300\" role=\"alert\">\n                <span className=\"font-medium\">Action Required:</span> {formError}\n              </div>\n            )}\n\n            {/* Department Dropdown */}\n            <div className=\"mb-4 w-full md:max-w-[48%] text-left\">\n              <label htmlFor=\"department\" className=\"block pb-2 text-base text-gray-600\">Department <span className='text-red-600'>*</span></label>\n              <select id=\"department\" name=\"department\" value={formData.department} onChange={handleInputChange} className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.department ? 'border-red-500' : 'border-gray-300'}`}>\n                <option value=\"\">Select Department</option>\n                {departments.map(dept => (<option key={dept} value={dept}>{dept}</option>))}\n              </select>\n              {errors.department && <p className=\"mt-1 text-sm text-red-600\">{errors.department}</p>}\n            </div>\n\n            {/* Team Dropdown */}\n            <div className=\"mb-4 w-full md:max-w-[48%] text-left\">\n              <label htmlFor=\"team\" className=\"block pb-2 text-base text-gray-600\">Team <span className='text-red-600'>*</span></label>\n              <select id=\"team\" name=\"team\" value={formData.team} onChange={handleInputChange} disabled={!formData.department} className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.team ? 'border-red-500' : 'border-gray-300'} ${!formData.department ? 'bg-gray-100 cursor-not-allowed' : ''}`}>\n                <option value=\"\">Select Team</option>\n                {formData.department && teamsByDepartment[formData.department]?.map(team => (<option key={team} value={team}>{team}</option>))}\n              </select>\n              {errors.team && <p className=\"mt-1 text-sm text-red-600\">{errors.team}</p>}\n              {!formData.department && (<p className=\"mt-1 text-sm text-gray-500\">Please select a department first</p>)}\n            </div>\n            \n            {/* Platform/Service Title Input */}\n            <div className=\"mb-4 w-full md:max-w-[48%] text-left\">\n              <label htmlFor=\"title\" className=\"block pb-2 text-base text-gray-600\">Platform/Service Title <span className='text-red-600'>*</span></label>\n              <input type=\"text\" id=\"title\" name=\"title\" value={formData.title} onChange={handleInputChange} className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.title ? 'border-red-500' : 'border-gray-300'}`} placeholder=\"e.g., Gmail, GitHub, AWS Console\" />\n              {errors.title && <p className=\"mt-1 text-sm text-red-600\">{errors.title}</p>}\n            </div>\n\n            {/* Password Input */}\n            <div className=\"mb-4 w-full md:max-w-[48%] text-left\">\n              <label htmlFor=\"password\" className=\"block pb-2 text-base text-gray-600\">Password <span className='text-red-600'>*</span></label>\n              <div className=\"relative\">\n                <input type={showPassword ? \"text\" : \"password\"} id=\"password\" name=\"password\" value={formData.password} onChange={handleInputChange} className={`w-full px-3 py-2 pr-24 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.password ? 'border-red-500' : 'border-gray-300'}`} placeholder=\"Enter password\" />\n                <div className=\"absolute inset-y-0 right-0 flex items-center\">\n                  <button type=\"button\" onClick={useGeneratedPassword} className=\"px-2 text-xs text-blue-600 hover:text-blue-800 border-r border-gray-300\" title=\"Use generated password from above\">Use Gen</button>\n                  <button type=\"button\" onClick={togglePasswordVisibility} className=\"pr-3 pl-2 flex items-center text-gray-400 hover:text-gray-600\">\n                    <span className=\"material-symbols-rounded text-sm\">{showPassword ? 'visibility_off' : 'visibility'}</span>\n                  </button>\n                </div>\n              </div>\n              {errors.password && <p className=\"mt-1 text-sm text-red-600\">{errors.password}</p>}\n              {formData.password && (<div className=\"mt-2\"><span className={`inline-block px-3 py-1 text-xs font-medium rounded-full border ${getStrengthColor(formData.strength)}`}>{formData.strength}</span></div>)}\n            </div>\n          </div>\n\n          {/* Form Action Buttons */}\n          <div className=\"flex justify-end space-x-4 p-6 bg-gray-50 border-t\">\n            <button type=\"button\" onClick={onCancel} className=\"px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\">Cancel</button>\n            <button type=\"submit\" className=\"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">Save Password Card</button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default AddPasswordCardForm;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAElD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,mBAAmB,CAAGC,IAAA,EAAiE,KAAAC,qBAAA,IAAhE,CAAEC,QAAQ,CAAEC,QAAQ,CAAEC,iBAAiB,CAAEC,gBAAiB,CAAC,CAAAL,IAAA,CACtF;AACA,KAAM,CAACM,QAAQ,CAAEC,WAAW,CAAC,CAAGd,QAAQ,CAAC,CACvCe,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,IAAI,CAAE,EAAE,CACRC,UAAU,CAAE,EAAE,CACdC,QAAQ,CAAE,eACZ,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,WAAW,CAAG,CAClB,IAAI,CAAE,aAAa,CAAE,QAAQ,CAAE,oBAAoB,CAAE,IAAI,CAAE,UAAU,CAAE,WAAW,CAAE,OAAO,CAC5F,CAED,KAAM,CAAAC,iBAAiB,CAAG,CACxB,IAAI,CAAE,CAAC,YAAY,CAAE,cAAc,CAAE,cAAc,CAAC,CACpD,aAAa,CAAE,CAAC,eAAe,CAAE,cAAc,CAAE,iBAAiB,CAAC,CACnE,QAAQ,CAAE,CAAC,qBAAqB,CAAE,YAAY,CAAE,YAAY,CAAC,CAC7D,oBAAoB,CAAE,CAAC,eAAe,CAAE,gBAAgB,CAAE,KAAK,CAAC,CAChE,IAAI,CAAE,CAAC,aAAa,CAAE,oBAAoB,CAAE,SAAS,CAAC,CACtD,UAAU,CAAE,CAAC,UAAU,CAAE,gBAAgB,CAAE,kBAAkB,CAAC,CAC9D,WAAW,CAAE,CAAC,mBAAmB,CAAE,cAAc,CAAE,UAAU,CAAC,CAC9D,OAAO,CAAE,CAAC,cAAc,CAAE,aAAa,CAAE,eAAe,CAC1D,CAAC,CAED;AACA,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACwB,MAAM,CAAEC,SAAS,CAAC,CAAGzB,QAAQ,CAAC,CAAC,CAAC,CAAC,CACxC,KAAM,CAAC0B,SAAS,CAAEC,YAAY,CAAC,CAAG3B,QAAQ,CAAC,EAAE,CAAC,CAAE;AAEhD;AACAC,SAAS,CAAC,IAAM,CACd,GAAIU,iBAAiB,CAAE,CACrBG,WAAW,CAACc,IAAI,GAAK,CACnB,GAAGA,IAAI,CACPZ,QAAQ,CAAEL,iBAAiB,CAC3BQ,QAAQ,CAAEP,gBACZ,CAAC,CAAC,CAAC,CACL,CACF,CAAC,CAAE,CAACD,iBAAiB,CAAEC,gBAAgB,CAAC,CAAC,CAEzC;AACA,KAAM,CAAAiB,iBAAiB,CAAIC,CAAC,EAAK,CAC/B,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAEhC;AACA,GAAIF,IAAI,GAAK,YAAY,CAAE,CACzBjB,WAAW,CAACc,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACG,IAAI,EAAGC,KAAK,CAAEf,IAAI,CAAE,EAAG,CAAC,CAAC,CAAC,CAC7D,CAAC,IAAM,CACLH,WAAW,CAACc,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACG,IAAI,EAAGC,KAAM,CAAC,CAAC,CAAC,CACnD,CAEA;AACA,GAAIR,MAAM,CAACO,IAAI,CAAC,CAAE,CAChBN,SAAS,CAACG,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACG,IAAI,EAAG,EAAG,CAAC,CAAC,CAAC,CAC9C,CAEA;AACA,GAAIA,IAAI,GAAK,UAAU,CAAE,CACvB,KAAM,CAAAZ,QAAQ,CAAGe,yBAAyB,CAACF,KAAK,CAAC,CACjDlB,WAAW,CAACc,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAET,QAAQ,CAAEA,QAAS,CAAC,CAAC,CAAC,CACxD,CACF,CAAC,CAED;AACA,KAAM,CAAAe,yBAAyB,CAAIlB,QAAQ,EAAK,CAC9C,GAAI,CAACA,QAAQ,CAAE,MAAO,eAAe,CACrC,GAAI,CAAAmB,KAAK,CAAG,CAAC,CACb,GAAInB,QAAQ,CAACoB,MAAM,EAAI,EAAE,CAAED,KAAK,EAAI,CAAC,CAAC,IACjC,IAAInB,QAAQ,CAACoB,MAAM,EAAI,CAAC,CAAED,KAAK,EAAI,CAAC,CACzC,GAAI,OAAO,CAACE,IAAI,CAACrB,QAAQ,CAAC,CAAEmB,KAAK,EAAI,CAAC,CACtC,GAAI,OAAO,CAACE,IAAI,CAACrB,QAAQ,CAAC,CAAEmB,KAAK,EAAI,CAAC,CACtC,GAAI,OAAO,CAACE,IAAI,CAACrB,QAAQ,CAAC,CAAEmB,KAAK,EAAI,CAAC,CACtC,GAAI,cAAc,CAACE,IAAI,CAACrB,QAAQ,CAAC,CAAEmB,KAAK,EAAI,CAAC,CAC7C,GAAInB,QAAQ,CAACoB,MAAM,EAAI,EAAE,CAAED,KAAK,EAAI,CAAC,CACrC,GAAIA,KAAK,EAAI,CAAC,CAAE,MAAO,iBAAiB,CACxC,GAAIA,KAAK,EAAI,CAAC,CAAE,MAAO,mBAAmB,CAC1C,MAAO,eAAe,CACxB,CAAC,CAED;AACA,KAAM,CAAAG,gBAAgB,CAAInB,QAAQ,EAAK,CACrC,OAAQA,QAAQ,EACd,IAAK,iBAAiB,CAAE,MAAO,8CAA8C,CAC7E,IAAK,mBAAmB,CAAE,MAAO,iDAAiD,CAClF,IAAK,eAAe,CAAE,MAAO,wCAAwC,CACrE,QAAS,MAAO,2CAA2C,CAC7D,CACF,CAAC,CAED;AACA,KAAM,CAAAoB,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,SAAS,CAAG,CAAC,CAAC,CACpB,GAAI,CAAC3B,QAAQ,CAACK,UAAU,CAACuB,IAAI,CAAC,CAAC,CAAED,SAAS,CAACtB,UAAU,CAAG,wBAAwB,CAChF,GAAI,CAACL,QAAQ,CAACI,IAAI,CAACwB,IAAI,CAAC,CAAC,CAAED,SAAS,CAACvB,IAAI,CAAG,kBAAkB,CAC9D,GAAI,CAACJ,QAAQ,CAACE,KAAK,CAAC0B,IAAI,CAAC,CAAC,CAAED,SAAS,CAACzB,KAAK,CAAG,mBAAmB,CACjE,GAAI,CAACF,QAAQ,CAACG,QAAQ,CAACyB,IAAI,CAAC,CAAC,CAAED,SAAS,CAACxB,QAAQ,CAAG,sBAAsB,CAC1ES,SAAS,CAACe,SAAS,CAAC,CACpB,MAAO,CAAAE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACJ,MAAM,GAAK,CAAC,CAC5C,CAAC,CAED;AACA,KAAM,CAAAQ,YAAY,CAAId,CAAC,EAAK,CAC1BA,CAAC,CAACe,cAAc,CAAC,CAAC,CAClBlB,YAAY,CAAC,EAAE,CAAC,CAAE;AAElB;AACA,GAAIY,YAAY,CAAC,CAAC,CAAE,CAClB,KAAM,CAAAO,aAAa,CAAGR,gBAAgB,CAACzB,QAAQ,CAACM,QAAQ,CAAC,CACzD,KAAM,CAAA4B,QAAQ,CAAG,CAAE,GAAGlC,QAAQ,CAAEiC,aAAc,CAAC,CAC/CrC,QAAQ,CAACsC,QAAQ,CAAC,CAAE;AAEpB;AACAjC,WAAW,CAAC,CAAEC,KAAK,CAAE,EAAE,CAAEC,QAAQ,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,QAAQ,CAAE,eAAgB,CAAC,CAAC,CAC/F,CAAC,IAAM,CACL;AACAQ,YAAY,CAAC,oDAAoD,CAAC,CACpE,CACF,CAAC,CAED;AACA,KAAM,CAAAqB,wBAAwB,CAAGA,CAAA,GAAMzB,eAAe,CAAC,CAACD,YAAY,CAAC,CAErE;AACA,KAAM,CAAA2B,oBAAoB,CAAGA,CAAA,GAAM,CACjC,GAAItC,iBAAiB,CAAE,CACrBG,WAAW,CAACc,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEZ,QAAQ,CAAEL,iBAAiB,CAAEQ,QAAQ,CAAEP,gBAAiB,CAAC,CAAC,CAAC,CAC7F,CACF,CAAC,CAED,mBACET,IAAA,QAAK+C,SAAS,CAAC,kHAAkH,CAAAC,QAAA,cAC/H9C,KAAA,QAAK6C,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAC/E9C,KAAA,QAAK6C,SAAS,CAAC,8DAA8D,CAAAC,QAAA,eAC3EhD,IAAA,OAAI+C,SAAS,CAAC,6CAA6C,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cACtFhD,IAAA,WAAQ+C,SAAS,CAAC,4CAA4C,CAACE,OAAO,CAAE1C,QAAS,CAAAyC,QAAA,CAAC,MAAO,CAAQ,CAAC,EAC/F,CAAC,cAEN9C,KAAA,SAAMI,QAAQ,CAAEmC,YAAa,CAAAO,QAAA,eAC3B9C,KAAA,QAAK6C,SAAS,CAAC,0EAA0E,CAAAC,QAAA,EAGtFzB,SAAS,eACRrB,KAAA,QAAK6C,SAAS,CAAC,kFAAkF,CAACG,IAAI,CAAC,OAAO,CAAAF,QAAA,eAC5GhD,IAAA,SAAM+C,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,IAAC,CAACzB,SAAS,EAC7D,CACN,cAGDrB,KAAA,QAAK6C,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACnD9C,KAAA,UAAOiD,OAAO,CAAC,YAAY,CAACJ,SAAS,CAAC,oCAAoC,CAAAC,QAAA,EAAC,aAAW,cAAAhD,IAAA,SAAM+C,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,EAAO,CAAC,cACrI9C,KAAA,WAAQkD,EAAE,CAAC,YAAY,CAACxB,IAAI,CAAC,YAAY,CAACC,KAAK,CAAEnB,QAAQ,CAACK,UAAW,CAACsC,QAAQ,CAAE3B,iBAAkB,CAACqB,SAAS,CAAE,0HAA0H1B,MAAM,CAACN,UAAU,CAAG,gBAAgB,CAAG,iBAAiB,EAAG,CAAAiC,QAAA,eACjShD,IAAA,WAAQ6B,KAAK,CAAC,EAAE,CAAAmB,QAAA,CAAC,mBAAiB,CAAQ,CAAC,CAC1C/B,WAAW,CAACqC,GAAG,CAACC,IAAI,eAAKvD,IAAA,WAAmB6B,KAAK,CAAE0B,IAAK,CAAAP,QAAA,CAAEO,IAAI,EAAxBA,IAAiC,CAAE,CAAC,EACrE,CAAC,CACRlC,MAAM,CAACN,UAAU,eAAIf,IAAA,MAAG+C,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAE3B,MAAM,CAACN,UAAU,CAAI,CAAC,EACnF,CAAC,cAGNb,KAAA,QAAK6C,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACnD9C,KAAA,UAAOiD,OAAO,CAAC,MAAM,CAACJ,SAAS,CAAC,oCAAoC,CAAAC,QAAA,EAAC,OAAK,cAAAhD,IAAA,SAAM+C,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,EAAO,CAAC,cACzH9C,KAAA,WAAQkD,EAAE,CAAC,MAAM,CAACxB,IAAI,CAAC,MAAM,CAACC,KAAK,CAAEnB,QAAQ,CAACI,IAAK,CAACuC,QAAQ,CAAE3B,iBAAkB,CAAC8B,QAAQ,CAAE,CAAC9C,QAAQ,CAACK,UAAW,CAACgC,SAAS,CAAE,0HAA0H1B,MAAM,CAACP,IAAI,CAAG,gBAAgB,CAAG,iBAAiB,IAAI,CAACJ,QAAQ,CAACK,UAAU,CAAG,gCAAgC,CAAG,EAAE,EAAG,CAAAiC,QAAA,eACzWhD,IAAA,WAAQ6B,KAAK,CAAC,EAAE,CAAAmB,QAAA,CAAC,aAAW,CAAQ,CAAC,CACpCtC,QAAQ,CAACK,UAAU,IAAAV,qBAAA,CAAIa,iBAAiB,CAACR,QAAQ,CAACK,UAAU,CAAC,UAAAV,qBAAA,iBAAtCA,qBAAA,CAAwCiD,GAAG,CAACxC,IAAI,eAAKd,IAAA,WAAmB6B,KAAK,CAAEf,IAAK,CAAAkC,QAAA,CAAElC,IAAI,EAAxBA,IAAiC,CAAE,CAAC,GACxH,CAAC,CACRO,MAAM,CAACP,IAAI,eAAId,IAAA,MAAG+C,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAE3B,MAAM,CAACP,IAAI,CAAI,CAAC,CACzE,CAACJ,QAAQ,CAACK,UAAU,eAAKf,IAAA,MAAG+C,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,kCAAgC,CAAG,CAAE,EACtG,CAAC,cAGN9C,KAAA,QAAK6C,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACnD9C,KAAA,UAAOiD,OAAO,CAAC,OAAO,CAACJ,SAAS,CAAC,oCAAoC,CAAAC,QAAA,EAAC,yBAAuB,cAAAhD,IAAA,SAAM+C,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,EAAO,CAAC,cAC5IhD,IAAA,UAAOyD,IAAI,CAAC,MAAM,CAACL,EAAE,CAAC,OAAO,CAACxB,IAAI,CAAC,OAAO,CAACC,KAAK,CAAEnB,QAAQ,CAACE,KAAM,CAACyC,QAAQ,CAAE3B,iBAAkB,CAACqB,SAAS,CAAE,0HAA0H1B,MAAM,CAACT,KAAK,CAAG,gBAAgB,CAAG,iBAAiB,EAAG,CAAC8C,WAAW,CAAC,kCAAkC,CAAE,CAAC,CAC3UrC,MAAM,CAACT,KAAK,eAAIZ,IAAA,MAAG+C,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAE3B,MAAM,CAACT,KAAK,CAAI,CAAC,EACzE,CAAC,cAGNV,KAAA,QAAK6C,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACnD9C,KAAA,UAAOiD,OAAO,CAAC,UAAU,CAACJ,SAAS,CAAC,oCAAoC,CAAAC,QAAA,EAAC,WAAS,cAAAhD,IAAA,SAAM+C,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,EAAO,CAAC,cACjI9C,KAAA,QAAK6C,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBhD,IAAA,UAAOyD,IAAI,CAAEtC,YAAY,CAAG,MAAM,CAAG,UAAW,CAACiC,EAAE,CAAC,UAAU,CAACxB,IAAI,CAAC,UAAU,CAACC,KAAK,CAAEnB,QAAQ,CAACG,QAAS,CAACwC,QAAQ,CAAE3B,iBAAkB,CAACqB,SAAS,CAAE,gIAAgI1B,MAAM,CAACR,QAAQ,CAAG,gBAAgB,CAAG,iBAAiB,EAAG,CAAC6C,WAAW,CAAC,gBAAgB,CAAE,CAAC,cAC1WxD,KAAA,QAAK6C,SAAS,CAAC,8CAA8C,CAAAC,QAAA,eAC3DhD,IAAA,WAAQyD,IAAI,CAAC,QAAQ,CAACR,OAAO,CAAEH,oBAAqB,CAACC,SAAS,CAAC,yEAAyE,CAACnC,KAAK,CAAC,mCAAmC,CAAAoC,QAAA,CAAC,SAAO,CAAQ,CAAC,cACnMhD,IAAA,WAAQyD,IAAI,CAAC,QAAQ,CAACR,OAAO,CAAEJ,wBAAyB,CAACE,SAAS,CAAC,+DAA+D,CAAAC,QAAA,cAChIhD,IAAA,SAAM+C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAE7B,YAAY,CAAG,gBAAgB,CAAG,YAAY,CAAO,CAAC,CACpG,CAAC,EACN,CAAC,EACH,CAAC,CACLE,MAAM,CAACR,QAAQ,eAAIb,IAAA,MAAG+C,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAE3B,MAAM,CAACR,QAAQ,CAAI,CAAC,CACjFH,QAAQ,CAACG,QAAQ,eAAKb,IAAA,QAAK+C,SAAS,CAAC,MAAM,CAAAC,QAAA,cAAChD,IAAA,SAAM+C,SAAS,CAAE,kEAAkEZ,gBAAgB,CAACzB,QAAQ,CAACM,QAAQ,CAAC,EAAG,CAAAgC,QAAA,CAAEtC,QAAQ,CAACM,QAAQ,CAAO,CAAC,CAAK,CAAE,EACrM,CAAC,EACH,CAAC,cAGNd,KAAA,QAAK6C,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjEhD,IAAA,WAAQyD,IAAI,CAAC,QAAQ,CAACR,OAAO,CAAE1C,QAAS,CAACwC,SAAS,CAAC,wKAAwK,CAAAC,QAAA,CAAC,QAAM,CAAQ,CAAC,cAC3OhD,IAAA,WAAQyD,IAAI,CAAC,QAAQ,CAACV,SAAS,CAAC,qKAAqK,CAAAC,QAAA,CAAC,oBAAkB,CAAQ,CAAC,EAC9N,CAAC,EACF,CAAC,EACJ,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA7C,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}