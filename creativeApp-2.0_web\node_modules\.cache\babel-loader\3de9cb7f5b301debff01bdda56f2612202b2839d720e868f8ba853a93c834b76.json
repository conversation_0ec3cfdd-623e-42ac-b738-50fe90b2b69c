{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\components\\\\password-manager\\\\PasswordCardsTable.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport AddPasswordCardForm from \"./AddPasswordCardForm\";\nimport { confirmationAlert } from \"../../common/coreui\";\nimport { useDeleteUserDataMutation } from \"../../features/api/userDataApi\";\nimport FetchLoggedInRole from \"../../common/fetchData/FetchLoggedInRole\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PasswordCardsTable = ({\n  generatedPassword,\n  passwordStrength\n}) => {\n  _s();\n  // Sample data - in real app this would come from API/state management\n  const [passwordCards, setPasswordCards] = useState([{\n    id: 1,\n    title: \"Gmail Account\",\n    platform: \"Gmail\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn\",\n    team: \"Team Name\",\n    department: \"IT\",\n    strength: \"Weak Password\",\n    strengthColor: \"bg-red-100 text-red-600 border-red-300\"\n  }, {\n    id: 2,\n    title: \"Slack Workspace\",\n    platform: \"Slack\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"StrongPass123!@#\",\n    team: \"Team Name\",\n    department: \"IT\",\n    strength: \"Strong Password\",\n    strengthColor: \"bg-green-100 text-green-600 border-green-300\"\n  }, {\n    id: 3,\n    title: \"GitHub Repository\",\n    platform: \"GitHub\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"ModeratePass456\",\n    team: \"Team Name\",\n    department: \"Development\",\n    strength: \"Moderate Password\",\n    strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\"\n  }, {\n    id: 4,\n    title: \"AWS Console\",\n    platform: \"AWS\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"WeakPass\",\n    team: \"Team Name\",\n    department: \"DevOps\",\n    strength: \"Weak Password\",\n    strengthColor: \"bg-red-100 text-red-600 border-red-300\"\n  }, {\n    id: 5,\n    title: \"Jira Project\",\n    platform: \"Jira\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"AnotherStrongPass789!\",\n    team: \"Team Name\",\n    department: \"Project Management\",\n    strength: \"Strong Password\",\n    strengthColor: \"bg-green-100 text-green-600 border-green-300\"\n  }, {\n    id: 6,\n    title: \"Office 365\",\n    platform: \"Microsoft 365\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"ModerateSecure123\",\n    team: \"Team Name\",\n    department: \"HR\",\n    strength: \"Moderate Password\",\n    strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\"\n  }, {\n    id: 7,\n    title: \"Database Admin\",\n    platform: \"MySQL\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"VeryWeakPass\",\n    team: \"Team Name\",\n    department: \"Database\",\n    strength: \"Weak Password\",\n    strengthColor: \"bg-red-100 text-red-600 border-red-300\"\n  }]);\n  const [visiblePasswords, setVisiblePasswords] = useState({});\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [showNewTable, setShowNewTable] = useState(false);\n  const [showTeamTable, setShowTeamTable] = useState(true);\n  const [newPasswordCards, setNewPasswordCards] = useState([]);\n  const [editingRowId, setEditingRowId] = useState(null);\n  const [shareableCards, setShareableCards] = useState([]);\n  const [sortConfig, setSortConfig] = useState({\n    key: null,\n    direction: \"asc\"\n  });\n  const [deleteUserData] = useDeleteUserDataMutation();\n  const [viewData, setViewData] = useState(null);\n  const togglePasswordVisibility = id => {\n    setVisiblePasswords(prev => ({\n      ...prev,\n      [id]: !prev[id]\n    }));\n  };\n  const copyToClipboard = text => {\n    navigator.clipboard.writeText(text);\n    // You could add a toast notification here\n  };\n\n  // Handle individual row editing\n  const handleEdit = id => {\n    setEditingRowId(editingRowId === id ? null : id);\n  };\n\n  // Handle creating new table\n  const handleCreateNewTable = () => {\n    setShowNewTable(true);\n  };\n\n  // Handle delete entire team table\n  // const handleDeleteTeamTable = () => {\n  //   if (\n  //     window.confirm(\n  //       \"Are you sure you want to delete the entire Team Password Card table?\"\n  //     )\n  //   ) {\n  //     setShowTeamTable(false);\n  //   }\n  // };\n  const handleDeleteTeamTable = () => {\n    confirmationAlert({\n      onConfirm: () => {\n        // deleteUsersData();\n        setPasswordCards([]);\n        // setViewData(null);\n      }\n    });\n  };\n\n  // Handle select all checkboxes password and\n\n  const toggleSelectAll = () => {\n    if (shareableCards.length === passwordCards.length) {\n      setShareableCards([]);\n    } else {\n      setShareableCards(passwordCards.map(card => card.id));\n    }\n  };\n\n  // Handle sorting\n  const handleSort = key => {\n    let direction = \"asc\";\n    if (sortConfig.key === key && sortConfig.direction === \"asc\") {\n      direction = \"desc\";\n    }\n    setSortConfig({\n      key,\n      direction\n    });\n  };\n\n  // Sort cards based on current sort config\n  const getSortedCards = cards => {\n    if (!sortConfig.key) return cards;\n    return [...cards].sort((a, b) => {\n      const aValue = a[sortConfig.key].toLowerCase();\n      const bValue = b[sortConfig.key].toLowerCase();\n      if (sortConfig.direction === \"asc\") {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n  };\n\n  // Handle shareable toggle\n  const toggleShareable = id => {\n    setShareableCards(prev => prev.includes(id) ? prev.filter(cardId => cardId !== id) : [...prev, id]);\n  };\n\n  // Handle form submission for new password cards\n  const handleAddPasswordCard = cardData => {\n    const newCard = {\n      ...cardData,\n      id: Date.now(),\n      password: \"••••••••••••\",\n      actualPassword: cardData.password\n    };\n    setNewPasswordCards(prev => [...prev, newCard]);\n    setShowAddForm(false);\n  };\n  const handleDelete = id => {\n    console.log(\"Delete password card:\", id);\n    // Show confirmation dialog and delete\n    setPasswordCards(prev => prev.filter(card => card.id !== id));\n  };\n\n  // Handle share functionality for department\n  const handleShare = () => {\n    // Get current user's department or team\n    const currentUserDepartment = \"IT\"; // This should come from user context/auth\n\n    // Filter cards that belong to the same department\n    const departmentCards = passwordCards.filter(card => card.department === currentUserDepartment);\n    if (departmentCards.length === 0) {\n      alert(\"No password cards available to share in your department.\");\n      return;\n    }\n\n    // Create shareable data\n    const shareData = {\n      title: `${currentUserDepartment} Department Password Cards`,\n      cards: departmentCards,\n      sharedBy: \"Current User\",\n      // This should come from user context\n      sharedAt: new Date().toISOString()\n    };\n\n    // For now, copy to clipboard (you can implement actual sharing logic)\n    navigator.clipboard.writeText(JSON.stringify(shareData, null, 2)).then(() => {\n      alert(`${departmentCards.length} password cards from ${currentUserDepartment} department copied to clipboard!`);\n    }).catch(() => {\n      alert(\"Failed to copy to clipboard. Please try again.\");\n    });\n  };\n\n  // Placeholder avatar images\n  const avatarImages = [\"https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=A\", \"https://via.placeholder.com/32x32/10B981/FFFFFF?text=B\", \"https://via.placeholder.com/32x32/F59E0B/FFFFFF?text=C\", \"https://via.placeholder.com/32x32/EF4444/FFFFFF?text=D\"];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white dark:bg-gray-900\",\n    children: [showTeamTable && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-left text-2xl font-bold text-gray-900 dark:text-white\",\n            children: \"Teams Password Card\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleShare,\n            className: \"flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-rounded\",\n              children: \"share\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleDeleteTeamTable(),\n            className: \"flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg\",\n            title: \"Delete entire table\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-outlined text-sm\",\n              children: \"delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex -space-x-2\",\n            children: avatarImages.map((avatar, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n              src: avatar,\n              alt: `User ${index + 1}`,\n              className: \"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setEditingRowId(null);\n              setShowAddForm(!showAddForm);\n            },\n            className: `w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${showAddForm ? \"bg-primary text-white\" : \"bg-transparent text-primary border-2 border-primary\"}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-rounded mr-2\",\n              children: \"add\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this), \"Add Password\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this), showAddForm && /*#__PURE__*/_jsxDEV(AddPasswordCardForm, {\n        onSubmit: handleAddPasswordCard,\n        onCancel: () => {\n          setShowAddForm(false);\n          setEditingRowId(null);\n        },\n        generatedPassword: generatedPassword,\n        passwordStrength: passwordStrength\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"w-12 px-4 py-3 text-left\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"checkbox-all\",\n                  type: \"checkbox\",\n                  checked: shareableCards.length === passwordCards.length && passwordCards.length > 0,\n                  onChange: toggleSelectAll,\n                  className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  onClick: () => handleSort(\"title\"),\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Title\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"title\" && sortConfig.direction === \"asc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25B2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"title\" && sortConfig.direction === \"desc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25BC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  onClick: () => handleSort(\"username\"),\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"User Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"username\" && sortConfig.direction === \"asc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25B2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 383,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"username\" && sortConfig.direction === \"desc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25BC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 393,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Department\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"divide-y divide-gray-200\",\n            children: getSortedCards(passwordCards).map(card => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: `shareable-${card.id}`,\n                  type: \"checkbox\",\n                  checked: shareableCards.includes(card.id),\n                  onChange: () => toggleShareable(card.id),\n                  className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\",\n                    children: \"TG\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 25\n                  }, this), editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    defaultValue: card.title,\n                    className: \"font-medium text-gray-900 border rounded px-2 py-1\",\n                    onBlur: e => {\n                      setPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                        ...c,\n                        title: e.target.value\n                      } : c));\n                      setEditingRowId(null);\n                    },\n                    onKeyDown: e => {\n                      if (e.key === \"Enter\") {\n                        e.target.blur();\n                      }\n                    },\n                    autoFocus: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: card.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    defaultValue: card.username,\n                    className: \"text-gray-900 border rounded px-2 py-1\",\n                    onBlur: e => {\n                      setPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                        ...c,\n                        username: e.target.value\n                      } : c));\n                    },\n                    onKeyDown: e => {\n                      if (e.key === \"Enter\") {\n                        e.target.blur();\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900\",\n                    children: card.username\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => copyToClipboard(card.username),\n                    className: \"ml-2 text-gray-400 hover:text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"content_copy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 498,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900 mr-2\",\n                    children: visiblePasswords[card.id] ? card.actualPassword : card.password\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => togglePasswordVisibility(card.id),\n                    className: \"text-gray-400 hover:text-gray-600 mr-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: visiblePasswords[card.id] ? \"visibility_off\" : \"visibility\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 515,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => copyToClipboard(card.actualPassword),\n                    className: \"text-gray-400 hover:text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"content_copy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 525,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 text-gray-900\",\n                children: card.team\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 text-gray-900\",\n                children: card.department\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`,\n                  children: card.strength\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(card.id),\n                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                    title: \"Edit\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"25\",\n                      height: \"24\",\n                      viewBox: \"0 0 25 24\",\n                      fill: \"none\",\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"mask\", {\n                        id: `mask1_${card.id}`,\n                        style: {\n                          maskType: \"alpha\"\n                        },\n                        maskUnits: \"userSpaceOnUse\",\n                        x: \"0\",\n                        y: \"0\",\n                        width: \"25\",\n                        height: \"24\",\n                        children: /*#__PURE__*/_jsxDEV(\"rect\", {\n                          x: \"0.5\",\n                          width: \"24\",\n                          height: \"24\",\n                          fill: \"#D9D9D9\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 565,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 556,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"g\", {\n                        mask: `url(#mask1_${card.id})`,\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12.9868 16.3213L14.4068 14.9013L7.50684 8.00134L6.08684 9.42134L12.9868 16.3213ZM15.8268 13.4813L17.2468 12.0613L15.8268 10.6413L14.4068 12.0613L15.8268 13.4813ZM5.50684 19.0013H8.92684L18.6668 9.26134C18.8441 9.08401 18.9441 8.84401 18.9441 8.59134C18.9441 8.33868 18.8441 8.09868 18.6668 7.92134L16.5868 5.84134C16.4095 5.66401 16.1695 5.56401 15.9168 5.56401C15.6641 5.56401 15.4241 5.66401 15.2468 5.84134L5.50684 15.5813V19.0013Z\",\n                          fill: \"currentColor\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 573,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 572,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 549,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 544,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(card.id),\n                    className: \"text-gray-400 hover:text-red-600 transition-colors\",\n                    title: \"Delete\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"25\",\n                      height: \"24\",\n                      viewBox: \"0 0 25 24\",\n                      fill: \"none\",\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"mask\", {\n                        id: `mask2_${card.id}`,\n                        style: {\n                          maskType: \"alpha\"\n                        },\n                        maskUnits: \"userSpaceOnUse\",\n                        x: \"0\",\n                        y: \"0\",\n                        width: \"25\",\n                        height: \"24\",\n                        children: /*#__PURE__*/_jsxDEV(\"rect\", {\n                          x: \"0.5\",\n                          width: \"24\",\n                          height: \"24\",\n                          fill: \"#D9D9D9\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 601,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 592,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"g\", {\n                        mask: `url(#mask2_${card.id})`,\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M8.60658 19.8997C8.15658 19.8997 7.77325 19.7414 7.45658 19.4247C7.13992 19.1081 6.98158 18.7247 6.98158 18.2747V6.89974H5.98158V4.89974H10.9816V3.89974H14.9816V4.89974H19.9816V6.89974H18.9816V18.2747C18.9816 18.7247 18.8232 19.1081 18.5066 19.4247C18.1899 19.7414 17.8066 19.8997 17.3566 19.8997H8.60658ZM16.9816 6.89974H8.98158V18.2747C8.98158 18.3581 9.01492 18.4331 9.08158 18.4997C9.14825 18.5664 9.22325 18.5997 9.30658 18.5997H16.6566C16.7399 18.5997 16.8149 18.5664 16.8816 18.4997C16.9482 18.4331 16.9816 18.3581 16.9816 18.2747V6.89974ZM10.9816 16.8997H12.9816V8.59974H10.9816V16.8997ZM13.9816 16.8997H15.9816V8.59974H13.9816V16.8997Z\",\n                          fill: \"currentColor\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 609,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 608,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 585,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 21\n              }, this)]\n            }, card.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), showNewTable && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-bold text-gray-900 mb-4\",\n        children: \"New Password Cards\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 629,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"w-12 px-4 py-3 text-left\",\n                children: \"Shareable\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  onClick: () => handleSort(\"title\"),\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Title\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 642,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"title\" && sortConfig.direction === \"asc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25B2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 644,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"title\" && sortConfig.direction === \"desc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25BC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 654,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 643,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  onClick: () => handleSort(\"username\"),\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"User Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"username\" && sortConfig.direction === \"asc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25B2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 674,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"username\" && sortConfig.direction === \"desc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25BC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 684,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 673,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Department\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"divide-y divide-gray-200\",\n            children: newPasswordCards.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"8\",\n                className: \"px-6 py-8 text-center text-gray-500\",\n                children: \"No password cards added yet. Click \\\"Add Password\\\" to add your first card.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 19\n            }, this) : getSortedCards(newPasswordCards).map(card => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: shareableCards.includes(card.id),\n                  onChange: () => toggleShareable(card.id),\n                  className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\",\n                    children: \"TG\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: card.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 741,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 text-gray-900\",\n                children: card.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900 mr-2\",\n                    children: card.password\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 751,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => copyToClipboard(card.actualPassword),\n                    className: \"text-gray-400 hover:text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"content_copy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 758,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 754,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 750,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 text-gray-900\",\n                children: card.team\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 764,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 text-gray-900\",\n                children: card.department\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 765,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`,\n                  children: card.strength\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(card.id),\n                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                    title: \"Edit\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"edit\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 782,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 777,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setNewPasswordCards(prev => prev.filter(c => c.id !== card.id)),\n                    className: \"text-gray-400 hover:text-red-600 transition-colors\",\n                    title: \"Delete\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"delete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 795,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 786,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 23\n              }, this)]\n            }, card.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 632,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 628,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center mt-6\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleCreateNewTable,\n        className: \"flex items-center justify-center px-6 py-2 text-sm font-medium text-white rounded-lg bg-primary hover:bg-primary/90 focus:ring-4 focus:ring-primary/30 transition-colors duration-200 focus:outline-none\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-rounded mr-2\",\n          children: \"add\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 816,\n          columnNumber: 11\n        }, this), \"Add New Password Card\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 812,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 811,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 254,\n    columnNumber: 5\n  }, this);\n};\n_s(PasswordCardsTable, \"x5Bmc2rloTKDLNIUWRHfp2RV4hA=\", false, function () {\n  return [useDeleteUserDataMutation];\n});\n_c = PasswordCardsTable;\nexport default PasswordCardsTable;\nvar _c;\n$RefreshReg$(_c, \"PasswordCardsTable\");", "map": {"version": 3, "names": ["React", "useState", "AddPasswordCardForm", "<PERSON><PERSON><PERSON><PERSON>", "useDeleteUserDataMutation", "FetchLoggedInRole", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PasswordCardsTable", "generatedPassword", "passwordStrength", "_s", "passwordCards", "setPasswordCards", "id", "title", "platform", "username", "password", "actualPassword", "team", "department", "strength", "strengthColor", "visiblePasswords", "setVisiblePasswords", "showAddForm", "setShowAddForm", "showNewTable", "setShowNewTable", "showTeamTable", "setShowTeamTable", "newPasswordCards", "setNewPasswordCards", "editingRowId", "setEditingRowId", "shareableCards", "setShareableCards", "sortConfig", "setSortConfig", "key", "direction", "deleteUserData", "viewData", "setViewData", "togglePasswordVisibility", "prev", "copyToClipboard", "text", "navigator", "clipboard", "writeText", "handleEdit", "handleCreateNewTable", "handleDeleteTeamTable", "onConfirm", "toggleSelectAll", "length", "map", "card", "handleSort", "getSortedCards", "cards", "sort", "a", "b", "aValue", "toLowerCase", "bValue", "toggleShareable", "includes", "filter", "cardId", "handleAddPasswordCard", "cardData", "newCard", "Date", "now", "handleDelete", "console", "log", "handleShare", "currentUserDepartment", "departmentCards", "alert", "shareData", "sharedBy", "sharedAt", "toISOString", "JSON", "stringify", "then", "catch", "avatarImages", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "avatar", "index", "src", "alt", "onSubmit", "onCancel", "type", "checked", "onChange", "defaultValue", "onBlur", "e", "c", "target", "value", "onKeyDown", "blur", "autoFocus", "width", "height", "viewBox", "fill", "xmlns", "style", "maskType", "maskUnits", "x", "y", "mask", "d", "colSpan", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/PasswordCardsTable.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport AddPasswordCardForm from \"./AddPasswordCardForm\";\nimport { confirmationAlert } from \"../../common/coreui\";\nimport { useDeleteUserDataMutation } from \"../../features/api/userDataApi\";\nimport FetchLoggedInRole from \"../../common/fetchData/FetchLoggedInRole\";\n\nconst PasswordCardsTable = ({ generatedPassword, passwordStrength }) => {\n  // Sample data - in real app this would come from API/state management\n  const [passwordCards, setPasswordCards] = useState([\n    {\n      id: 1,\n      title: \"Gmail Account\",\n      platform: \"Gmail\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn\",\n      team: \"Team Name\",\n      department: \"IT\",\n      strength: \"Weak Password\",\n      strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n    },\n    {\n      id: 2,\n      title: \"Slack Workspace\",\n      platform: \"Slack\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"StrongPass123!@#\",\n      team: \"Team Name\",\n      department: \"IT\",\n      strength: \"Strong Password\",\n      strengthColor: \"bg-green-100 text-green-600 border-green-300\",\n    },\n    {\n      id: 3,\n      title: \"GitHub Repository\",\n      platform: \"GitHub\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"ModeratePass456\",\n      team: \"Team Name\",\n      department: \"Development\",\n      strength: \"Moderate Password\",\n      strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\",\n    },\n    {\n      id: 4,\n      title: \"AWS Console\",\n      platform: \"AWS\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"WeakPass\",\n      team: \"Team Name\",\n      department: \"DevOps\",\n      strength: \"Weak Password\",\n      strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n    },\n    {\n      id: 5,\n      title: \"Jira Project\",\n      platform: \"Jira\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"AnotherStrongPass789!\",\n      team: \"Team Name\",\n      department: \"Project Management\",\n      strength: \"Strong Password\",\n      strengthColor: \"bg-green-100 text-green-600 border-green-300\",\n    },\n    {\n      id: 6,\n      title: \"Office 365\",\n      platform: \"Microsoft 365\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"ModerateSecure123\",\n      team: \"Team Name\",\n      department: \"HR\",\n      strength: \"Moderate Password\",\n      strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\",\n    },\n    {\n      id: 7,\n      title: \"Database Admin\",\n      platform: \"MySQL\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"VeryWeakPass\",\n      team: \"Team Name\",\n      department: \"Database\",\n      strength: \"Weak Password\",\n      strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n    },\n  ]);\n\n  const [visiblePasswords, setVisiblePasswords] = useState({});\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [showNewTable, setShowNewTable] = useState(false);\n  const [showTeamTable, setShowTeamTable] = useState(true);\n  const [newPasswordCards, setNewPasswordCards] = useState([]);\n  const [editingRowId, setEditingRowId] = useState(null);\n  const [shareableCards, setShareableCards] = useState([]);\n  const [sortConfig, setSortConfig] = useState({ key: null, direction: \"asc\" });\n\n  const [deleteUserData] = useDeleteUserDataMutation();\n  const [viewData, setViewData] = useState(null);\n  const togglePasswordVisibility = (id) => {\n    setVisiblePasswords((prev) => ({\n      ...prev,\n      [id]: !prev[id],\n    }));\n  };\n\n  const copyToClipboard = (text) => {\n    navigator.clipboard.writeText(text);\n    // You could add a toast notification here\n  };\n\n  // Handle individual row editing\n  const handleEdit = (id) => {\n    setEditingRowId(editingRowId === id ? null : id);\n  };\n\n  // Handle creating new table\n  const handleCreateNewTable = () => {\n    setShowNewTable(true);\n  };\n\n  // Handle delete entire team table\n  // const handleDeleteTeamTable = () => {\n  //   if (\n  //     window.confirm(\n  //       \"Are you sure you want to delete the entire Team Password Card table?\"\n  //     )\n  //   ) {\n  //     setShowTeamTable(false);\n  //   }\n  // };\n  const handleDeleteTeamTable = () => {\n    confirmationAlert({\n      onConfirm: () => {\n        // deleteUsersData();\n        setPasswordCards([]);\n        // setViewData(null);\n      },\n    });\n  };\n\n  // Handle select all checkboxes password and\n  \n  const toggleSelectAll = () => {\n    if (shareableCards.length === passwordCards.length) {\n      setShareableCards([]);\n    } else {\n      setShareableCards(passwordCards.map((card) => card.id));\n    }\n  };\n\n  // Handle sorting\n  const handleSort = (key) => {\n    let direction = \"asc\";\n    if (sortConfig.key === key && sortConfig.direction === \"asc\") {\n      direction = \"desc\";\n    }\n    setSortConfig({ key, direction });\n  };\n\n  // Sort cards based on current sort config\n  const getSortedCards = (cards) => {\n    if (!sortConfig.key) return cards;\n\n    return [...cards].sort((a, b) => {\n      const aValue = a[sortConfig.key].toLowerCase();\n      const bValue = b[sortConfig.key].toLowerCase();\n\n      if (sortConfig.direction === \"asc\") {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n  };\n\n  // Handle shareable toggle\n  const toggleShareable = (id) => {\n    setShareableCards((prev) =>\n      prev.includes(id) ? prev.filter((cardId) => cardId !== id) : [...prev, id]\n    );\n  };\n\n  // Handle form submission for new password cards\n  const handleAddPasswordCard = (cardData) => {\n    const newCard = {\n      ...cardData,\n      id: Date.now(),\n      password: \"••••••••••••\",\n      actualPassword: cardData.password,\n    };\n    setNewPasswordCards((prev) => [...prev, newCard]);\n    setShowAddForm(false);\n  };\n\n  const handleDelete = (id) => {\n    console.log(\"Delete password card:\", id);\n    // Show confirmation dialog and delete\n    setPasswordCards((prev) => prev.filter((card) => card.id !== id));\n  };\n\n  // Handle share functionality for department\n  const handleShare = () => {\n    // Get current user's department or team\n    const currentUserDepartment = \"IT\"; // This should come from user context/auth\n\n    // Filter cards that belong to the same department\n    const departmentCards = passwordCards.filter(\n      (card) => card.department === currentUserDepartment\n    );\n\n    if (departmentCards.length === 0) {\n      alert(\"No password cards available to share in your department.\");\n      return;\n    }\n\n    // Create shareable data\n    const shareData = {\n      title: `${currentUserDepartment} Department Password Cards`,\n      cards: departmentCards,\n      sharedBy: \"Current User\", // This should come from user context\n      sharedAt: new Date().toISOString(),\n    };\n\n    // For now, copy to clipboard (you can implement actual sharing logic)\n    navigator.clipboard\n      .writeText(JSON.stringify(shareData, null, 2))\n      .then(() => {\n        alert(\n          `${departmentCards.length} password cards from ${currentUserDepartment} department copied to clipboard!`\n        );\n      })\n      .catch(() => {\n        alert(\"Failed to copy to clipboard. Please try again.\");\n      });\n  };\n\n  // Placeholder avatar images\n  const avatarImages = [\n    \"https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=A\",\n    \"https://via.placeholder.com/32x32/10B981/FFFFFF?text=B\",\n    \"https://via.placeholder.com/32x32/F59E0B/FFFFFF?text=C\",\n    \"https://via.placeholder.com/32x32/EF4444/FFFFFF?text=D\",\n  ];\n\n  return (\n    <div className=\"bg-white dark:bg-gray-900\">\n      {showTeamTable && (\n        <>\n          {/* Header */}\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\">\n            <div className=\"flex items-center space-x-4\">\n              <h2 className=\"text-left text-2xl font-bold text-gray-900 dark:text-white\">\n                Teams Password Card\n              </h2>\n\n              {/* Share Icon */}\n              <button\n                onClick={handleShare}\n                className=\"flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\"\n              >\n                <span className=\"material-symbols-rounded\">share</span>\n              </button>\n\n              {/* Delete Team Table Icon */}\n              <button\n                onClick={() => handleDeleteTeamTable()}\n                className=\"flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg\"\n                title=\"Delete entire table\"\n              >\n                {/* <span className=\"material-symbols-rounded\">delete</span> */}\n                <span className=\"material-symbols-outlined text-sm\">\n                  delete\n                </span>\n              </button>\n\n              {/* User Avatars */}\n              <div className=\"flex -space-x-2\">\n                {avatarImages.map((avatar, index) => (\n                  <img\n                    key={index}\n                    src={avatar}\n                    alt={`User ${index + 1}`}\n                    className=\"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"\n                  />\n                ))}\n              </div>\n            </div>\n\n            <div className=\"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0\">\n              {/* Add Password Card Button */}\n              <button\n                onClick={() => {\n                  setEditingRowId(null);\n                  setShowAddForm(!showAddForm);\n                }}\n                className={`w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${\n                  showAddForm\n                    ? \"bg-primary text-white\"\n                    : \"bg-transparent text-primary border-2 border-primary\"\n                }`}\n              >\n                <span className=\"material-symbols-rounded mr-2\">add</span>\n                Add Password\n              </button>\n            </div>\n          </div>\n\n          {/* Add/Edit Password Form - Embedded */}\n          {showAddForm && (\n            <AddPasswordCardForm\n              onSubmit={handleAddPasswordCard}\n              onCancel={() => {\n                setShowAddForm(false);\n                setEditingRowId(null);\n              }}\n              generatedPassword={generatedPassword}\n              passwordStrength={passwordStrength}\n            />\n          )}\n\n          {/* Table */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50 border-b border-gray-200\">\n                <tr>\n                  <th className=\"w-12 px-4 py-3 text-left\">\n                    <input\n                      id=\"checkbox-all\"\n                      type=\"checkbox\"\n                      checked={\n                        shareableCards.length === passwordCards.length &&\n                        passwordCards.length > 0\n                      }\n                      onChange={toggleSelectAll}\n                      className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                    />\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"title\")}\n                    >\n                      <span>Title</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"username\")}\n                    >\n                      <span>User Name</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Password\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Team\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Department\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Level\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Action\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"divide-y divide-gray-200\">\n                {getSortedCards(passwordCards).map((card) => (\n                  <tr key={card.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-4 py-4\">\n                      <input\n                        id={`shareable-${card.id}`}\n                        type=\"checkbox\"\n                        checked={shareableCards.includes(card.id)}\n                        onChange={() => toggleShareable(card.id)}\n                        className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                      />\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center\">\n                        <div className=\"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\">\n                          TG\n                        </div>\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.title}\n                            className=\"font-medium text-gray-900 border rounded px-2 py-1\"\n                            onBlur={(e) => {\n                              setPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, title: e.target.value }\n                                    : c\n                                )\n                              );\n                              setEditingRowId(null);\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                            autoFocus\n                          />\n                        ) : (\n                          <span className=\"font-medium text-gray-900\">\n                            {card.title}\n                          </span>\n                        )}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center\">\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.username}\n                            className=\"text-gray-900 border rounded px-2 py-1\"\n                            onBlur={(e) => {\n                              setPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, username: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                          />\n                        ) : (\n                          <span className=\"text-gray-900\">{card.username}</span>\n                        )}\n                        <button\n                          onClick={() => copyToClipboard(card.username)}\n                          className=\"ml-2 text-gray-400 hover:text-gray-600\"\n                        >\n                          <span className=\"material-symbols-rounded text-sm\">\n                            content_copy\n                          </span>\n                        </button>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center\">\n                        <span className=\"text-gray-900 mr-2\">\n                          {visiblePasswords[card.id]\n                            ? card.actualPassword\n                            : card.password}\n                        </span>\n                        <button\n                          onClick={() => togglePasswordVisibility(card.id)}\n                          className=\"text-gray-400 hover:text-gray-600 mr-2\"\n                        >\n                          <span className=\"material-symbols-rounded text-sm\">\n                            {visiblePasswords[card.id]\n                              ? \"visibility_off\"\n                              : \"visibility\"}\n                          </span>\n                        </button>\n                        <button\n                          onClick={() => copyToClipboard(card.actualPassword)}\n                          className=\"text-gray-400 hover:text-gray-600\"\n                        >\n                          <span className=\"material-symbols-rounded text-sm\">\n                            content_copy\n                          </span>\n                        </button>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 text-gray-900\">{card.team}</td>\n                    <td className=\"px-6 py-4 text-gray-900\">\n                      {card.department}\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <span\n                        className={`px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`}\n                      >\n                        {card.strength}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center space-x-3\">\n                        <button\n                          onClick={() => handleEdit(card.id)}\n                          className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n                          title=\"Edit\"\n                        >\n                          <svg\n                            width=\"25\"\n                            height=\"24\"\n                            viewBox=\"0 0 25 24\"\n                            fill=\"none\"\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                          >\n                            <mask\n                              id={`mask1_${card.id}`}\n                              style={{ maskType: \"alpha\" }}\n                              maskUnits=\"userSpaceOnUse\"\n                              x=\"0\"\n                              y=\"0\"\n                              width=\"25\"\n                              height=\"24\"\n                            >\n                              <rect\n                                x=\"0.5\"\n                                width=\"24\"\n                                height=\"24\"\n                                fill=\"#D9D9D9\"\n                              />\n                            </mask>\n                            <g mask={`url(#mask1_${card.id})`}>\n                              <path\n                                d=\"M12.9868 16.3213L14.4068 14.9013L7.50684 8.00134L6.08684 9.42134L12.9868 16.3213ZM15.8268 13.4813L17.2468 12.0613L15.8268 10.6413L14.4068 12.0613L15.8268 13.4813ZM5.50684 19.0013H8.92684L18.6668 9.26134C18.8441 9.08401 18.9441 8.84401 18.9441 8.59134C18.9441 8.33868 18.8441 8.09868 18.6668 7.92134L16.5868 5.84134C16.4095 5.66401 16.1695 5.56401 15.9168 5.56401C15.6641 5.56401 15.4241 5.66401 15.2468 5.84134L5.50684 15.5813V19.0013Z\"\n                                fill=\"currentColor\"\n                              />\n                            </g>\n                          </svg>\n                        </button>\n                        <button\n                          onClick={() => handleDelete(card.id)}\n                          className=\"text-gray-400 hover:text-red-600 transition-colors\"\n                          title=\"Delete\"\n                        >\n                          <svg\n                            width=\"25\"\n                            height=\"24\"\n                            viewBox=\"0 0 25 24\"\n                            fill=\"none\"\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                          >\n                            <mask\n                              id={`mask2_${card.id}`}\n                              style={{ maskType: \"alpha\" }}\n                              maskUnits=\"userSpaceOnUse\"\n                              x=\"0\"\n                              y=\"0\"\n                              width=\"25\"\n                              height=\"24\"\n                            >\n                              <rect\n                                x=\"0.5\"\n                                width=\"24\"\n                                height=\"24\"\n                                fill=\"#D9D9D9\"\n                              />\n                            </mask>\n                            <g mask={`url(#mask2_${card.id})`}>\n                              <path\n                                d=\"M8.60658 19.8997C8.15658 19.8997 7.77325 19.7414 7.45658 19.4247C7.13992 19.1081 6.98158 18.7247 6.98158 18.2747V6.89974H5.98158V4.89974H10.9816V3.89974H14.9816V4.89974H19.9816V6.89974H18.9816V18.2747C18.9816 18.7247 18.8232 19.1081 18.5066 19.4247C18.1899 19.7414 17.8066 19.8997 17.3566 19.8997H8.60658ZM16.9816 6.89974H8.98158V18.2747C8.98158 18.3581 9.01492 18.4331 9.08158 18.4997C9.14825 18.5664 9.22325 18.5997 9.30658 18.5997H16.6566C16.7399 18.5997 16.8149 18.5664 16.8816 18.4997C16.9482 18.4331 16.9816 18.3581 16.9816 18.2747V6.89974ZM10.9816 16.8997H12.9816V8.59974H10.9816V16.8997ZM13.9816 16.8997H15.9816V8.59974H13.9816V16.8997Z\"\n                                fill=\"currentColor\"\n                              />\n                            </g>\n                          </svg>\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </>\n      )}\n\n      {/* New Password Cards Table */}\n      {showNewTable && (\n        <div className=\"mt-8\">\n          <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n            New Password Cards\n          </h3>\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50 border-b border-gray-200\">\n                <tr>\n                  <th className=\"w-12 px-4 py-3 text-left\">Shareable</th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"title\")}\n                    >\n                      <span>Title</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"username\")}\n                    >\n                      <span>User Name</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Password\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Team\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Department\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Level\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Action\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"divide-y divide-gray-200\">\n                {newPasswordCards.length === 0 ? (\n                  <tr>\n                    <td\n                      colSpan=\"8\"\n                      className=\"px-6 py-8 text-center text-gray-500\"\n                    >\n                      No password cards added yet. Click \"Add Password\" to add\n                      your first card.\n                    </td>\n                  </tr>\n                ) : (\n                  getSortedCards(newPasswordCards).map((card) => (\n                    <tr key={card.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-4 py-4\">\n                        <input\n                          type=\"checkbox\"\n                          checked={shareableCards.includes(card.id)}\n                          onChange={() => toggleShareable(card.id)}\n                          className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                        />\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          <div className=\"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\">\n                            TG\n                          </div>\n                          <span className=\"font-medium text-gray-900\">\n                            {card.title}\n                          </span>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 text-gray-900\">\n                        {card.username}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          <span className=\"text-gray-900 mr-2\">\n                            {card.password}\n                          </span>\n                          <button\n                            onClick={() => copyToClipboard(card.actualPassword)}\n                            className=\"text-gray-400 hover:text-gray-600\"\n                          >\n                            <span className=\"material-symbols-rounded text-sm\">\n                              content_copy\n                            </span>\n                          </button>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 text-gray-900\">{card.team}</td>\n                      <td className=\"px-6 py-4 text-gray-900\">\n                        {card.department}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <span\n                          className={`px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`}\n                        >\n                          {card.strength}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center space-x-3\">\n                          <button\n                            onClick={() => handleEdit(card.id)}\n                            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n                            title=\"Edit\"\n                          >\n                            <span className=\"material-symbols-rounded text-sm\">\n                              edit\n                            </span>\n                          </button>\n                          <button\n                            onClick={() =>\n                              setNewPasswordCards((prev) =>\n                                prev.filter((c) => c.id !== card.id)\n                              )\n                            }\n                            className=\"text-gray-400 hover:text-red-600 transition-colors\"\n                            title=\"Delete\"\n                          >\n                            <span className=\"material-symbols-rounded text-sm\">\n                              delete\n                            </span>\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))\n                )}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      )}\n\n      {/* Add New Password Card Button (Bottom) */}\n      <div className=\"flex justify-center mt-6\">\n        <button\n          onClick={handleCreateNewTable}\n          className=\"flex items-center justify-center px-6 py-2 text-sm font-medium text-white rounded-lg bg-primary hover:bg-primary/90 focus:ring-4 focus:ring-primary/30 transition-colors duration-200 focus:outline-none\"\n        >\n          <span className=\"material-symbols-rounded mr-2\">add</span>\n          Add New Password Card\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default PasswordCardsTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,OAAOC,iBAAiB,MAAM,0CAA0C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzE,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,iBAAiB;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EACtE;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC,CACjD;IACEe,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,6CAA6C;IAC7DC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE;EACjB,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,iBAAiB;IACxBC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,kBAAkB;IAClCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,iBAAiB;IAC3BC,aAAa,EAAE;EACjB,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,mBAAmB;IAC1BC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,iBAAiB;IACjCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,aAAa;IACzBC,QAAQ,EAAE,mBAAmB;IAC7BC,aAAa,EAAE;EACjB,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,aAAa;IACpBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,UAAU;IAC1BC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE;EACjB,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,uBAAuB;IACvCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,oBAAoB;IAChCC,QAAQ,EAAE,iBAAiB;IAC3BC,aAAa,EAAE;EACjB,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,eAAe;IACzBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,mBAAmB;IACnCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,mBAAmB;IAC7BC,aAAa,EAAE;EACjB,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,gBAAgB;IACvBC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,cAAc;IAC9BC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE;EACjB,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC;IAAEyC,GAAG,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAM,CAAC,CAAC;EAE7E,MAAM,CAACC,cAAc,CAAC,GAAGxC,yBAAyB,CAAC,CAAC;EACpD,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM8C,wBAAwB,GAAI/B,EAAE,IAAK;IACvCW,mBAAmB,CAAEqB,IAAI,KAAM;MAC7B,GAAGA,IAAI;MACP,CAAChC,EAAE,GAAG,CAACgC,IAAI,CAAChC,EAAE;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMiC,eAAe,GAAIC,IAAI,IAAK;IAChCC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMI,UAAU,GAAItC,EAAE,IAAK;IACzBqB,eAAe,CAACD,YAAY,KAAKpB,EAAE,GAAG,IAAI,GAAGA,EAAE,CAAC;EAClD,CAAC;;EAED;EACA,MAAMuC,oBAAoB,GAAGA,CAAA,KAAM;IACjCxB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMyB,qBAAqB,GAAGA,CAAA,KAAM;IAClCrD,iBAAiB,CAAC;MAChBsD,SAAS,EAAEA,CAAA,KAAM;QACf;QACA1C,gBAAgB,CAAC,EAAE,CAAC;QACpB;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;;EAEA,MAAM2C,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIpB,cAAc,CAACqB,MAAM,KAAK7C,aAAa,CAAC6C,MAAM,EAAE;MAClDpB,iBAAiB,CAAC,EAAE,CAAC;IACvB,CAAC,MAAM;MACLA,iBAAiB,CAACzB,aAAa,CAAC8C,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAAC7C,EAAE,CAAC,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAM8C,UAAU,GAAIpB,GAAG,IAAK;IAC1B,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIH,UAAU,CAACE,GAAG,KAAKA,GAAG,IAAIF,UAAU,CAACG,SAAS,KAAK,KAAK,EAAE;MAC5DA,SAAS,GAAG,MAAM;IACpB;IACAF,aAAa,CAAC;MAAEC,GAAG;MAAEC;IAAU,CAAC,CAAC;EACnC,CAAC;;EAED;EACA,MAAMoB,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAI,CAACxB,UAAU,CAACE,GAAG,EAAE,OAAOsB,KAAK;IAEjC,OAAO,CAAC,GAAGA,KAAK,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC/B,MAAMC,MAAM,GAAGF,CAAC,CAAC1B,UAAU,CAACE,GAAG,CAAC,CAAC2B,WAAW,CAAC,CAAC;MAC9C,MAAMC,MAAM,GAAGH,CAAC,CAAC3B,UAAU,CAACE,GAAG,CAAC,CAAC2B,WAAW,CAAC,CAAC;MAE9C,IAAI7B,UAAU,CAACG,SAAS,KAAK,KAAK,EAAE;QAClC,OAAOyB,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MACvD,CAAC,MAAM;QACL,OAAOF,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MACvD;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIvD,EAAE,IAAK;IAC9BuB,iBAAiB,CAAES,IAAI,IACrBA,IAAI,CAACwB,QAAQ,CAACxD,EAAE,CAAC,GAAGgC,IAAI,CAACyB,MAAM,CAAEC,MAAM,IAAKA,MAAM,KAAK1D,EAAE,CAAC,GAAG,CAAC,GAAGgC,IAAI,EAAEhC,EAAE,CAC3E,CAAC;EACH,CAAC;;EAED;EACA,MAAM2D,qBAAqB,GAAIC,QAAQ,IAAK;IAC1C,MAAMC,OAAO,GAAG;MACd,GAAGD,QAAQ;MACX5D,EAAE,EAAE8D,IAAI,CAACC,GAAG,CAAC,CAAC;MACd3D,QAAQ,EAAE,cAAc;MACxBC,cAAc,EAAEuD,QAAQ,CAACxD;IAC3B,CAAC;IACDe,mBAAmB,CAAEa,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE6B,OAAO,CAAC,CAAC;IACjDhD,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMmD,YAAY,GAAIhE,EAAE,IAAK;IAC3BiE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAElE,EAAE,CAAC;IACxC;IACAD,gBAAgB,CAAEiC,IAAI,IAAKA,IAAI,CAACyB,MAAM,CAAEZ,IAAI,IAAKA,IAAI,CAAC7C,EAAE,KAAKA,EAAE,CAAC,CAAC;EACnE,CAAC;;EAED;EACA,MAAMmE,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,MAAMC,qBAAqB,GAAG,IAAI,CAAC,CAAC;;IAEpC;IACA,MAAMC,eAAe,GAAGvE,aAAa,CAAC2D,MAAM,CACzCZ,IAAI,IAAKA,IAAI,CAACtC,UAAU,KAAK6D,qBAChC,CAAC;IAED,IAAIC,eAAe,CAAC1B,MAAM,KAAK,CAAC,EAAE;MAChC2B,KAAK,CAAC,0DAA0D,CAAC;MACjE;IACF;;IAEA;IACA,MAAMC,SAAS,GAAG;MAChBtE,KAAK,EAAE,GAAGmE,qBAAqB,4BAA4B;MAC3DpB,KAAK,EAAEqB,eAAe;MACtBG,QAAQ,EAAE,cAAc;MAAE;MAC1BC,QAAQ,EAAE,IAAIX,IAAI,CAAC,CAAC,CAACY,WAAW,CAAC;IACnC,CAAC;;IAED;IACAvC,SAAS,CAACC,SAAS,CAChBC,SAAS,CAACsC,IAAI,CAACC,SAAS,CAACL,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAC7CM,IAAI,CAAC,MAAM;MACVP,KAAK,CACH,GAAGD,eAAe,CAAC1B,MAAM,wBAAwByB,qBAAqB,kCACxE,CAAC;IACH,CAAC,CAAC,CACDU,KAAK,CAAC,MAAM;MACXR,KAAK,CAAC,gDAAgD,CAAC;IACzD,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMS,YAAY,GAAG,CACnB,wDAAwD,EACxD,wDAAwD,EACxD,wDAAwD,EACxD,wDAAwD,CACzD;EAED,oBACExF,OAAA;IAAKyF,SAAS,EAAC,2BAA2B;IAAAC,QAAA,GACvCjE,aAAa,iBACZzB,OAAA,CAAAE,SAAA;MAAAwF,QAAA,gBAEE1F,OAAA;QAAKyF,SAAS,EAAC,iGAAiG;QAAAC,QAAA,gBAC9G1F,OAAA;UAAKyF,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C1F,OAAA;YAAIyF,SAAS,EAAC,4DAA4D;YAAAC,QAAA,EAAC;UAE3E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGL9F,OAAA;YACE+F,OAAO,EAAEnB,WAAY;YACrBa,SAAS,EAAC,+KAA+K;YAAAC,QAAA,eAEzL1F,OAAA;cAAMyF,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAGT9F,OAAA;YACE+F,OAAO,EAAEA,CAAA,KAAM9C,qBAAqB,CAAC,CAAE;YACvCwC,SAAS,EAAC,qGAAqG;YAC/G/E,KAAK,EAAC,qBAAqB;YAAAgF,QAAA,eAG3B1F,OAAA;cAAMyF,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGT9F,OAAA;YAAKyF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BF,YAAY,CAACnC,GAAG,CAAC,CAAC2C,MAAM,EAAEC,KAAK,kBAC9BjG,OAAA;cAEEkG,GAAG,EAAEF,MAAO;cACZG,GAAG,EAAE,QAAQF,KAAK,GAAG,CAAC,EAAG;cACzBR,SAAS,EAAC;YAAiE,GAHtEQ,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9F,OAAA;UAAKyF,SAAS,EAAC,0IAA0I;UAAAC,QAAA,eAEvJ1F,OAAA;YACE+F,OAAO,EAAEA,CAAA,KAAM;cACbjE,eAAe,CAAC,IAAI,CAAC;cACrBR,cAAc,CAAC,CAACD,WAAW,CAAC;YAC9B,CAAE;YACFoE,SAAS,EAAE,ucACTpE,WAAW,GACP,uBAAuB,GACvB,qDAAqD,EACxD;YAAAqE,QAAA,gBAEH1F,OAAA;cAAMyF,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAE5D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLzE,WAAW,iBACVrB,OAAA,CAACL,mBAAmB;QAClByG,QAAQ,EAAEhC,qBAAsB;QAChCiC,QAAQ,EAAEA,CAAA,KAAM;UACd/E,cAAc,CAAC,KAAK,CAAC;UACrBQ,eAAe,CAAC,IAAI,CAAC;QACvB,CAAE;QACF1B,iBAAiB,EAAEA,iBAAkB;QACrCC,gBAAgB,EAAEA;MAAiB;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACF,eAGD9F,OAAA;QAAKyF,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnE1F,OAAA;UAAOyF,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACvB1F,OAAA;YAAOyF,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eACpD1F,OAAA;cAAA0F,QAAA,gBACE1F,OAAA;gBAAIyF,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,eACtC1F,OAAA;kBACES,EAAE,EAAC,cAAc;kBACjB6F,IAAI,EAAC,UAAU;kBACfC,OAAO,EACLxE,cAAc,CAACqB,MAAM,KAAK7C,aAAa,CAAC6C,MAAM,IAC9C7C,aAAa,CAAC6C,MAAM,GAAG,CACxB;kBACDoD,QAAQ,EAAErD,eAAgB;kBAC1BsC,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,eACnE1F,OAAA;kBACEyF,SAAS,EAAC,4CAA4C;kBACtDM,OAAO,EAAEA,CAAA,KAAMxC,UAAU,CAAC,OAAO,CAAE;kBAAAmC,QAAA,gBAEnC1F,OAAA;oBAAA0F,QAAA,EAAM;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClB9F,OAAA;oBAAKyF,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B1F,OAAA;sBACEyF,SAAS,EAAE,WACTxD,UAAU,CAACE,GAAG,KAAK,OAAO,IAC1BF,UAAU,CAACG,SAAS,KAAK,KAAK,GAC1B,eAAe,GACf,eAAe,EAClB;sBAAAsD,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACP9F,OAAA;sBACEyF,SAAS,EAAE,WACTxD,UAAU,CAACE,GAAG,KAAK,OAAO,IAC1BF,UAAU,CAACG,SAAS,KAAK,MAAM,GAC3B,eAAe,GACf,eAAe,EAClB;sBAAAsD,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,eACnE1F,OAAA;kBACEyF,SAAS,EAAC,4CAA4C;kBACtDM,OAAO,EAAEA,CAAA,KAAMxC,UAAU,CAAC,UAAU,CAAE;kBAAAmC,QAAA,gBAEtC1F,OAAA;oBAAA0F,QAAA,EAAM;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtB9F,OAAA;oBAAKyF,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B1F,OAAA;sBACEyF,SAAS,EAAE,WACTxD,UAAU,CAACE,GAAG,KAAK,UAAU,IAC7BF,UAAU,CAACG,SAAS,KAAK,KAAK,GAC1B,eAAe,GACf,eAAe,EAClB;sBAAAsD,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACP9F,OAAA;sBACEyF,SAAS,EAAE,WACTxD,UAAU,CAACE,GAAG,KAAK,UAAU,IAC7BF,UAAU,CAACG,SAAS,KAAK,MAAM,GAC3B,eAAe,GACf,eAAe,EAClB;sBAAAsD,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR9F,OAAA;YAAOyF,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EACxClC,cAAc,CAACjD,aAAa,CAAC,CAAC8C,GAAG,CAAEC,IAAI,iBACtCtD,OAAA;cAAkByF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC5C1F,OAAA;gBAAIyF,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB1F,OAAA;kBACES,EAAE,EAAE,aAAa6C,IAAI,CAAC7C,EAAE,EAAG;kBAC3B6F,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAExE,cAAc,CAACkC,QAAQ,CAACX,IAAI,CAAC7C,EAAE,CAAE;kBAC1C+F,QAAQ,EAAEA,CAAA,KAAMxC,eAAe,CAACV,IAAI,CAAC7C,EAAE,CAAE;kBACzCgF,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB1F,OAAA;kBAAKyF,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC1F,OAAA;oBAAKyF,SAAS,EAAC,gGAAgG;oBAAAC,QAAA,EAAC;kBAEhH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,EACLjE,YAAY,KAAKyB,IAAI,CAAC7C,EAAE,gBACvBT,OAAA;oBACEsG,IAAI,EAAC,MAAM;oBACXG,YAAY,EAAEnD,IAAI,CAAC5C,KAAM;oBACzB+E,SAAS,EAAC,oDAAoD;oBAC9DiB,MAAM,EAAGC,CAAC,IAAK;sBACbnG,gBAAgB,CAAEiC,IAAI,IACpBA,IAAI,CAACY,GAAG,CAAEuD,CAAC,IACTA,CAAC,CAACnG,EAAE,KAAK6C,IAAI,CAAC7C,EAAE,GACZ;wBAAE,GAAGmG,CAAC;wBAAElG,KAAK,EAAEiG,CAAC,CAACE,MAAM,CAACC;sBAAM,CAAC,GAC/BF,CACN,CACF,CAAC;sBACD9E,eAAe,CAAC,IAAI,CAAC;oBACvB,CAAE;oBACFiF,SAAS,EAAGJ,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAACxE,GAAG,KAAK,OAAO,EAAE;wBACrBwE,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC;sBACjB;oBACF,CAAE;oBACFC,SAAS;kBAAA;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,gBAEF9F,OAAA;oBAAMyF,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EACxCpC,IAAI,CAAC5C;kBAAK;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB1F,OAAA;kBAAKyF,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,GAC/B7D,YAAY,KAAKyB,IAAI,CAAC7C,EAAE,gBACvBT,OAAA;oBACEsG,IAAI,EAAC,MAAM;oBACXG,YAAY,EAAEnD,IAAI,CAAC1C,QAAS;oBAC5B6E,SAAS,EAAC,wCAAwC;oBAClDiB,MAAM,EAAGC,CAAC,IAAK;sBACbnG,gBAAgB,CAAEiC,IAAI,IACpBA,IAAI,CAACY,GAAG,CAAEuD,CAAC,IACTA,CAAC,CAACnG,EAAE,KAAK6C,IAAI,CAAC7C,EAAE,GACZ;wBAAE,GAAGmG,CAAC;wBAAEhG,QAAQ,EAAE+F,CAAC,CAACE,MAAM,CAACC;sBAAM,CAAC,GAClCF,CACN,CACF,CAAC;oBACH,CAAE;oBACFG,SAAS,EAAGJ,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAACxE,GAAG,KAAK,OAAO,EAAE;wBACrBwE,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC;sBACjB;oBACF;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEF9F,OAAA;oBAAMyF,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAEpC,IAAI,CAAC1C;kBAAQ;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACtD,eACD9F,OAAA;oBACE+F,OAAO,EAAEA,CAAA,KAAMrD,eAAe,CAACY,IAAI,CAAC1C,QAAQ,CAAE;oBAC9C6E,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eAElD1F,OAAA;sBAAMyF,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB1F,OAAA;kBAAKyF,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC1F,OAAA;oBAAMyF,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EACjCvE,gBAAgB,CAACmC,IAAI,CAAC7C,EAAE,CAAC,GACtB6C,IAAI,CAACxC,cAAc,GACnBwC,IAAI,CAACzC;kBAAQ;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eACP9F,OAAA;oBACE+F,OAAO,EAAEA,CAAA,KAAMvD,wBAAwB,CAACc,IAAI,CAAC7C,EAAE,CAAE;oBACjDgF,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eAElD1F,OAAA;sBAAMyF,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC/CvE,gBAAgB,CAACmC,IAAI,CAAC7C,EAAE,CAAC,GACtB,gBAAgB,GAChB;oBAAY;sBAAAkF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACT9F,OAAA;oBACE+F,OAAO,EAAEA,CAAA,KAAMrD,eAAe,CAACY,IAAI,CAACxC,cAAc,CAAE;oBACpD2E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,eAE7C1F,OAAA;sBAAMyF,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAEpC,IAAI,CAACvC;cAAI;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxD9F,OAAA;gBAAIyF,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EACpCpC,IAAI,CAACtC;cAAU;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB1F,OAAA;kBACEyF,SAAS,EAAE,8CAA8CnC,IAAI,CAACpC,aAAa,EAAG;kBAAAwE,QAAA,EAE7EpC,IAAI,CAACrC;gBAAQ;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB1F,OAAA;kBAAKyF,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C1F,OAAA;oBACE+F,OAAO,EAAEA,CAAA,KAAMhD,UAAU,CAACO,IAAI,CAAC7C,EAAE,CAAE;oBACnCgF,SAAS,EAAC,qDAAqD;oBAC/D/E,KAAK,EAAC,MAAM;oBAAAgF,QAAA,eAEZ1F,OAAA;sBACEkH,KAAK,EAAC,IAAI;sBACVC,MAAM,EAAC,IAAI;sBACXC,OAAO,EAAC,WAAW;sBACnBC,IAAI,EAAC,MAAM;sBACXC,KAAK,EAAC,4BAA4B;sBAAA5B,QAAA,gBAElC1F,OAAA;wBACES,EAAE,EAAE,SAAS6C,IAAI,CAAC7C,EAAE,EAAG;wBACvB8G,KAAK,EAAE;0BAAEC,QAAQ,EAAE;wBAAQ,CAAE;wBAC7BC,SAAS,EAAC,gBAAgB;wBAC1BC,CAAC,EAAC,GAAG;wBACLC,CAAC,EAAC,GAAG;wBACLT,KAAK,EAAC,IAAI;wBACVC,MAAM,EAAC,IAAI;wBAAAzB,QAAA,eAEX1F,OAAA;0BACE0H,CAAC,EAAC,KAAK;0BACPR,KAAK,EAAC,IAAI;0BACVC,MAAM,EAAC,IAAI;0BACXE,IAAI,EAAC;wBAAS;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACP9F,OAAA;wBAAG4H,IAAI,EAAE,cAActE,IAAI,CAAC7C,EAAE,GAAI;wBAAAiF,QAAA,eAChC1F,OAAA;0BACE6H,CAAC,EAAC,qbAAqb;0BACvbR,IAAI,EAAC;wBAAc;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACT9F,OAAA;oBACE+F,OAAO,EAAEA,CAAA,KAAMtB,YAAY,CAACnB,IAAI,CAAC7C,EAAE,CAAE;oBACrCgF,SAAS,EAAC,oDAAoD;oBAC9D/E,KAAK,EAAC,QAAQ;oBAAAgF,QAAA,eAEd1F,OAAA;sBACEkH,KAAK,EAAC,IAAI;sBACVC,MAAM,EAAC,IAAI;sBACXC,OAAO,EAAC,WAAW;sBACnBC,IAAI,EAAC,MAAM;sBACXC,KAAK,EAAC,4BAA4B;sBAAA5B,QAAA,gBAElC1F,OAAA;wBACES,EAAE,EAAE,SAAS6C,IAAI,CAAC7C,EAAE,EAAG;wBACvB8G,KAAK,EAAE;0BAAEC,QAAQ,EAAE;wBAAQ,CAAE;wBAC7BC,SAAS,EAAC,gBAAgB;wBAC1BC,CAAC,EAAC,GAAG;wBACLC,CAAC,EAAC,GAAG;wBACLT,KAAK,EAAC,IAAI;wBACVC,MAAM,EAAC,IAAI;wBAAAzB,QAAA,eAEX1F,OAAA;0BACE0H,CAAC,EAAC,KAAK;0BACPR,KAAK,EAAC,IAAI;0BACVC,MAAM,EAAC,IAAI;0BACXE,IAAI,EAAC;wBAAS;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACP9F,OAAA;wBAAG4H,IAAI,EAAE,cAActE,IAAI,CAAC7C,EAAE,GAAI;wBAAAiF,QAAA,eAChC1F,OAAA;0BACE6H,CAAC,EAAC,soBAAsoB;0BACxoBR,IAAI,EAAC;wBAAc;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAhMExC,IAAI,CAAC7C,EAAE;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiMZ,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA,eACN,CACH,EAGAvE,YAAY,iBACXvB,OAAA;MAAKyF,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB1F,OAAA;QAAIyF,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EAAC;MAErD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL9F,OAAA;QAAKyF,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnE1F,OAAA;UAAOyF,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACvB1F,OAAA;YAAOyF,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eACpD1F,OAAA;cAAA0F,QAAA,gBACE1F,OAAA;gBAAIyF,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvD9F,OAAA;gBAAIyF,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,eACnE1F,OAAA;kBACEyF,SAAS,EAAC,4CAA4C;kBACtDM,OAAO,EAAEA,CAAA,KAAMxC,UAAU,CAAC,OAAO,CAAE;kBAAAmC,QAAA,gBAEnC1F,OAAA;oBAAA0F,QAAA,EAAM;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClB9F,OAAA;oBAAKyF,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B1F,OAAA;sBACEyF,SAAS,EAAE,WACTxD,UAAU,CAACE,GAAG,KAAK,OAAO,IAC1BF,UAAU,CAACG,SAAS,KAAK,KAAK,GAC1B,eAAe,GACf,eAAe,EAClB;sBAAAsD,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACP9F,OAAA;sBACEyF,SAAS,EAAE,WACTxD,UAAU,CAACE,GAAG,KAAK,OAAO,IAC1BF,UAAU,CAACG,SAAS,KAAK,MAAM,GAC3B,eAAe,GACf,eAAe,EAClB;sBAAAsD,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,eACnE1F,OAAA;kBACEyF,SAAS,EAAC,4CAA4C;kBACtDM,OAAO,EAAEA,CAAA,KAAMxC,UAAU,CAAC,UAAU,CAAE;kBAAAmC,QAAA,gBAEtC1F,OAAA;oBAAA0F,QAAA,EAAM;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtB9F,OAAA;oBAAKyF,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B1F,OAAA;sBACEyF,SAAS,EAAE,WACTxD,UAAU,CAACE,GAAG,KAAK,UAAU,IAC7BF,UAAU,CAACG,SAAS,KAAK,KAAK,GAC1B,eAAe,GACf,eAAe,EAClB;sBAAAsD,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACP9F,OAAA;sBACEyF,SAAS,EAAE,WACTxD,UAAU,CAACE,GAAG,KAAK,UAAU,IAC7BF,UAAU,CAACG,SAAS,KAAK,MAAM,GAC3B,eAAe,GACf,eAAe,EAClB;sBAAAsD,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR9F,OAAA;YAAOyF,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EACxC/D,gBAAgB,CAACyB,MAAM,KAAK,CAAC,gBAC5BpD,OAAA;cAAA0F,QAAA,eACE1F,OAAA;gBACE8H,OAAO,EAAC,GAAG;gBACXrC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAChD;cAGD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GAELtC,cAAc,CAAC7B,gBAAgB,CAAC,CAAC0B,GAAG,CAAEC,IAAI,iBACxCtD,OAAA;cAAkByF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC5C1F,OAAA;gBAAIyF,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB1F,OAAA;kBACEsG,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAExE,cAAc,CAACkC,QAAQ,CAACX,IAAI,CAAC7C,EAAE,CAAE;kBAC1C+F,QAAQ,EAAEA,CAAA,KAAMxC,eAAe,CAACV,IAAI,CAAC7C,EAAE,CAAE;kBACzCgF,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB1F,OAAA;kBAAKyF,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC1F,OAAA;oBAAKyF,SAAS,EAAC,gGAAgG;oBAAAC,QAAA,EAAC;kBAEhH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN9F,OAAA;oBAAMyF,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EACxCpC,IAAI,CAAC5C;kBAAK;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EACpCpC,IAAI,CAAC1C;cAAQ;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB1F,OAAA;kBAAKyF,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC1F,OAAA;oBAAMyF,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EACjCpC,IAAI,CAACzC;kBAAQ;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACP9F,OAAA;oBACE+F,OAAO,EAAEA,CAAA,KAAMrD,eAAe,CAACY,IAAI,CAACxC,cAAc,CAAE;oBACpD2E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,eAE7C1F,OAAA;sBAAMyF,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAEpC,IAAI,CAACvC;cAAI;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxD9F,OAAA;gBAAIyF,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EACpCpC,IAAI,CAACtC;cAAU;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB1F,OAAA;kBACEyF,SAAS,EAAE,8CAA8CnC,IAAI,CAACpC,aAAa,EAAG;kBAAAwE,QAAA,EAE7EpC,IAAI,CAACrC;gBAAQ;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL9F,OAAA;gBAAIyF,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB1F,OAAA;kBAAKyF,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C1F,OAAA;oBACE+F,OAAO,EAAEA,CAAA,KAAMhD,UAAU,CAACO,IAAI,CAAC7C,EAAE,CAAE;oBACnCgF,SAAS,EAAC,qDAAqD;oBAC/D/E,KAAK,EAAC,MAAM;oBAAAgF,QAAA,eAEZ1F,OAAA;sBAAMyF,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACT9F,OAAA;oBACE+F,OAAO,EAAEA,CAAA,KACPnE,mBAAmB,CAAEa,IAAI,IACvBA,IAAI,CAACyB,MAAM,CAAE0C,CAAC,IAAKA,CAAC,CAACnG,EAAE,KAAK6C,IAAI,CAAC7C,EAAE,CACrC,CACD;oBACDgF,SAAS,EAAC,oDAAoD;oBAC9D/E,KAAK,EAAC,QAAQ;oBAAAgF,QAAA,eAEd1F,OAAA;sBAAMyF,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAzEExC,IAAI,CAAC7C,EAAE;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0EZ,CACL;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD9F,OAAA;MAAKyF,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvC1F,OAAA;QACE+F,OAAO,EAAE/C,oBAAqB;QAC9ByC,SAAS,EAAC,0MAA0M;QAAAC,QAAA,gBAEpN1F,OAAA;UAAMyF,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,yBAE5D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxF,EAAA,CA/yBIH,kBAAkB;EAAA,QAkGGN,yBAAyB;AAAA;AAAAkI,EAAA,GAlG9C5H,kBAAkB;AAizBxB,eAAeA,kBAAkB;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}