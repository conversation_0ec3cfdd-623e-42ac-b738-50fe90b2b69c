import React, { useState, useEffect } from 'react';

// Main component for the Add Password Card form
const AddPasswordCardForm = ({ onSubmit, onCancel, generatedPassword, passwordStrength }) => {
  // State to hold form data
  const [formData, setFormData] = useState({
    title: '',
    password: '',
    team: '',
    department: '',
    strength: 'Weak Password'
  });

  // Department and Team options for the select dropdowns
  const departments = [
    'IT', 'Development', 'DevOps', 'Project Management', 'HR', 'Database', 'Marketing', 'Sales'
  ];

  const teamsByDepartment = {
    'IT': ['IT Support', 'System Admin', 'Network Team'],
    'Development': ['Frontend Team', 'Backend Team', 'Full Stack Team'],
    'DevOps': ['Infrastructure Team', 'CI/CD Team', 'Cloud Team'],
    'Project Management': ['Scrum Masters', 'Product Owners', 'PMO'],
    'HR': ['Recruitment', 'Employee Relations', 'Payroll'],
    'Database': ['DBA Team', 'Data Analytics', 'Data Engineering'],
    'Marketing': ['Digital Marketing', 'Content Team', 'SEO Team'],
    'Sales': ['Inside Sales', 'Field Sales', 'Sales Support']
  };

  // State for showing/hiding the password, individual field errors, and a general form error
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [formError, setFormError] = useState(''); // **NEW**: State for the main error message

  // Effect to update the form's password field when a new password is generated
  useEffect(() => {
    if (generatedPassword) {
      setFormData(prev => ({
        ...prev,
        password: generatedPassword,
        strength: passwordStrength
      }));
    }
  }, [generatedPassword, passwordStrength]);

  // Handles input changes for all form fields
  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // Reset team selection when the department changes
    if (name === 'department') {
      setFormData(prev => ({ ...prev, [name]: value, team: '' }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear the error for a field when the user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }

    // Automatically calculate password strength as the user types
    if (name === 'password') {
      const strength = calculatePasswordStrength(value);
      setFormData(prev => ({ ...prev, strength: strength }));
    }
  };

  // Calculates the strength of a given password
  const calculatePasswordStrength = (password) => {
    if (!password) return 'Weak Password';
    let score = 0;
    if (password.length >= 12) score += 2;
    else if (password.length >= 8) score += 1;
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^A-Za-z0-9]/.test(password)) score += 1;
    if (password.length >= 16) score += 1;
    if (score >= 6) return 'Strong Password';
    if (score >= 4) return 'Moderate Password';
    return 'Weak Password';
  };

  // Returns Tailwind CSS classes based on password strength for styling
  const getStrengthColor = (strength) => {
    switch (strength) {
      case 'Strong Password': return 'bg-green-100 text-green-600 border-green-300';
      case 'Moderate Password': return 'bg-yellow-100 text-yellow-600 border-yellow-300';
      case 'Weak Password': return 'bg-red-100 text-red-600 border-red-300';
      default: return 'bg-gray-100 text-gray-600 border-gray-300';
    }
  };

  // Validates the form fields before submission
  const validateForm = () => {
    const newErrors = {};
    if (!formData.department.trim()) newErrors.department = 'Department is required';
    if (!formData.team.trim()) newErrors.team = 'Team is required';
    if (!formData.title.trim()) newErrors.title = 'Title is required';
    if (!formData.password.trim()) newErrors.password = 'Password is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handles the form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    setFormError(''); // Clear previous general errors

    
    if (validateForm()) {
      const strengthColor = getStrengthColor(formData.strength);
      const cardData = { ...formData, strengthColor };
      onSubmit(cardData); // This function is passed from the parent component
      
      // Reset form after successful submission
      setFormData({ title: '', password: '', team: '', department: '', strength: 'Weak Password' });
    } else {
      // **NEW**: Set a clear error message for the user
      setFormError('Please fill out all required fields before saving.');
    }
  };

  // Toggles password visibility
  const togglePasswordVisibility = () => setShowPassword(!showPassword);

  // Sets the form password to the generated password
  const useGeneratedPassword = () => {
    if (generatedPassword) {
      setFormData(prev => ({ ...prev, password: generatedPassword, strength: passwordStrength }));
    }
  };

  return (
    <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
      <div className="rounded-lg shadow-md w-full max-w-4xl relative bg-neutral-50 m-4">
        <div className="flex justify-between items-center mb-4 bg-gray-100 px-4 py-3">
          <h4 className="text-lg font-medium text-left text-gray-800">Add New Password Card</h4>
          <button className="text-3xl text-gray-500 hover:text-gray-800" onClick={onCancel}>&times;</button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className='flex flex-wrap gap-4 p-6 overflow-y-auto max-h-[80vh] scrollbar-vertical'>
            
            
            {formError && (
              <div className="w-full p-3 mb-2 text-sm text-red-800 rounded-lg bg-red-100 border border-red-300" role="alert">
                <span className="font-medium">Action Required:</span> {formError}
              </div>
            )}

            {/* Department Dropdown */}
            <div className="mb-4 w-full md:max-w-[48%] text-left">
              <label htmlFor="department" className="block pb-2 text-base text-gray-600">Department <span className='text-red-600'>*</span></label>
              <select id="department" name="department" value={formData.department} onChange={handleInputChange} className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.department ? 'border-red-500' : 'border-gray-300'}`}>
                <option value="">Select Department</option>
                {departments.map(dept => (<option key={dept} value={dept}>{dept}</option>))}
              </select>
              {errors.department && <p className="mt-1 text-sm text-red-600">{errors.department}</p>}
            </div>

            {/* Team Dropdown */}
            <div className="mb-4 w-full md:max-w-[48%] text-left">
              <label htmlFor="team" className="block pb-2 text-base text-gray-600">Team <span className='text-red-600'>*</span></label>
              <select id="team" name="team" value={formData.team} onChange={handleInputChange} disabled={!formData.department} className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.team ? 'border-red-500' : 'border-gray-300'} ${!formData.department ? 'bg-gray-100 cursor-not-allowed' : ''}`}>
                <option value="">Select Team</option>
                {formData.department && teamsByDepartment[formData.department]?.map(team => (<option key={team} value={team}>{team}</option>))}
              </select>
              {errors.team && <p className="mt-1 text-sm text-red-600">{errors.team}</p>}
              {!formData.department && (<p className="mt-1 text-sm text-gray-500">Please select a department first</p>)}
            </div>
            
            {/* Platform/Service Title Input */}
            <div className="mb-4 w-full md:max-w-[48%] text-left">
              <label htmlFor="title" className="block pb-2 text-base text-gray-600">Platform/Service Title <span className='text-red-600'>*</span></label>
              <input type="text" id="title" name="title" value={formData.title} onChange={handleInputChange} className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.title ? 'border-red-500' : 'border-gray-300'}`} placeholder="e.g., Gmail, GitHub, AWS Console" />
              {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
            </div>

            {/* Password Input */}
            <div className="mb-4 w-full md:max-w-[48%] text-left">
              <label htmlFor="password" className="block pb-2 text-base text-gray-600">Password <span className='text-red-600'>*</span></label>
              <div className="relative">
                <input type={showPassword ? "text" : "password"} id="password" name="password" value={formData.password} onChange={handleInputChange} className={`w-full px-3 py-2 pr-24 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.password ? 'border-red-500' : 'border-gray-300'}`} placeholder="Enter password" />
                <div className="absolute inset-y-0 right-0 flex items-center">
                  <button type="button" onClick={useGeneratedPassword} className="px-2 text-xs text-blue-600 hover:text-blue-800 border-r border-gray-300" title="Use generated password from above">Use Gen</button>
                  <button type="button" onClick={togglePasswordVisibility} className="pr-3 pl-2 flex items-center text-gray-400 hover:text-gray-600">
                    <span className="material-symbols-rounded text-sm">{showPassword ? 'visibility_off' : 'visibility'}</span>
                  </button>
                </div>
              </div>
              {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password}</p>}
              {formData.password && (<div className="mt-2"><span className={`inline-block px-3 py-1 text-xs font-medium rounded-full border ${getStrengthColor(formData.strength)}`}>{formData.strength}</span></div>)}
            </div>
          </div>

          {/* Form Action Buttons */}
          <div className="flex justify-end space-x-4 p-6 bg-gray-50 border-t">
            <button type="button" onClick={onCancel} className="px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">Cancel</button>
            <button type="submit" className="flex items-center justify-center px-6 py-2 text-sm font-medium text-white rounded-lg bg-primary hover:bg-primary/90 focus:ring-4 focus:ring-primary/30 transition-colors duration-200 focus:outline-none">Save Password</button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddPasswordCardForm;
