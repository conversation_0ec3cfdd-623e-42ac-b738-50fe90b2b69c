import React, { useState } from "react";
import AddPasswordCardForm from "./AddPasswordCardForm";
import { confirmationAlert } from "../../common/coreui";
import FetchLoggedInRole from "../../common/fetchData/FetchLoggedInRole";

const PasswordCardsTable = ({ generatedPassword, passwordStrength }) => {
  // Get current user data
  const { userData } = FetchLoggedInRole();
  const currentUserId = userData?.id;

  // Sample data - in real app this would come from API/state management
  const [passwordCards, setPasswordCards] = useState([
    {
      id: 1,
      title: "Gmail Account",
      platform: "Gmail",
      username: "<EMAIL>",
      password: "••••••••••••",
      actualPassword: "xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn",
      team: "Team Name",
      department: "IT",
      strength: "Weak Password",
      strengthColor: "bg-red-100 text-red-600 border-red-300",
      authorId: currentUserId, // Add author ID
    },
    {
      id: 2,
      title: "Slack Workspace",
      platform: "Slack",
      username: "<EMAIL>",
      password: "••••••••••••",
      actualPassword: "StrongPass123!@#",
      team: "Team Name",
      department: "IT",
      strength: "Strong Password",
      strengthColor: "bg-green-100 text-green-600 border-green-300",
      authorId: currentUserId,
    },
    {
      id: 3,
      title: "GitHub Repository",
      platform: "GitHub",
      username: "<EMAIL>",
      password: "••••••••••••",
      actualPassword: "ModeratePass456",
      team: "Team Name",
      department: "Development",
      strength: "Moderate Password",
      strengthColor: "bg-yellow-100 text-yellow-600 border-yellow-300",
      authorId: 999, // Different author ID to test permissions
    },
    {
      id: 4,
      title: "AWS Console",
      platform: "AWS",
      username: "<EMAIL>",
      password: "••••••••••••",
      actualPassword: "WeakPass",
      team: "Team Name",
      department: "DevOps",
      strength: "Weak Password",
      strengthColor: "bg-red-100 text-red-600 border-red-300",
      authorId: currentUserId,
    },
    {
      id: 5,
      title: "Jira Project",
      platform: "Jira",
      username: "<EMAIL>",
      password: "••••••••••••",
      actualPassword: "AnotherStrongPass789!",
      team: "Team Name",
      department: "Project Management",
      strength: "Strong Password",
      strengthColor: "bg-green-100 text-green-600 border-green-300",
      authorId: 998, // Different author ID to test permissions
    },
    {
      id: 6,
      title: "Office 365",
      platform: "Microsoft 365",
      username: "<EMAIL>",
      password: "••••••••••••",
      actualPassword: "ModerateSecure123",
      team: "Team Name",
      department: "HR",
      strength: "Moderate Password",
      strengthColor: "bg-yellow-100 text-yellow-600 border-yellow-300",
      authorId: currentUserId,
    },
    {
      id: 7,
      title: "Database Admin",
      platform: "MySQL",
      username: "<EMAIL>",
      password: "••••••••••••",
      actualPassword: "VeryWeakPass",
      team: "Team Name",
      department: "Database",
      strength: "Weak Password",
      strengthColor: "bg-red-100 text-red-600 border-red-300",
      authorId: currentUserId,
    },
  ]);

  const [visiblePasswords, setVisiblePasswords] = useState({});
  const [showAddForm, setShowAddForm] = useState(false);
  const [showNewTable, setShowNewTable] = useState(false);
  const [showTeamTable] = useState(true);
  const [newPasswordCards, setNewPasswordCards] = useState([]);
  const [editingRowId, setEditingRowId] = useState(null);
  const [shareableCards, setShareableCards] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: "asc" });
  const togglePasswordVisibility = (id) => {
    setVisiblePasswords((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  // Handle individual row editing
  const handleEdit = (id) => {
    setEditingRowId(editingRowId === id ? null : id);
  };

  // Handle creating new table
  const handleCreateNewTable = () => {
    setShowNewTable(true);
  };

  // Handle delete entire team table - only author can delete
  const handleDeleteTeamTable = () => {
    // Check if current user is author of any cards
    const isAuthor = passwordCards.some(card => card.authorId === currentUserId);

    if (!isAuthor) {
      alert("Only the author can delete the entire table.");
      return;
    }

    // Only proceed if there are selected cards or if user is author
    if (shareableCards.length === 0) {
      alert("Please select at least one row to delete the table.");
      return;
    }

    confirmationAlert({
      onConfirm: () => {
        setPasswordCards([]);
      },
    });
  };

  // Handle select all checkboxes password and
  
  const toggleSelectAll = () => {
    if (shareableCards.length === passwordCards.length) {
      setShareableCards([]);
    } else {
      setShareableCards(passwordCards.map((card) => card.id));
    }
  };

  // Handle sorting
  const handleSort = (key) => {
    let direction = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  };

  // Sort cards based on current sort config
  const getSortedCards = (cards) => {
    if (!sortConfig.key) return cards;

    return [...cards].sort((a, b) => {
      const aValue = a[sortConfig.key].toLowerCase();
      const bValue = b[sortConfig.key].toLowerCase();

      if (sortConfig.direction === "asc") {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });
  };

  // Handle shareable toggle
  const toggleShareable = (id) => {
    setShareableCards((prev) =>
      prev.includes(id) ? prev.filter((cardId) => cardId !== id) : [...prev, id]
    );
  };

  // Handle form submission for new password cards
  const handleAddPasswordCard = (cardData) => {
    const newCard = {
      ...cardData,
      id: Date.now(),
      password: "••••••••••••",
      actualPassword: cardData.password,
      authorId: currentUserId, // Add current user as author
    };
    setNewPasswordCards((prev) => [...prev, newCard]);
    setShowAddForm(false);
  };

  const handleDelete = (id) => {
    confirmationAlert({
      onConfirm: () => {
        setPasswordCards((prev) => prev.filter((card) => card.id !== id));
      },
    });
  };

  // Handle share functionality for department - only author can share
  const handleShare = () => {
    // Check if current user is author of any cards
    const isAuthor = passwordCards.some(card => card.authorId === currentUserId);

    if (!isAuthor) {
      alert("Only the author can share the table.");
      return;
    }

    // Get current user's department or team
    const currentUserDepartment = userData?.departments?.[0]?.name || "IT";

    // Filter cards that belong to the same department and are authored by current user
    const departmentCards = passwordCards.filter(
      (card) => card.department === currentUserDepartment && card.authorId === currentUserId
    );

    if (departmentCards.length === 0) {
      alert("No password cards available to share in your department.");
      return;
    }

    // Create shareable data
    const shareData = {
      title: `${currentUserDepartment} Department Password Cards`,
      cards: departmentCards,
      sharedBy: `${userData?.fname} ${userData?.lname}` || "Current User",
      sharedAt: new Date().toISOString(),
    };

    // For now, copy to clipboard (you can implement actual sharing logic)
    navigator.clipboard
      .writeText(JSON.stringify(shareData, null, 2))
      .then(() => {
        alert(
          `${departmentCards.length} password cards from ${currentUserDepartment} department copied to clipboard!`
        );
      })
      .catch(() => {
        alert("Failed to copy to clipboard. Please try again.");
      });
  };

  // Placeholder avatar images
  const avatarImages = [
    "https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=A",
    "https://via.placeholder.com/32x32/10B981/FFFFFF?text=B",
    "https://via.placeholder.com/32x32/F59E0B/FFFFFF?text=C",
    "https://via.placeholder.com/32x32/EF4444/FFFFFF?text=D",
  ];

  return (
    <div className="bg-white dark:bg-gray-900">
      {showTeamTable && (
        <>
          {/* Header */}
          <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6">
            <div className="flex items-center space-x-4">
              <h2 className="text-left text-2xl font-bold text-gray-900 dark:text-white">
                Teams Password Card
              </h2>

              {/* Share Icon */}
              <button
                onClick={handleShare}
                className="flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <span className="material-symbols-rounded">share</span>
              </button>

              {/* Delete Team Table Icon */}
              <button
                onClick={() => handleDeleteTeamTable()}
                className="flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg"
                title="Delete entire table"
              >
                {/* <span className="material-symbols-rounded">delete</span> */}
                <span className="material-symbols-outlined text-sm">
                  delete
                </span>
              </button>

              {/* User Avatars */}
              <div className="flex -space-x-2">
                {avatarImages.map((avatar, index) => (
                  <img
                    key={index}
                    src={avatar}
                    alt={`User ${index + 1}`}
                    className="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800"
                  />
                ))}
              </div>
            </div>

            <div className="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0">
              {/* Add Password Card Button */}
              <button
                onClick={() => {
                  setEditingRowId(null);
                  setShowAddForm(!showAddForm);
                }}
                className={`w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${
                  showAddForm
                    ? "bg-primary text-white"
                    : "bg-transparent text-primary border-2 border-primary"
                }`}
              >
                <span className="material-symbols-rounded mr-2">add</span>
                Add Password
              </button>
            </div>
          </div>

          {/* Add/Edit Password Form - Embedded */}
          {showAddForm && (
            <AddPasswordCardForm
              onSubmit={handleAddPasswordCard}
              onCancel={() => {
                setShowAddForm(false);
                setEditingRowId(null);
              }}
              generatedPassword={generatedPassword}
              passwordStrength={passwordStrength}
            />
          )}

          {/* Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="w-12 px-4 py-3 text-left">
                    <input
                      id="checkbox-all"
                      type="checkbox"
                      checked={
                        shareableCards.length === passwordCards.length &&
                        passwordCards.length > 0
                      }
                      onChange={toggleSelectAll}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    <div
                      className="flex items-center space-x-2 cursor-pointer"
                      onClick={() => handleSort("title")}
                    >
                      <span>Title</span>
                      <div className="flex flex-col">
                        <span
                          className={`text-xs ${
                            sortConfig.key === "title" &&
                            sortConfig.direction === "asc"
                              ? "text-blue-600"
                              : "text-gray-400"
                          }`}
                        >
                          ▲
                        </span>
                        <span
                          className={`text-xs ${
                            sortConfig.key === "title" &&
                            sortConfig.direction === "desc"
                              ? "text-blue-600"
                              : "text-gray-400"
                          }`}
                        >
                          ▼
                        </span>
                      </div>
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    <div
                      className="flex items-center space-x-2 cursor-pointer"
                      onClick={() => handleSort("username")}
                    >
                      <span>User Name</span>
                      <div className="flex flex-col">
                        <span
                          className={`text-xs ${
                            sortConfig.key === "username" &&
                            sortConfig.direction === "asc"
                              ? "text-blue-600"
                              : "text-gray-400"
                          }`}
                        >
                          ▲
                        </span>
                        <span
                          className={`text-xs ${
                            sortConfig.key === "username" &&
                            sortConfig.direction === "desc"
                              ? "text-blue-600"
                              : "text-gray-400"
                          }`}
                        >
                          ▼
                        </span>
                      </div>
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Password
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Team
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Department
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Level
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {getSortedCards(passwordCards).map((card) => (
                  <tr key={card.id} className="hover:bg-gray-50">
                    <td className="px-4 py-4">
                      <input
                        id={`shareable-${card.id}`}
                        type="checkbox"
                        checked={shareableCards.includes(card.id)}
                        onChange={() => toggleShareable(card.id)}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                      />
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3">
                          TG
                        </div>
                        {editingRowId === card.id ? (
                          <input
                            type="text"
                            defaultValue={card.title}
                            className="font-medium text-gray-900 border rounded px-2 py-1 w-full"
                            onBlur={(e) => {
                              setPasswordCards((prev) =>
                                prev.map((c) =>
                                  c.id === card.id
                                    ? { ...c, title: e.target.value }
                                    : c
                                )
                              );
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                e.target.blur();
                              }
                            }}
                            autoFocus
                          />
                        ) : (
                          <span className="font-medium text-gray-900">
                            {card.title}
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        {editingRowId === card.id ? (
                          <input
                            type="text"
                            defaultValue={card.username}
                            className="text-gray-900 border rounded px-2 py-1 w-full mr-2"
                            onBlur={(e) => {
                              setPasswordCards((prev) =>
                                prev.map((c) =>
                                  c.id === card.id
                                    ? { ...c, username: e.target.value }
                                    : c
                                )
                              );
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                e.target.blur();
                              }
                            }}
                          />
                        ) : (
                          <span className="text-gray-900">{card.username}</span>
                        )}
                        <button
                          onClick={() => copyToClipboard(card.username)}
                          className="ml-2 text-gray-400 hover:text-gray-600"
                        >
                          <span className="material-symbols-rounded text-sm">
                            content_copy
                          </span>
                        </button>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        {editingRowId === card.id ? (
                          <input
                            type="text"
                            defaultValue={card.actualPassword}
                            className="text-gray-900 border rounded px-2 py-1 w-full mr-2"
                            onBlur={(e) => {
                              setPasswordCards((prev) =>
                                prev.map((c) =>
                                  c.id === card.id
                                    ? { ...c, actualPassword: e.target.value }
                                    : c
                                )
                              );
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                e.target.blur();
                              }
                            }}
                          />
                        ) : (
                          <span className="text-gray-900 mr-2">
                            {visiblePasswords[card.id]
                              ? card.actualPassword
                              : card.password}
                          </span>
                        )}
                        <button
                          onClick={() => togglePasswordVisibility(card.id)}
                          className="text-gray-400 hover:text-gray-600 mr-2"
                        >
                          <span className="material-symbols-rounded text-sm">
                            {visiblePasswords[card.id]
                              ? "visibility_off"
                              : "visibility"}
                          </span>
                        </button>
                        <button
                          onClick={() => copyToClipboard(card.actualPassword)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <span className="material-symbols-rounded text-sm">
                            content_copy
                          </span>
                        </button>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      {editingRowId === card.id ? (
                        <input
                          type="text"
                          defaultValue={card.team}
                          className="text-gray-900 border rounded px-2 py-1 w-full"
                          onBlur={(e) => {
                            setPasswordCards((prev) =>
                              prev.map((c) =>
                                c.id === card.id
                                  ? { ...c, team: e.target.value }
                                  : c
                              )
                            );
                          }}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              e.target.blur();
                            }
                          }}
                        />
                      ) : (
                        <span className="text-gray-900">{card.team}</span>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      {editingRowId === card.id ? (
                        <input
                          type="text"
                          defaultValue={card.department}
                          className="text-gray-900 border rounded px-2 py-1 w-full"
                          onBlur={(e) => {
                            setPasswordCards((prev) =>
                              prev.map((c) =>
                                c.id === card.id
                                  ? { ...c, department: e.target.value }
                                  : c
                              )
                            );
                          }}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              e.target.blur();
                            }
                          }}
                        />
                      ) : (
                        <span className="text-gray-900">{card.department}</span>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <span
                        className={`px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`}
                      >
                        {card.strength}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => handleEdit(card.id)}
                          className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                          title="Edit"
                        >
                          <span className="material-symbols-outlined text-lg">stylus_note</span>
                        </button>
                        <button
                          onClick={() => handleDelete(card.id)}
                          className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                          title="Delete"
                        >
                          <span className="material-symbols-outlined text-sm">delete</span>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </>
      )}

      {/* New Password Cards Table */}
      {showNewTable && (
        <div className="mt-8">
          {/* Header */}
          <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6">
            <div className="flex items-center space-x-4">
              <h2 className="text-left text-2xl font-bold text-gray-900 dark:text-white">
                Teams Password Card
              </h2>

              {/* Share Icon */}
              <button
                onClick={handleShare}
                className="flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <span className="material-symbols-rounded">share</span>
              </button>

              {/* Delete Team Table Icon */}
              <button
                onClick={() => handleDeleteTeamTable()}
                className="flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg"
                title="Delete entire table"
              >
                <span className="material-symbols-outlined text-sm">
                  delete
                </span>
              </button>

              {/* User Avatars */}
              <div className="flex -space-x-2">
                {avatarImages.map((avatar, index) => (
                  <img
                    key={index}
                    src={avatar}
                    alt={`User ${index + 1}`}
                    className="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800"
                  />
                ))}
              </div>
            </div>

            <div className="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0">
              {/* Add Password Card Button */}
              <button
                onClick={() => {
                  setEditingRowId(null);
                  setShowAddForm(!showAddForm);
                }}
                className={`w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${
                  showAddForm
                    ? "bg-primary text-white"
                    : "bg-transparent text-primary border-2 border-primary"
                }`}
              >
                <span className="material-symbols-rounded mr-2">add</span>
                Add Password
              </button>
            </div>
          </div>

          {/* Add/Edit Password Form - Embedded */}
          {showAddForm && (
            <AddPasswordCardForm
              onSubmit={handleAddPasswordCard}
              onCancel={() => {
                setShowAddForm(false);
                setEditingRowId(null);
              }}
              generatedPassword={generatedPassword}
              passwordStrength={passwordStrength}
            />
          )}

          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="w-12 px-4 py-3 text-left">
                    <input
                      id="checkbox-all-new"
                      type="checkbox"
                      checked={
                        shareableCards.length === newPasswordCards.length &&
                        newPasswordCards.length > 0
                      }
                      onChange={() => {
                        if (shareableCards.length === newPasswordCards.length) {
                          setShareableCards([]);
                        } else {
                          setShareableCards(newPasswordCards.map((card) => card.id));
                        }
                      }}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    <div
                      className="flex items-center space-x-2 cursor-pointer"
                      onClick={() => handleSort("title")}
                    >
                      <span>Title</span>
                      <div className="flex flex-col">
                        <span
                          className={`text-xs ${
                            sortConfig.key === "title" &&
                            sortConfig.direction === "asc"
                              ? "text-blue-600"
                              : "text-gray-400"
                          }`}
                        >
                          ▲
                        </span>
                        <span
                          className={`text-xs ${
                            sortConfig.key === "title" &&
                            sortConfig.direction === "desc"
                              ? "text-blue-600"
                              : "text-gray-400"
                          }`}
                        >
                          ▼
                        </span>
                      </div>
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    <div
                      className="flex items-center space-x-2 cursor-pointer"
                      onClick={() => handleSort("username")}
                    >
                      <span>User Name</span>
                      <div className="flex flex-col">
                        <span
                          className={`text-xs ${
                            sortConfig.key === "username" &&
                            sortConfig.direction === "asc"
                              ? "text-blue-600"
                              : "text-gray-400"
                          }`}
                        >
                          ▲
                        </span>
                        <span
                          className={`text-xs ${
                            sortConfig.key === "username" &&
                            sortConfig.direction === "desc"
                              ? "text-blue-600"
                              : "text-gray-400"
                          }`}
                        >
                          ▼
                        </span>
                      </div>
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Password
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Team
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Department
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Level
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {newPasswordCards.length === 0 ? (
                  <tr>
                    <td
                      colSpan="8"
                      className="px-6 py-8 text-center text-gray-500"
                    >
                      No password cards added yet. Click "Add Password" to add
                      your first card.
                    </td>
                  </tr>
                ) : (
                  getSortedCards(newPasswordCards).map((card) => (
                    <tr key={card.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4">
                        <input
                          type="checkbox"
                          checked={shareableCards.includes(card.id)}
                          onChange={() => toggleShareable(card.id)}
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3">
                            TG
                          </div>
                          {editingRowId === card.id ? (
                            <input
                              type="text"
                              defaultValue={card.title}
                              className="font-medium text-gray-900 border rounded px-2 py-1 w-full"
                              onBlur={(e) => {
                                setNewPasswordCards((prev) =>
                                  prev.map((c) =>
                                    c.id === card.id
                                      ? { ...c, title: e.target.value }
                                      : c
                                  )
                                );
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  e.target.blur();
                                }
                              }}
                              autoFocus
                            />
                          ) : (
                            <span className="font-medium text-gray-900">
                              {card.title}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          {editingRowId === card.id ? (
                            <input
                              type="text"
                              defaultValue={card.username}
                              className="text-gray-900 border rounded px-2 py-1 w-full mr-2"
                              onBlur={(e) => {
                                setNewPasswordCards((prev) =>
                                  prev.map((c) =>
                                    c.id === card.id
                                      ? { ...c, username: e.target.value }
                                      : c
                                  )
                                );
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  e.target.blur();
                                }
                              }}
                            />
                          ) : (
                            <span className="text-gray-900">{card.username}</span>
                          )}
                          <button
                            onClick={() => copyToClipboard(card.username)}
                            className="ml-2 text-gray-400 hover:text-gray-600"
                          >
                            <span className="material-symbols-rounded text-sm">
                              content_copy
                            </span>
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          {editingRowId === card.id ? (
                            <input
                              type="text"
                              defaultValue={card.actualPassword}
                              className="text-gray-900 border rounded px-2 py-1 w-full mr-2"
                              onBlur={(e) => {
                                setNewPasswordCards((prev) =>
                                  prev.map((c) =>
                                    c.id === card.id
                                      ? { ...c, actualPassword: e.target.value }
                                      : c
                                  )
                                );
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  e.target.blur();
                                }
                              }}
                            />
                          ) : (
                            <span className="text-gray-900 mr-2">
                              {visiblePasswords[card.id]
                                ? card.actualPassword
                                : card.password}
                            </span>
                          )}
                          <button
                            onClick={() => togglePasswordVisibility(card.id)}
                            className="text-gray-400 hover:text-gray-600 mr-2"
                          >
                            <span className="material-symbols-rounded text-sm">
                              {visiblePasswords[card.id]
                                ? "visibility_off"
                                : "visibility"}
                            </span>
                          </button>
                          <button
                            onClick={() => copyToClipboard(card.actualPassword)}
                            className="text-gray-400 hover:text-gray-600"
                          >
                            <span className="material-symbols-rounded text-sm">
                              content_copy
                            </span>
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        {editingRowId === card.id ? (
                          <input
                            type="text"
                            defaultValue={card.team}
                            className="text-gray-900 border rounded px-2 py-1 w-full"
                            onBlur={(e) => {
                              setNewPasswordCards((prev) =>
                                prev.map((c) =>
                                  c.id === card.id
                                    ? { ...c, team: e.target.value }
                                    : c
                                )
                              );
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                e.target.blur();
                              }
                            }}
                          />
                        ) : (
                          <span className="text-gray-900">{card.team}</span>
                        )}
                      </td>
                      <td className="px-6 py-4">
                        {editingRowId === card.id ? (
                          <input
                            type="text"
                            defaultValue={card.department}
                            className="text-gray-900 border rounded px-2 py-1 w-full"
                            onBlur={(e) => {
                              setNewPasswordCards((prev) =>
                                prev.map((c) =>
                                  c.id === card.id
                                    ? { ...c, department: e.target.value }
                                    : c
                                )
                              );
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                e.target.blur();
                              }
                            }}
                          />
                        ) : (
                          <span className="text-gray-900">{card.department}</span>
                        )}
                      </td>
                      <td className="px-6 py-4">
                        <span
                          className={`px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`}
                        >
                          {card.strength}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center space-x-1">
                          <button
                            onClick={() => handleEdit(card.id)}
                            className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                            title="Edit"
                          >
                            <span className="material-symbols-outlined text-lg">stylus_note</span>
                          </button>
                          <button
                            onClick={() => {
                              confirmationAlert({
                                onConfirm: () => {
                                  setNewPasswordCards((prev) =>
                                    prev.filter((c) => c.id !== card.id)
                                  );
                                },
                              });
                            }}
                            className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                            title="Delete"
                          >
                            <span className="material-symbols-outlined text-sm">delete</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Add New Password Card Button (Bottom) */}
      <div className="flex justify-center mt-6">
        <button
          onClick={handleCreateNewTable}
          className="flex items-center justify-center px-6 py-2 text-sm font-medium text-white rounded-lg bg-primary hover:bg-primary/90 focus:ring-4 focus:ring-primary/30 transition-colors duration-200 focus:outline-none"
        >
          <span className="material-symbols-rounded mr-2">add</span>
          Add New Password Card
        </button>
      </div>
    </div>
  );
};

export default PasswordCardsTable;
