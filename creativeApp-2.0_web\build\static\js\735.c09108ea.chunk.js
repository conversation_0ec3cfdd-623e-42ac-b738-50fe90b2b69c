"use strict";(self.webpackChunkcreativeapp=self.webpackChunkcreativeapp||[]).push([[735],{45735:(e,s,t)=>{t.r(s),t.d(s,{default:()=>m});var r=t(65043),a=t(28352),l=t(32029),n=t(70579);const c=e=>{let{title:s,teamLead:t,members:r,liveMembers:a,benchMembers:l,resourceCounts:c,shifts:o,logo:m}=e;return(0,n.jsxs)("div",{className:"p-6 rounded-lg mb-8",children:[(0,n.jsxs)("div",{className:"flex items-center gap-4 whitespace-nowrap",children:[(0,n.jsxs)("p",{className:"font-normal text-lg",children:["Team: ",(0,n.jsx)("span",{className:"text-primary",children:s})]}),(0,n.jsx)("span",{className:"font-normal text-lg",children:"|"}),(0,n.jsxs)("p",{className:"font-normal text-lg",children:["Team Lead: ",(0,n.jsx)("span",{className:"text-primary",children:t})]}),(0,n.jsx)("span",{className:"font-normal text-lg",children:"|"}),(0,n.jsxs)("p",{className:"font-normal text-lg",children:["Total Members: ",(0,n.jsx)("span",{className:"text-primary",children:r})]}),(0,n.jsxs)("p",{className:"font-normal text-lg",children:["Live Members: ",(0,n.jsx)("span",{className:"text-primary",children:a})]}),(0,n.jsxs)("p",{className:"font-normal text-lg",children:["Bench Members: ",(0,n.jsx)("span",{className:"text-primary",children:l})]}),(0,n.jsxs)("p",{className:"font-normal text-lg",children:["Total Designer: ",(0,n.jsx)("span",{className:"text-primary",children:c.Designer||0})]}),(0,n.jsxs)("p",{className:"font-normal text-lg",children:["Total Developer: ",(0,n.jsx)("span",{className:"text-primary",children:c.Developer||0})]}),(0,n.jsxs)("p",{className:"font-normal text-lg",children:["Total QA: ",(0,n.jsx)("span",{className:"text-primary",children:c.QA||0})]})]}),(0,n.jsxs)("div",{className:"flex flex-row gap-2 mt-2",children:[(0,n.jsxs)("div",{className:"w-[220px] p-4 rounded-lg text-center font-bold text-gray-700 shadow-md flex flex-col items-center justify-center border",children:[(0,n.jsx)("h5",{className:"text-lg",children:s}),(0,n.jsx)("img",{src:m,alt:s,className:"w-16 h-16 rounded-full my-2"}),(0,n.jsxs)("p",{className:"font-normal text-lg",children:["Total Members: ",(0,n.jsx)("span",{className:"text-primary",children:r})]})]}),(0,n.jsx)("div",{className:"flex flex-col w-full gap-2 mt-2",children:Object.keys(o).map(((e,s)=>(0,n.jsx)(i,{title:e,...o[e]},s)))})]})]})},i=e=>{let{title:s,time:t,resourceCounts:r}=e;return(0,n.jsx)("div",{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsxs)("div",{className:"w-[209px] h-[77px] p-4 rounded-lg text-center font-bold text-gray-700 shadow-md border",children:[(0,n.jsx)("h5",{className:"text-sm",children:s}),(0,n.jsxs)("p",{className:"text-xs font-normal text-gray-600",children:["\u23f0 ",t]})]}),(0,n.jsx)("div",{className:"grid grid-cols-4 gap-4 w-full",children:Object.entries(r||{}).map((e=>{let[s,t]=e;return(0,n.jsx)(o,{title:`Total ${s}`,count:t},s)}))})]})})},o=e=>{let{title:s,count:t}=e;return(0,n.jsxs)("div",{className:"h-[78px] p-4 rounded-lg text-center shadow-md border",children:[(0,n.jsx)("span",{className:"text-sm",children:s}),(0,n.jsxs)("p",{className:"text-base font-bold",children:[" \ud83d\udc65 ",t]})]})},m=()=>{const e=localStorage.getItem("token"),{data:s,error:t}=(0,a.A)(`${l.H}users`,e);(0,r.useEffect)((()=>{t&&console.error("API Error:",t)}),[t]);const i=e=>{if(!e)return"";const[s,t]=e.split(":"),r=new Date;return r.setHours(s,t),r.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!0})},o=(0,r.useMemo)((()=>{if(!Array.isArray(s)||0===s.length)return{};const e={};return s.forEach((s=>{Array.isArray(s.teams)&&s.teams.forEach((t=>{var r;const a=t.name;e[a]||(e[a]={logo:t.logo?`${l.H}${t.logo}`:"/assets/images/default-logo.png",teamLead:t.poc||"Not Assigned",members:0,liveMembers:0,benchMembers:0,resourceCounts:{},shifts:{}}),e[a].members++;(null===(r=s.member_statuses)||void 0===r?void 0:r.some((e=>"Live"===e.name)))?e[a].liveMembers++:e[a].benchMembers++,Array.isArray(s.schedules)&&s.schedules.forEach((t=>{const r=t.shift_name;e[a].shifts[r]||(e[a].shifts[r]={time:t.shift_start&&t.shift_end?`${i(t.shift_start)} - ${i(t.shift_end)}`:"Shift time not available",resourceCounts:{}}),Array.isArray(s.resource_types)&&s.resource_types.forEach((s=>{const t=s.name||"Other";e[a].shifts[r].resourceCounts[t]=(e[a].shifts[r].resourceCounts[t]||0)+1,e[a].resourceCounts[t]=(e[a].resourceCounts[t]||0)+1}))}))}))})),e}),[s]);return(0,n.jsx)("div",{className:"p-6",children:0===Object.keys(o).length?(0,n.jsx)("p",{className:"text-center text-gray-500",children:"No teams available"}):Object.keys(o).map(((e,s)=>(0,n.jsx)(c,{title:e,...o[e]},s)))})}}}]);
//# sourceMappingURL=735.c09108ea.chunk.js.map