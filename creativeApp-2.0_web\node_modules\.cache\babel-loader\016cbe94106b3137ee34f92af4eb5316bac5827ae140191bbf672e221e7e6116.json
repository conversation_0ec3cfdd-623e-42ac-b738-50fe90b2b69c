{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\components\\\\password-manager\\\\AddPasswordCardForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddPasswordCardForm = ({\n  onSubmit,\n  onCancel,\n  generatedPassword,\n  passwordStrength\n}) => {\n  _s();\n  var _teamsByDepartment$fo;\n  const [formData, setFormData] = useState({\n    title: '',\n    username: '',\n    password: '',\n    team: '',\n    department: '',\n    strength: 'Weak Password'\n  });\n\n  // Department and Team options\n  const departments = ['IT', ' Creative Development', 'DevOps', 'Project Management', 'HR', 'Database', 'Marketing', 'Sales'];\n  const teamsByDepartment = {\n    'IT': ['IT Support', 'System Admin', 'Network Team'],\n    ' Creative Development': ['Frontend Team', 'Backend Team', 'Full Stack Team'],\n    'DevOps': ['Infrastructure Team', 'CI/CD Team', 'Cloud Team'],\n    'Project Management': ['Scrum Masters', 'Product Owners', 'PMO'],\n    'HR': ['Recruitment', 'Employee Relations', 'Payroll'],\n    'Database': ['DBA Team', 'Data Analytics', 'Data Engineering'],\n    'Marketing': ['Digital Marketing', 'Content Team', 'SEO Team'],\n    'Sales': ['Inside Sales', 'Field Sales', 'Sales Support']\n  };\n\n  //state for showing then password and form errors\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Update form when generated password changes\n  useEffect(() => {\n    if (generatedPassword) {\n      setFormData(prev => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength\n      }));\n    }\n  }, [generatedPassword, passwordStrength]);\n  //Handles input changes for all form fields\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Reset team when department changes\n    if (name === 'department') {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value,\n        team: ''\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Auto-calculate password strength when password changes\n    if (name === 'password') {\n      const strength = calculatePasswordStrength(value);\n      setFormData(prev => ({\n        ...prev,\n        strength: strength\n      }));\n    }\n  };\n  const calculatePasswordStrength = password => {\n    if (!password) return 'Weak Password';\n    let score = 0;\n\n    // Length check\n    if (password.length >= 12) score += 2;else if (password.length >= 8) score += 1;\n\n    // Character variety checks\n    if (/[a-z]/.test(password)) score += 1;\n    if (/[A-Z]/.test(password)) score += 1;\n    if (/[0-9]/.test(password)) score += 1;\n    if (/[^A-Za-z0-9]/.test(password)) score += 1;\n\n    // Additional complexity\n    if (password.length >= 16) score += 1;\n    if (score >= 6) return 'Strong Password';\n    if (score >= 4) return 'Moderate Password';\n    return 'Weak Password';\n  };\n\n  //Returns css classes based on password strer\n  const getStrengthColor = strength => {\n    switch (strength) {\n      case 'Strong Password':\n        return 'bg-green-100 text-green-600 border-green-300';\n      case 'Moderate Password':\n        return 'bg-yellow-100 text-yellow-600 border-yellow-300';\n      case 'Weak Password':\n        return 'bg-red-100 text-red-600 border-red-300';\n      default:\n        return 'bg-gray-100 text-gray-600 border-gray-300';\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.title.trim()) {\n      newErrors.title = 'Title is required';\n    }\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    }\n    if (!formData.password.trim()) {\n      newErrors.password = 'Password is required';\n    }\n    if (!formData.team.trim()) {\n      newErrors.team = 'Team is required';\n    }\n    if (!formData.department.trim()) {\n      newErrors.department = 'Department is required';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (validateForm()) {\n      // Add strength color for display\n      const strengthColor = getStrengthColor(formData.strength);\n      const cardData = {\n        ...formData,\n        strengthColor\n      };\n      onSubmit(cardData);\n\n      // Reset form\n      setFormData({\n        title: '',\n        username: '',\n        password: '',\n        team: '',\n        department: '',\n        strength: 'Weak Password'\n      });\n    }\n  };\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n  const useGeneratedPassword = () => {\n    if (generatedPassword) {\n      setFormData(prev => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"/ rounded-lg shadow-md w-full max-w-4xl relative bg-neutral-50 m-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-4 bg-gray-100 px-4 py-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-medium text-left text-gray-800\",\n          children: \"Add New Password Card\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-3xl text-gray-500 hover:text-gray-800\",\n          onClick: onCancel,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-4 p-6 overflow-y-auto max-h-[80vh] scrollbar-vertical\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 w-full md:max-w-[48%] text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"title\",\n              className: \"block pb-2 text-base text-gray-600\",\n              children: [\"Platform/Service Title \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-red-600\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 40\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"title\",\n              name: \"title\",\n              value: formData.title,\n              onChange: handleInputChange,\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.title ? 'border-red-500' : 'border-gray-300'}`,\n              placeholder: \"e.g., Gmail, GitHub, AWS Console\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), errors.title && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 32\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 w-full md:max-w-[48%] text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"username\",\n              className: \"block pb-2 text-base text-gray-600\",\n              children: [\"Username/Email \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-red-600\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 32\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"username\",\n              name: \"username\",\n              value: formData.username,\n              onChange: handleInputChange,\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.username ? 'border-red-500' : 'border-gray-300'}`,\n              placeholder: \"Enter username or email address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), errors.username && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 w-full md:max-w-[48%] text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"block pb-2 text-base text-gray-600\",\n              children: [\"Password \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-red-600\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 26\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: showPassword ? \"text\" : \"password\",\n                id: \"password\",\n                name: \"password\",\n                value: formData.password,\n                onChange: handleInputChange,\n                className: `w-full px-3 py-2 pr-24 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.password ? 'border-red-500' : 'border-gray-300'}`,\n                placeholder: \"Enter password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 right-0 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: useGeneratedPassword,\n                  className: \"px-2 text-xs text-blue-600 hover:text-blue-800 border-r border-gray-300\",\n                  title: \"Use generated password from above\",\n                  children: \"Use Gen\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: togglePasswordVisibility,\n                  className: \"pr-3 pl-2 flex items-center text-gray-400 hover:text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-symbols-rounded text-sm\",\n                    children: showPassword ? 'visibility_off' : 'visibility'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.password\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 35\n            }, this), formData.password && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-block px-3 py-1 text-xs font-medium rounded-full border ${getStrengthColor(formData.strength)}`,\n                children: formData.strength\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 w-full md:max-w-[48%] text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"department\",\n              className: \"block pb-2 text-base text-gray-600\",\n              children: [\"Department \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-red-600\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 28\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"department\",\n              name: \"department\",\n              value: formData.department,\n              onChange: handleInputChange,\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.department ? 'border-red-500' : 'border-gray-300'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Department\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), departments.map(dept => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: dept,\n                children: dept\n              }, dept, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this), errors.department && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.department\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 w-full md:max-w-[48%] text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"team\",\n              className: \"block pb-2 text-base text-gray-600\",\n              children: [\"Team \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-red-600\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 22\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"team\",\n              name: \"team\",\n              value: formData.team,\n              onChange: handleInputChange,\n              disabled: !formData.department,\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.team ? 'border-red-500' : 'border-gray-300'} ${!formData.department ? 'bg-gray-100 cursor-not-allowed' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this), formData.department && ((_teamsByDepartment$fo = teamsByDepartment[formData.department]) === null || _teamsByDepartment$fo === void 0 ? void 0 : _teamsByDepartment$fo.map(team => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: team,\n                children: team\n              }, team, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this)))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this), errors.team && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.team\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 31\n            }, this), !formData.department && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-500\",\n              children: \"Please select a department first\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-4 p-6 bg-gray-50 border-t\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: onCancel,\n            className: \"px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n            children: \"Save Password Card\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 191,\n    columnNumber: 5\n  }, this);\n};\n_s(AddPasswordCardForm, \"F1Olx8D+5A6wCuBFI1UfhgP0Hgw=\");\n_c = AddPasswordCardForm;\nexport default AddPasswordCardForm;\nvar _c;\n$RefreshReg$(_c, \"AddPasswordCardForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AddPasswordCardForm", "onSubmit", "onCancel", "generatedPassword", "passwordStrength", "_s", "_teamsByDepartment$fo", "formData", "setFormData", "title", "username", "password", "team", "department", "strength", "departments", "teamsByDepartment", "showPassword", "setShowPassword", "errors", "setErrors", "prev", "handleInputChange", "e", "name", "value", "target", "calculatePasswordStrength", "score", "length", "test", "getStrengthColor", "validateForm", "newErrors", "trim", "Object", "keys", "handleSubmit", "preventDefault", "strengthColor", "cardData", "togglePasswordVisibility", "useGeneratedPassword", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "htmlFor", "type", "id", "onChange", "placeholder", "map", "dept", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/AddPasswordCardForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst AddPasswordCardForm = ({ onSubmit, onCancel, generatedPassword, passwordStrength }) => {\n  const [formData, setFormData] = useState({\n    title: '',\n    username: '',\n    password: '',\n    team: '',\n    department: '',\n    strength: 'Weak Password'\n  });\n\n  // Department and Team options\n  const departments = [\n    'IT',\n    ' Creative Development',\n    'DevOps',\n    'Project Management',\n    'HR',\n    'Database',\n    'Marketing',\n    'Sales'\n  ];\n\n  const teamsByDepartment = {\n    'IT': ['IT Support', 'System Admin', 'Network Team'],\n    ' Creative Development': ['Frontend Team', 'Backend Team', 'Full Stack Team'],\n    'DevOps': ['Infrastructure Team', 'CI/CD Team', 'Cloud Team'],\n    'Project Management': ['Scrum Masters', 'Product Owners', 'PMO'],\n    'HR': ['Recruitment', 'Employee Relations', 'Payroll'],\n    'Database': ['DBA Team', 'Data Analytics', 'Data Engineering'],\n    'Marketing': ['Digital Marketing', 'Content Team', 'SEO Team'],\n    'Sales': ['Inside Sales', 'Field Sales', 'Sales Support']\n  };\n\n\n  //state for showing then password and form errors\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Update form when generated password changes\n  useEffect(() => {\n    if (generatedPassword) {\n      setFormData(prev => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength\n      }));\n    }\n  }, [generatedPassword, passwordStrength]);\n  //Handles input changes for all form fields\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n\n    // Reset team when department changes\n    if (name === 'department') {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value,\n        team: ''\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Auto-calculate password strength when password changes\n    if (name === 'password') {\n      const strength = calculatePasswordStrength(value);\n      setFormData(prev => ({\n        ...prev,\n        strength: strength\n      }));\n    }\n  };\n\n  const calculatePasswordStrength = (password) => {\n    if (!password) return 'Weak Password';\n    \n    let score = 0;\n    \n    // Length check\n    if (password.length >= 12) score += 2;\n    else if (password.length >= 8) score += 1;\n    \n    // Character variety checks\n    if (/[a-z]/.test(password)) score += 1;\n    if (/[A-Z]/.test(password)) score += 1;\n    if (/[0-9]/.test(password)) score += 1;\n    if (/[^A-Za-z0-9]/.test(password)) score += 1;\n    \n    // Additional complexity\n    if (password.length >= 16) score += 1;\n    \n    if (score >= 6) return 'Strong Password';\n    if (score >= 4) return 'Moderate Password';\n    return 'Weak Password';\n  };\n\n  //Returns css classes based on password strer\n  const getStrengthColor = (strength) => {\n    switch (strength) {\n      case 'Strong Password':\n        return 'bg-green-100 text-green-600 border-green-300';\n      case 'Moderate Password':\n        return 'bg-yellow-100 text-yellow-600 border-yellow-300';\n      case 'Weak Password':\n        return 'bg-red-100 text-red-600 border-red-300';\n      default:\n        return 'bg-gray-100 text-gray-600 border-gray-300';\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n    \n    if (!formData.title.trim()) {\n      newErrors.title = 'Title is required';\n    }\n    \n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    }\n    \n    if (!formData.password.trim()) {\n      newErrors.password = 'Password is required';\n    }\n    \n    if (!formData.team.trim()) {\n      newErrors.team = 'Team is required';\n    }\n    \n    if (!formData.department.trim()) {\n      newErrors.department = 'Department is required';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    if (validateForm()) {\n      // Add strength color for display\n      const strengthColor = getStrengthColor(formData.strength);\n      const cardData = {\n        ...formData,\n        strengthColor\n      };\n      \n      onSubmit(cardData);\n      \n      // Reset form\n      setFormData({\n        title: '',\n        username: '',\n        password: '',\n        team: '',\n        department: '',\n        strength: 'Weak Password'\n      });\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  const useGeneratedPassword = () => {\n    if (generatedPassword) {\n      setFormData(prev => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength\n      }));\n    }\n  };\n\n  return (\n    <div className=\"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\">\n      <div className=\"/ rounded-lg shadow-md w-full max-w-4xl relative bg-neutral-50 m-4\">\n        <div className=\"flex justify-between items-center mb-4 bg-gray-100 px-4 py-3\">\n          <h4 className=\"text-lg font-medium text-left text-gray-800\">Add New Password Card</h4>\n          <button\n            className=\"text-3xl text-gray-500 hover:text-gray-800\"\n            onClick={onCancel}\n          >\n            &times;\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit}>\n          <div className='flex flex-wrap gap-4 p-6 overflow-y-auto max-h-[80vh] scrollbar-vertical'>\n            \n            {/* Title */}\n            <div className=\"mb-4 w-full md:max-w-[48%] text-left\">\n              <label htmlFor=\"title\" className=\"block pb-2 text-base text-gray-600\">\n                Platform/Service Title <span className='text-red-600'>*</span>\n              </label>\n              <input\n                type=\"text\"\n                id=\"title\"\n                name=\"title\"\n                value={formData.title}\n                onChange={handleInputChange}\n                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                  errors.title ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"e.g., Gmail, GitHub, AWS Console\"\n              />\n              {errors.title && <p className=\"mt-1 text-sm text-red-600\">{errors.title}</p>}\n            </div>\n\n            {/* Username */}\n            <div className=\"mb-4 w-full md:max-w-[48%] text-left\">\n              <label htmlFor=\"username\" className=\"block pb-2 text-base text-gray-600\">\n                Username/Email <span className='text-red-600'>*</span>\n              </label>\n              <input\n                type=\"text\"\n                id=\"username\"\n                name=\"username\"\n                value={formData.username}\n                onChange={handleInputChange}\n                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                  errors.username ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"Enter username or email address\"\n              />\n              {errors.username && <p className=\"mt-1 text-sm text-red-600\">{errors.username}</p>}\n            </div>\n\n            {/* Password */}\n            <div className=\"mb-4 w-full md:max-w-[48%] text-left\">\n              <label htmlFor=\"password\" className=\"block pb-2 text-base text-gray-600\">\n                Password <span className='text-red-600'>*</span>\n              </label>\n              <div className=\"relative\">\n                <input\n                  type={showPassword ? \"text\" : \"password\"}\n                  id=\"password\"\n                  name=\"password\"\n                  value={formData.password}\n                  onChange={handleInputChange}\n                  className={`w-full px-3 py-2 pr-24 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                    errors.password ? 'border-red-500' : 'border-gray-300'\n                  }`}\n                  placeholder=\"Enter password\"\n                />\n                <div className=\"absolute inset-y-0 right-0 flex items-center\">\n                  <button\n                    type=\"button\"\n                    onClick={useGeneratedPassword}\n                    className=\"px-2 text-xs text-blue-600 hover:text-blue-800 border-r border-gray-300\"\n                    title=\"Use generated password from above\"\n                  >\n                    Use Gen\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={togglePasswordVisibility}\n                    className=\"pr-3 pl-2 flex items-center text-gray-400 hover:text-gray-600\"\n                  >\n                    <span className=\"material-symbols-rounded text-sm\">\n                      {showPassword ? 'visibility_off' : 'visibility'}\n                    </span>\n                  </button>\n                </div>\n              </div>\n              {errors.password && <p className=\"mt-1 text-sm text-red-600\">{errors.password}</p>}\n              \n              {/* Password Strength Indicator */}\n              {formData.password && (\n                <div className=\"mt-2\">\n                  <span className={`inline-block px-3 py-1 text-xs font-medium rounded-full border ${getStrengthColor(formData.strength)}`}>\n                    {formData.strength}\n                  </span>\n                </div>\n              )}\n            </div>\n\n            {/* Department */}\n            <div className=\"mb-4 w-full md:max-w-[48%] text-left\">\n              <label htmlFor=\"department\" className=\"block pb-2 text-base text-gray-600\">\n                Department <span className='text-red-600'>*</span>\n              </label>\n              <select\n                id=\"department\"\n                name=\"department\"\n                value={formData.department}\n                onChange={handleInputChange}\n                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                  errors.department ? 'border-red-500' : 'border-gray-300'\n                }`}\n              >\n                <option value=\"\">Select Department</option>\n                {departments.map(dept => (\n                  <option key={dept} value={dept}>{dept}</option>\n                ))}\n              </select>\n              {errors.department && <p className=\"mt-1 text-sm text-red-600\">{errors.department}</p>}\n            </div>\n\n            {/* Team */}\n            <div className=\"mb-4 w-full md:max-w-[48%] text-left\">\n              <label htmlFor=\"team\" className=\"block pb-2 text-base text-gray-600\">\n                Team <span className='text-red-600'>*</span>\n              </label>\n              <select\n                id=\"team\"\n                name=\"team\"\n                value={formData.team}\n                onChange={handleInputChange}\n                disabled={!formData.department}\n                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                  errors.team ? 'border-red-500' : 'border-gray-300'\n                } ${!formData.department ? 'bg-gray-100 cursor-not-allowed' : ''}`}\n              >\n                <option value=\"\">Select Team</option>\n                {formData.department && teamsByDepartment[formData.department]?.map(team => (\n                  <option key={team} value={team}>{team}</option>\n                ))}\n              </select>\n              {errors.team && <p className=\"mt-1 text-sm text-red-600\">{errors.team}</p>}\n              {!formData.department && (\n                <p className=\"mt-1 text-sm text-gray-500\">Please select a department first</p>\n              )}\n            </div>\n\n            {/* Strength Level (Read-only, auto-calculated) */}\n            {/* <div className=\"mb-4 w-full text-left\">\n              <label htmlFor=\"strength\" className=\"block pb-2 text-base text-gray-600\">\n                Password Strength Level\n              </label>\n              <div className={`w-full px-3 py-2 border rounded-md cursor-not-allowed ${getStrengthColor(formData.strength)}`}>\n                {formData.strength}\n              </div>\n              <p className=\"mt-1 text-xs text-gray-500\">\n                Strength is automatically calculated based on your password complexity\n              </p>\n            </div> */}\n\n          </div>\n\n          {/* Submit Buttons */}\n          <div className=\"flex justify-end space-x-4 p-6 bg-gray-50 border-t\">\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              className=\"px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              className=\"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n            >\n              Save Password Card\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default AddPasswordCardForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,iBAAiB;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAC3F,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC;IACvCa,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMC,WAAW,GAAG,CAClB,IAAI,EACJ,uBAAuB,EACvB,QAAQ,EACR,oBAAoB,EACpB,IAAI,EACJ,UAAU,EACV,WAAW,EACX,OAAO,CACR;EAED,MAAMC,iBAAiB,GAAG;IACxB,IAAI,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC;IACpD,uBAAuB,EAAE,CAAC,eAAe,EAAE,cAAc,EAAE,iBAAiB,CAAC;IAC7E,QAAQ,EAAE,CAAC,qBAAqB,EAAE,YAAY,EAAE,YAAY,CAAC;IAC7D,oBAAoB,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,KAAK,CAAC;IAChE,IAAI,EAAE,CAAC,aAAa,EAAE,oBAAoB,EAAE,SAAS,CAAC;IACtD,UAAU,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,kBAAkB,CAAC;IAC9D,WAAW,EAAE,CAAC,mBAAmB,EAAE,cAAc,EAAE,UAAU,CAAC;IAC9D,OAAO,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,eAAe;EAC1D,CAAC;;EAGD;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuB,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd,IAAIM,iBAAiB,EAAE;MACrBK,WAAW,CAACa,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPV,QAAQ,EAAER,iBAAiB;QAC3BW,QAAQ,EAAEV;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACD,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;EACzC;EACA,MAAMkB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA,IAAIF,IAAI,KAAK,YAAY,EAAE;MACzBhB,WAAW,CAACa,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACG,IAAI,GAAGC,KAAK;QACbb,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLJ,WAAW,CAACa,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACG,IAAI,GAAGC;MACV,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAIN,MAAM,CAACK,IAAI,CAAC,EAAE;MAChBJ,SAAS,CAACC,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACG,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAIA,IAAI,KAAK,UAAU,EAAE;MACvB,MAAMV,QAAQ,GAAGa,yBAAyB,CAACF,KAAK,CAAC;MACjDjB,WAAW,CAACa,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPP,QAAQ,EAAEA;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMa,yBAAyB,GAAIhB,QAAQ,IAAK;IAC9C,IAAI,CAACA,QAAQ,EAAE,OAAO,eAAe;IAErC,IAAIiB,KAAK,GAAG,CAAC;;IAEb;IACA,IAAIjB,QAAQ,CAACkB,MAAM,IAAI,EAAE,EAAED,KAAK,IAAI,CAAC,CAAC,KACjC,IAAIjB,QAAQ,CAACkB,MAAM,IAAI,CAAC,EAAED,KAAK,IAAI,CAAC;;IAEzC;IACA,IAAI,OAAO,CAACE,IAAI,CAACnB,QAAQ,CAAC,EAAEiB,KAAK,IAAI,CAAC;IACtC,IAAI,OAAO,CAACE,IAAI,CAACnB,QAAQ,CAAC,EAAEiB,KAAK,IAAI,CAAC;IACtC,IAAI,OAAO,CAACE,IAAI,CAACnB,QAAQ,CAAC,EAAEiB,KAAK,IAAI,CAAC;IACtC,IAAI,cAAc,CAACE,IAAI,CAACnB,QAAQ,CAAC,EAAEiB,KAAK,IAAI,CAAC;;IAE7C;IACA,IAAIjB,QAAQ,CAACkB,MAAM,IAAI,EAAE,EAAED,KAAK,IAAI,CAAC;IAErC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,iBAAiB;IACxC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,mBAAmB;IAC1C,OAAO,eAAe;EACxB,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAIjB,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,iBAAiB;QACpB,OAAO,8CAA8C;MACvD,KAAK,mBAAmB;QACtB,OAAO,iDAAiD;MAC1D,KAAK,eAAe;QAClB,OAAO,wCAAwC;MACjD;QACE,OAAO,2CAA2C;IACtD;EACF,CAAC;EAED,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAC1B,QAAQ,CAACE,KAAK,CAACyB,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACxB,KAAK,GAAG,mBAAmB;IACvC;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,CAACwB,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACvB,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,CAACuB,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACtB,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAACsB,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACrB,IAAI,GAAG,kBAAkB;IACrC;IAEA,IAAI,CAACL,QAAQ,CAACM,UAAU,CAACqB,IAAI,CAAC,CAAC,EAAE;MAC/BD,SAAS,CAACpB,UAAU,GAAG,wBAAwB;IACjD;IAEAO,SAAS,CAACa,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACJ,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMQ,YAAY,GAAId,CAAC,IAAK;IAC1BA,CAAC,CAACe,cAAc,CAAC,CAAC;IAElB,IAAIN,YAAY,CAAC,CAAC,EAAE;MAClB;MACA,MAAMO,aAAa,GAAGR,gBAAgB,CAACxB,QAAQ,CAACO,QAAQ,CAAC;MACzD,MAAM0B,QAAQ,GAAG;QACf,GAAGjC,QAAQ;QACXgC;MACF,CAAC;MAEDtC,QAAQ,CAACuC,QAAQ,CAAC;;MAElB;MACAhC,WAAW,CAAC;QACVC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,EAAE;QACRC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM2B,wBAAwB,GAAGA,CAAA,KAAM;IACrCvB,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,MAAMyB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIvC,iBAAiB,EAAE;MACrBK,WAAW,CAACa,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPV,QAAQ,EAAER,iBAAiB;QAC3BW,QAAQ,EAAEV;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,oBACEL,OAAA;IAAK4C,SAAS,EAAC,kHAAkH;IAAAC,QAAA,eAC/H7C,OAAA;MAAK4C,SAAS,EAAC,oEAAoE;MAAAC,QAAA,gBACjF7C,OAAA;QAAK4C,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3E7C,OAAA;UAAI4C,SAAS,EAAC,6CAA6C;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtFjD,OAAA;UACE4C,SAAS,EAAC,4CAA4C;UACtDM,OAAO,EAAE/C,QAAS;UAAA0C,QAAA,EACnB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENjD,OAAA;QAAME,QAAQ,EAAEoC,YAAa;QAAAO,QAAA,gBAC3B7C,OAAA;UAAK4C,SAAS,EAAC,0EAA0E;UAAAC,QAAA,gBAGvF7C,OAAA;YAAK4C,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACnD7C,OAAA;cAAOmD,OAAO,EAAC,OAAO;cAACP,SAAS,EAAC,oCAAoC;cAAAC,QAAA,GAAC,yBAC7C,eAAA7C,OAAA;gBAAM4C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACRjD,OAAA;cACEoD,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,OAAO;cACV5B,IAAI,EAAC,OAAO;cACZC,KAAK,EAAElB,QAAQ,CAACE,KAAM;cACtB4C,QAAQ,EAAE/B,iBAAkB;cAC5BqB,SAAS,EAAE,0HACTxB,MAAM,CAACV,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,EAClD;cACH6C,WAAW,EAAC;YAAkC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,EACD7B,MAAM,CAACV,KAAK,iBAAIV,OAAA;cAAG4C,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAEzB,MAAM,CAACV;YAAK;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAGNjD,OAAA;YAAK4C,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACnD7C,OAAA;cAAOmD,OAAO,EAAC,UAAU;cAACP,SAAS,EAAC,oCAAoC;cAAAC,QAAA,GAAC,iBACxD,eAAA7C,OAAA;gBAAM4C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACRjD,OAAA;cACEoD,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,UAAU;cACb5B,IAAI,EAAC,UAAU;cACfC,KAAK,EAAElB,QAAQ,CAACG,QAAS;cACzB2C,QAAQ,EAAE/B,iBAAkB;cAC5BqB,SAAS,EAAE,0HACTxB,MAAM,CAACT,QAAQ,GAAG,gBAAgB,GAAG,iBAAiB,EACrD;cACH4C,WAAW,EAAC;YAAiC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,EACD7B,MAAM,CAACT,QAAQ,iBAAIX,OAAA;cAAG4C,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAEzB,MAAM,CAACT;YAAQ;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eAGNjD,OAAA;YAAK4C,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACnD7C,OAAA;cAAOmD,OAAO,EAAC,UAAU;cAACP,SAAS,EAAC,oCAAoC;cAAAC,QAAA,GAAC,WAC9D,eAAA7C,OAAA;gBAAM4C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACRjD,OAAA;cAAK4C,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB7C,OAAA;gBACEoD,IAAI,EAAElC,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzCmC,EAAE,EAAC,UAAU;gBACb5B,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAElB,QAAQ,CAACI,QAAS;gBACzB0C,QAAQ,EAAE/B,iBAAkB;gBAC5BqB,SAAS,EAAE,gIACTxB,MAAM,CAACR,QAAQ,GAAG,gBAAgB,GAAG,iBAAiB,EACrD;gBACH2C,WAAW,EAAC;cAAgB;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACFjD,OAAA;gBAAK4C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,gBAC3D7C,OAAA;kBACEoD,IAAI,EAAC,QAAQ;kBACbF,OAAO,EAAEP,oBAAqB;kBAC9BC,SAAS,EAAC,yEAAyE;kBACnFlC,KAAK,EAAC,mCAAmC;kBAAAmC,QAAA,EAC1C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTjD,OAAA;kBACEoD,IAAI,EAAC,QAAQ;kBACbF,OAAO,EAAER,wBAAyB;kBAClCE,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,eAEzE7C,OAAA;oBAAM4C,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC/C3B,YAAY,GAAG,gBAAgB,GAAG;kBAAY;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACL7B,MAAM,CAACR,QAAQ,iBAAIZ,OAAA;cAAG4C,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAEzB,MAAM,CAACR;YAAQ;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAGjFzC,QAAQ,CAACI,QAAQ,iBAChBZ,OAAA;cAAK4C,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB7C,OAAA;gBAAM4C,SAAS,EAAE,kEAAkEZ,gBAAgB,CAACxB,QAAQ,CAACO,QAAQ,CAAC,EAAG;gBAAA8B,QAAA,EACtHrC,QAAQ,CAACO;cAAQ;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNjD,OAAA;YAAK4C,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACnD7C,OAAA;cAAOmD,OAAO,EAAC,YAAY;cAACP,SAAS,EAAC,oCAAoC;cAAAC,QAAA,GAAC,aAC9D,eAAA7C,OAAA;gBAAM4C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACRjD,OAAA;cACEqD,EAAE,EAAC,YAAY;cACf5B,IAAI,EAAC,YAAY;cACjBC,KAAK,EAAElB,QAAQ,CAACM,UAAW;cAC3BwC,QAAQ,EAAE/B,iBAAkB;cAC5BqB,SAAS,EAAE,0HACTxB,MAAM,CAACN,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,EACvD;cAAA+B,QAAA,gBAEH7C,OAAA;gBAAQ0B,KAAK,EAAC,EAAE;gBAAAmB,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC1CjC,WAAW,CAACwC,GAAG,CAACC,IAAI,iBACnBzD,OAAA;gBAAmB0B,KAAK,EAAE+B,IAAK;gBAAAZ,QAAA,EAAEY;cAAI,GAAxBA,IAAI;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA6B,CAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACR7B,MAAM,CAACN,UAAU,iBAAId,OAAA;cAAG4C,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAEzB,MAAM,CAACN;YAAU;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eAGNjD,OAAA;YAAK4C,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACnD7C,OAAA;cAAOmD,OAAO,EAAC,MAAM;cAACP,SAAS,EAAC,oCAAoC;cAAAC,QAAA,GAAC,OAC9D,eAAA7C,OAAA;gBAAM4C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACRjD,OAAA;cACEqD,EAAE,EAAC,MAAM;cACT5B,IAAI,EAAC,MAAM;cACXC,KAAK,EAAElB,QAAQ,CAACK,IAAK;cACrByC,QAAQ,EAAE/B,iBAAkB;cAC5BmC,QAAQ,EAAE,CAAClD,QAAQ,CAACM,UAAW;cAC/B8B,SAAS,EAAE,0HACTxB,MAAM,CAACP,IAAI,GAAG,gBAAgB,GAAG,iBAAiB,IAChD,CAACL,QAAQ,CAACM,UAAU,GAAG,gCAAgC,GAAG,EAAE,EAAG;cAAA+B,QAAA,gBAEnE7C,OAAA;gBAAQ0B,KAAK,EAAC,EAAE;gBAAAmB,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACpCzC,QAAQ,CAACM,UAAU,MAAAP,qBAAA,GAAIU,iBAAiB,CAACT,QAAQ,CAACM,UAAU,CAAC,cAAAP,qBAAA,uBAAtCA,qBAAA,CAAwCiD,GAAG,CAAC3C,IAAI,iBACtEb,OAAA;gBAAmB0B,KAAK,EAAEb,IAAK;gBAAAgC,QAAA,EAAEhC;cAAI,GAAxBA,IAAI;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA6B,CAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACR7B,MAAM,CAACP,IAAI,iBAAIb,OAAA;cAAG4C,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAEzB,MAAM,CAACP;YAAI;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACzE,CAACzC,QAAQ,CAACM,UAAU,iBACnBd,OAAA;cAAG4C,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAC9E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeH,CAAC,eAGNjD,OAAA;UAAK4C,SAAS,EAAC,oDAAoD;UAAAC,QAAA,gBACjE7C,OAAA;YACEoD,IAAI,EAAC,QAAQ;YACbF,OAAO,EAAE/C,QAAS;YAClByC,SAAS,EAAC,wKAAwK;YAAAC,QAAA,EACnL;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjD,OAAA;YACEoD,IAAI,EAAC,QAAQ;YACbR,SAAS,EAAC,qKAAqK;YAAAC,QAAA,EAChL;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3C,EAAA,CArXIL,mBAAmB;AAAA0D,EAAA,GAAnB1D,mBAAmB;AAuXzB,eAAeA,mBAAmB;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}