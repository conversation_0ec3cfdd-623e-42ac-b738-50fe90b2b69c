{"version": 3, "file": "static/js/510.4350d131.chunk.js", "mappings": "wNAGA,MAAMA,EAAUC,gEAmRhB,EAjR0BC,IAAyC,IAAxC,UAAEC,EAAS,WAAEC,EAAU,QAAEC,GAASH,GACxCI,EAAAA,EAAAA,MAAjB,MACOC,EAAaC,IAAkBC,EAAAA,EAAAA,UAAS,KACxCC,EAAOC,IAAYF,EAAAA,EAAAA,UAAS,KAC5BG,EAAoBC,IAAyBJ,EAAAA,EAAAA,UAAS,KACtDK,EAAcC,IAAmBN,EAAAA,EAAAA,UAAS,KAC1CO,EAAWC,IAAgBR,EAAAA,EAAAA,UAAS,KACpCS,EAAOC,IAAYV,EAAAA,EAAAA,UAAS,KAC5BW,EAAgBC,IAAqBZ,EAAAA,EAAAA,UAAS,KAGrDa,EAAAA,EAAAA,YAAU,KACN,IAAKnB,EAAW,OAESoB,WACrB,MAAMC,EAAQC,aAAaC,QAAQ,SACnC,GAAKF,EAKL,IACI,MAAMG,QAAiBC,MAAM,GAAG5B,eAAsB,CAClD6B,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUN,IAC3B,eAAgB,sBAIxB,IAAKG,EAASI,GACV,MAAM,IAAIC,MAAM,+BAGpB,MAAMC,QAAaN,EAASO,OAC5B1B,EAAeyB,EAAK1B,YACxB,CAAE,MAAOW,GACLC,EAASD,EAAMiB,QACnB,MArBIhB,EAAS,iCAqBb,EAGJiB,EAAkB,GACnB,CAACjC,KAGJmB,EAAAA,EAAAA,YAAU,KACN,IAAKjB,IAAYE,EAAY8B,OAAQ,OAEVd,WACvB,MAAMC,EAAQC,aAAaC,QAAQ,SACnC,GAAKF,EAKL,IACI,MAAMG,QAAiBC,MAAM,GAAG5B,mBAAyBK,IAAW,CAChEwB,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUN,IAC3B,eAAgB,sBAIxB,IAAKG,EAASI,GACV,MAAM,IAAIC,MAAM,0CAGpB,MACMM,SADaX,EAASO,QACDI,cAG3B,GAAIA,EAAe,CACfrB,EAAaqB,EAAcC,MAC3B1B,EAAsByB,EAAcE,YACpCzB,EAAgBuB,EAAcG,MAG9B,MAAMD,EAAajC,EAAYmC,MAAKC,GAAOA,EAAIJ,OAASD,EAAcE,aAElEA,GAAcA,EAAW9B,OACzBC,EAAS6B,EAAW9B,QACf4B,EAAcG,MAAQD,EAAW9B,MAAM2B,OAAS,GACjDtB,EAAgByB,EAAW9B,MAAM,GAAG6B,OAGxC5B,EAAS,GAEjB,MACIQ,EAAS,2BAEjB,CAAE,MAAOD,GACLC,EAASD,EAAMiB,QACnB,MA1CIhB,EAAS,iCA0Cb,EAGJyB,EAAoB,GACrB,CAACvC,EAASE,IAgFb,OACIsC,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SACK5C,IACG0C,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mHAAkHD,UAC7HE,EAAAA,EAAAA,MAAA,OAAKD,UAAU,4FAA2FD,SAAA,EACtGF,EAAAA,EAAAA,KAAA,UAAQK,QATRC,KAChB/C,GAAW,EAAM,EAQ6B4C,UAAU,2DAA0DD,SAAC,UAGnGF,EAAAA,EAAAA,KAAA,MAAIG,UAAU,kCAAiCD,SAAC,yBAChDE,EAAAA,EAAAA,MAAA,QAAMG,SAjEL7B,UAGjB,GAFA8B,EAAMC,iBAED1C,GAAuBE,GAAiBE,EAA7C,CAKAG,EAAS,IACT,IACI,MAAMK,EAAQC,aAAaC,QAAQ,SACnC,IAAKF,EAED,YADAL,EAAS,oCAIb,MAAMQ,QAAiBC,MAAM,GAAG5B,mBAAyBK,IAAW,CAChEwB,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUN,IAC3B,eAAgB,oBAEpB+B,KAAMC,KAAKC,UAAU,CACjBjB,WAAY5B,EACZ6B,KAAM3B,EACNyB,KAAMvB,MAId,IAAKW,EAASI,GACV,MAAM,IAAIC,MAAM,oCAGpB,MAAM0B,QAAe/B,EAASO,OAC9ByB,QAAQC,IAAI,oBAAqBF,GAG7BA,EAAOpB,cACPjB,EAAkB,mBAAmBqC,EAAOpB,cAAcC,+BAE1DlB,EAAkB,wCAGtBwC,YAAW,KACPzD,GAAW,GACXiB,EAAkB,GAAG,GACtB,IACP,CAAE,MAAOH,GACLC,EAASD,EAAMiB,QACnB,CA3CA,MAFIhB,EAAS,0BA6Cb,EAgB6C4B,SAAA,EACzBE,EAAAA,EAAAA,MAAA,OAAKD,UAAU,OAAMD,SAAA,EACjBF,EAAAA,EAAAA,KAAA,SAAOiB,QAAQ,aAAad,UAAU,+CAA8CD,SAAC,uBAGrFE,EAAAA,EAAAA,MAAA,UACIc,GAAG,aACHC,MAAOpD,EACPqD,SA9FAC,IAC5B,MAAMC,EAAiBD,EAAEE,OAAOJ,MAIhC,GAHAnD,EAAsBsD,GACtBpD,EAAgB,IAEZoD,EAAgB,CAChB,MAAM3B,EAAajC,EAAYmC,MAAKC,GAAOA,EAAIJ,OAAS4B,IAEpD3B,GAAcA,EAAW9B,OAAS8B,EAAW9B,MAAM2B,OAAS,GAC5D1B,EAAS6B,EAAW9B,OACpBK,EAAgByB,EAAW9B,MAAM,GAAG6B,QAEpC5B,EAAS,IACTI,EAAgB,IAExB,MACIJ,EAAS,IACTI,EAAgB,GACpB,EA6E4BiC,UAAU,+HACVqB,UAAQ,EAAAtB,SAAA,EAERF,EAAAA,EAAAA,KAAA,UAAQmB,MAAM,GAAEjB,SAAC,wBACO,IAAvBxC,EAAY8B,QACTQ,EAAAA,EAAAA,KAAA,UAAQyB,UAAQ,EAAAvB,SAAC,6BAEjBxC,EAAYgE,KAAK/B,IACbK,EAAAA,EAAAA,KAAA,UAA4BmB,MAAOxB,EAAWD,KAAKQ,SAC9CP,EAAWD,MADHC,EAAWuB,aAQxCd,EAAAA,EAAAA,MAAA,OAAKD,UAAU,OAAMD,SAAA,EACjBF,EAAAA,EAAAA,KAAA,SAAOiB,QAAQ,OAAOd,UAAU,+CAA8CD,SAAC,iBAG/EE,EAAAA,EAAAA,MAAA,UACIc,GAAG,OACHC,MAAOlD,EACPmD,SAAWC,GAAMnD,EAAgBmD,EAAEE,OAAOJ,OAC1ChB,UAAU,+HACVqB,UAAQ,EAAAtB,SAAA,EAERF,EAAAA,EAAAA,KAAA,UAAQmB,MAAM,GAAEjB,SAAC,kBACC,IAAjBrC,EAAM2B,QACHQ,EAAAA,EAAAA,KAAA,UAAQyB,UAAQ,EAAAvB,SAAC,uBAEjBrC,EAAM6D,KAAK9B,IACPI,EAAAA,EAAAA,KAAA,UAAsBmB,MAAOvB,EAAKF,KAAKQ,SAClCN,EAAKF,MADGE,EAAKsB,aAQlCd,EAAAA,EAAAA,MAAA,OAAKD,UAAU,OAAMD,SAAA,EACjBF,EAAAA,EAAAA,KAAA,SAAOiB,QAAQ,YAAYd,UAAU,+CAA8CD,SAAC,gBAGpFF,EAAAA,EAAAA,KAAA,SACIkB,GAAG,YACHS,KAAK,OACLR,MAAOhD,EACPiD,SAAWC,GAAMjD,EAAaiD,EAAEE,OAAOJ,OACvChB,UAAU,+HACVqB,UAAQ,QAIhBxB,EAAAA,EAAAA,KAAA,OAAKG,UAAU,OAAMD,UACjBF,EAAAA,EAAAA,KAAA,UACI2B,KAAK,SACLxB,UAAU,kEAAiED,SAC9E,4BAKJ7B,IAAS2B,EAAAA,EAAAA,KAAA,KAAGG,UAAU,uBAAsBD,SAAE7B,IAE9CE,IACD6B,EAAAA,EAAAA,MAAA,OAAKD,UAAU,qFAAoFD,SAAA,EAC/FF,EAAAA,EAAAA,KAAA,QAAMG,UAAU,qEAAoED,SAAC,kBACrFF,EAAAA,EAAAA,KAAA,KAAGG,UAAU,0CAAyCD,SAAE3B,gBAM7E,EC9QLqD,EAAeA,IAEA,OADHhD,aAAaC,QAAQ,SAIjC1B,EAAUC,+DAoIhB,EAlI0ByE,KACtB,MAAOC,EAAQC,IAAanE,EAAAA,EAAAA,UAAS,KAC9BoE,EAAcC,IAAmBrE,EAAAA,EAAAA,WAAS,IAC1CsE,EAASC,IAAcvE,EAAAA,EAAAA,WAAS,IAChCwE,EAAiBC,IAAsBzE,EAAAA,EAAAA,UAAS,OAChDS,EAAOC,IAAYV,EAAAA,EAAAA,UAAS,OAYnCa,EAAAA,EAAAA,YAAU,KACcC,WAChB,IAAKkD,IAGD,OAFAtD,EAAS,uCACT6D,GAAW,GAIf,MAAMxD,EAAQC,aAAaC,QAAQ,SAEnC,IACI,MAAMC,QAAiBC,MAAM,GAAG5B,oBAA2B,CACvD6B,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUN,IAC3B,eAAgB,sBAIxB,IAAKG,EAASI,GACV,MAAM,IAAIC,MAAM,gCAAkCL,EAASwD,YAG/D,MAAMlD,QAAaN,EAASO,OAC5ByB,QAAQC,IAAI,SAAU3B,GAEtB2C,EAAU3C,EAAKmD,eAAeb,KAAIc,IAAK,CACnCtB,GAAIsB,EAAMtB,GACVxB,KAAM8C,EAAM9C,KACZC,WAAY6C,EAAM7C,WAClBC,KAAM4C,EAAM5C,KACZ6C,WAAYD,EAAMC,WAClBC,WAAYF,EAAME,eAE1B,CAAE,MAAOrE,GACLC,EAASD,EAAMiB,QACnB,CAAC,QACG6C,GAAW,EACf,GAGJQ,EAAa,GACd,IAqCH,OAAItE,GACO2B,EAAAA,EAAAA,KAAA,OAAKG,UAAU,eAAcD,SAAE7B,IAGtC6D,GACOlC,EAAAA,EAAAA,KAAA,OAAKG,UAAU,gBAAeD,SAAC,eAIpB,IAAlB4B,EAAOtC,QACAQ,EAAAA,EAAAA,KAAA,OAAKG,UAAU,gBAAeD,SAAC,uBAItCE,EAAAA,EAAAA,MAAA,OAAAF,SAAA,EACIF,EAAAA,EAAAA,KAAC4C,EAAAA,EAAY,CACTC,aAAcf,EACdgB,YAzGQ,CAChB,CAAEC,MAAO,KAAMC,IAAK,MACpB,CAAED,MAAO,cAAeC,IAAK,QAC7B,CAAED,MAAO,aAAcC,IAAK,cAC5B,CAAED,MAAO,OAAQC,IAAK,QACtB,CAAED,MAAO,aAAcC,IAAK,cAC5B,CAAED,MAAO,aAAcC,IAAK,eAoGpBC,SApDSvE,UACjB,IAAKkD,IAED,YADAtD,EAAS,kCAIb,MAAMK,EAAQC,aAAaC,QAAQ,SAEnC,IACI,MAAMC,QAAiBC,MAAM,GAAG5B,oBAA0B+D,IAAM,CAC5DlC,OAAQ,SACRC,QAAS,CACL,cAAiB,UAAUN,IAC3B,eAAgB,sBAIxB,IAAKG,EAASI,GACV,MAAM,IAAIC,MAAM,oCAAsCL,EAASwD,YAInEP,GAAUmB,GAAcA,EAAWC,QAAOX,GAASA,EAAMtB,KAAOA,KACpE,CAAE,MAAO7C,GACLC,EAASD,EAAMiB,QACnB,GA4BQ8D,OAxBQlC,IAChBmB,EAAmBnB,GACnBe,GAAgB,EAAK,EAuBbA,gBAAiBA,EACjBoB,qBAAsBhB,IAEzBL,IACGhC,EAAAA,EAAAA,KAACsD,EAAiB,CACdhG,UAAW0E,EACXzE,WAAY0E,EACZzE,QAAS4E,MAGf,ECvHd,EAZsBmB,KAElBvD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,yCAAwCD,UACrDE,EAAAA,EAAAA,MAACoD,EAAAA,EAAmB,CAAAtD,SAAA,EAClBF,EAAAA,EAAAA,KAACyD,EAAAA,EAAW,CAACC,UAAU,qBAAqBC,WAAW,wBACvD3D,EAAAA,EAAAA,KAAC6B,EAAiB,KAClB7B,EAAAA,EAAAA,KAAC4D,EAAAA,EAAe,Q", "sources": ["pages/training/training-topic/EditTrainingTopic.jsx", "pages/training/training-topic/TrainingTopicList.jsx", "dashboard/settings/TrainingTopic.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL+'/';\r\n\r\nconst EditTrainingTopic = ({ isVisible, setVisible, topicId }) => {\r\n    const navigate = useNavigate();\r\n    const [departments, setDepartments] = useState([]);\r\n    const [teams, setTeams] = useState([]);\r\n    const [selectedDepartment, setSelectedDepartment] = useState('');\r\n    const [selectedTeam, setSelectedTeam] = useState('');\r\n    const [topicName, setTopicName] = useState('');\r\n    const [error, setError] = useState('');\r\n    const [successMessage, setSuccessMessage] = useState('');\r\n\r\n    // Fetch Departments\r\n    useEffect(() => {\r\n        if (!isVisible) return;\r\n\r\n        const fetchDepartments = async () => {\r\n            const token = localStorage.getItem('token');\r\n            if (!token) {\r\n                setError('No authentication token found.');\r\n                return;\r\n            }\r\n\r\n            try {\r\n                const response = await fetch(`${API_URL}departments`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n                if (!response.ok) {\r\n                    throw new Error('Failed to fetch departments');\r\n                }\r\n\r\n                const data = await response.json();\r\n                setDepartments(data.departments);\r\n            } catch (error) {\r\n                setError(error.message);\r\n            }\r\n        };\r\n\r\n        fetchDepartments();\r\n    }, [isVisible]);\r\n\r\n    // Fetch the Training Topic Details to Edit\r\n    useEffect(() => {\r\n        if (!topicId || !departments.length) return;\r\n\r\n        const fetchTrainingTopic = async () => {\r\n            const token = localStorage.getItem('token');\r\n            if (!token) {\r\n                setError('No authentication token found.');\r\n                return;\r\n            }\r\n\r\n            try {\r\n                const response = await fetch(`${API_URL}training-topic/${topicId}`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n                if (!response.ok) {\r\n                    throw new Error('Failed to fetch Training Topic details');\r\n                }\r\n\r\n                const data = await response.json();\r\n                const trainingTopic = data.trainingTopic;\r\n\r\n                // Ensure the trainingTopic exists and is valid\r\n                if (trainingTopic) {\r\n                    setTopicName(trainingTopic.name);\r\n                    setSelectedDepartment(trainingTopic.department);\r\n                    setSelectedTeam(trainingTopic.team);\r\n\r\n                    // Fetch teams for the selected department\r\n                    const department = departments.find(dep => dep.name === trainingTopic.department);\r\n\r\n                    if (department && department.teams) {\r\n                        setTeams(department.teams);\r\n                        if (!trainingTopic.team && department.teams.length > 0) {\r\n                            setSelectedTeam(department.teams[0].name);\r\n                        }\r\n                    } else {\r\n                        setTeams([]);\r\n                    }\r\n                } else {\r\n                    setError('Training topic not found');\r\n                }\r\n            } catch (error) {\r\n                setError(error.message);\r\n            }\r\n        };\r\n\r\n        fetchTrainingTopic();\r\n    }, [topicId, departments]);\r\n\r\n    // Handle Department Change and Fetch Teams\r\n    const handleDepartmentChange = (e) => {\r\n        const departmentName = e.target.value;\r\n        setSelectedDepartment(departmentName);\r\n        setSelectedTeam('');\r\n\r\n        if (departmentName) {\r\n            const department = departments.find(dep => dep.name === departmentName);\r\n\r\n            if (department && department.teams && department.teams.length > 0) {\r\n                setTeams(department.teams);\r\n                setSelectedTeam(department.teams[0].name);\r\n            } else {\r\n                setTeams([]);\r\n                setSelectedTeam('');\r\n            }\r\n        } else {\r\n            setTeams([]);\r\n            setSelectedTeam('');\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (event) => {\r\n        event.preventDefault();\r\n\r\n        if (!selectedDepartment || !selectedTeam || !topicName) {\r\n            setError('Please fill all fields.');\r\n            return;\r\n        }\r\n\r\n        setError('');\r\n        try {\r\n            const token = localStorage.getItem('token');\r\n            if (!token) {\r\n                setError('Authentication token is missing.');\r\n                return;\r\n            }\r\n\r\n            const response = await fetch(`${API_URL}training-topic/${topicId}`, {\r\n                method: 'PUT',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n                body: JSON.stringify({\r\n                    department: selectedDepartment,\r\n                    team: selectedTeam,\r\n                    name: topicName,\r\n                }),\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Failed to update Training Topic.');\r\n            }\r\n\r\n            const result = await response.json();\r\n            console.log('Topics for update', result);\r\n\r\n            // Ensure you're accessing the correct field for success message\r\n            if (result.trainingTopic) {\r\n                setSuccessMessage(`Training Topic \"${result.trainingTopic.name}\" updated successfully!`);\r\n            } else {\r\n                setSuccessMessage('Training Topic updated successfully!');\r\n            }\r\n\r\n            setTimeout(() => {\r\n                setVisible(false);\r\n                setSuccessMessage('');\r\n            }, 2000);\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n\r\n    const handleClose = () => {\r\n        setVisible(false);\r\n    };\r\n\r\n    return (\r\n        <>\r\n            {isVisible && (\r\n                <div className=\"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\">\r\n                    <div className=\"bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative overflow-y-auto h-[80vh] mt-10\">\r\n                        <button onClick={handleClose} className=\"absolute top-2 right-2 text-gray-400 hover:text-gray-900\">\r\n                            &times;\r\n                        </button>\r\n                        <h4 className=\"text-xl font-semibold mb-4 py-4\">Edit Training Topic</h4>\r\n                        <form onSubmit={handleSubmit}>\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"department\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Select Department\r\n                                </label>\r\n                                <select\r\n                                    id=\"department\"\r\n                                    value={selectedDepartment}\r\n                                    onChange={handleDepartmentChange}\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                    required\r\n                                >\r\n                                    <option value=\"\">Select a Department</option>\r\n                                    {departments.length === 0 ? (\r\n                                        <option disabled>No departments available</option>\r\n                                    ) : (\r\n                                        departments.map((department) => (\r\n                                            <option key={department.id} value={department.name}>\r\n                                                {department.name}\r\n                                            </option>\r\n                                        ))\r\n                                    )}\r\n                                </select>\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"team\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Select Team\r\n                                </label>\r\n                                <select\r\n                                    id=\"team\"\r\n                                    value={selectedTeam}\r\n                                    onChange={(e) => setSelectedTeam(e.target.value)}\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                    required\r\n                                >\r\n                                    <option value=\"\">Select a Team</option>\r\n                                    {teams.length === 0 ? (\r\n                                        <option disabled>No teams available</option>\r\n                                    ) : (\r\n                                        teams.map((team) => (\r\n                                            <option key={team.id} value={team.name}>\r\n                                                {team.name}\r\n                                            </option>\r\n                                        ))\r\n                                    )}\r\n                                </select>\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"topicName\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Topic Name\r\n                                </label>\r\n                                <input\r\n                                    id=\"topicName\"\r\n                                    type=\"text\"\r\n                                    value={topicName}\r\n                                    onChange={(e) => setTopicName(e.target.value)}\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                    required\r\n                                />\r\n                            </div>\r\n\r\n                            <div className=\"py-4\">\r\n                                <button\r\n                                    type=\"submit\"\r\n                                    className=\"w-full bg-primary hover:bg-secondary text-white rounded-md py-3\"\r\n                                >\r\n                                    Update Training Topic\r\n                                </button>\r\n                            </div>\r\n\r\n                            {error && <p className=\"text-red-500 text-sm\">{error}</p>}\r\n\r\n                            {successMessage && \r\n                            <div className='bg-[#DAF8E6] p-6 rounded-lg border-l-4 border-green-500 flex flex-row items-center'>\r\n                                <span className=\"material-symbols-rounded bg-green-500 text-gray-700 p-1 rounded-md\">check_circle</span>\r\n                                <p className=\"text-green-500 text-xl font-medium pl-6\">{successMessage}</p>\r\n                            </div>}\r\n                        </form>\r\n                    </div>\r\n                </div>\r\n            )}\r\n        </>\r\n    );\r\n};\r\n\r\nexport default EditTrainingTopic;\r\n", "import React, { useEffect, useState } from 'react';\r\nimport TableContent from '../../../common/table/TableContent';\r\nimport EditTrainingTopic from './EditTrainingTopic'; // Ensure correct component is imported\r\n\r\nconst isTokenValid = () => {\r\n    const token = localStorage.getItem('token');\r\n    return token !== null;\r\n};\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst TrainingTopicList = () => {\r\n    const [topics, setTopics] = useState([]);\r\n    const [modalVisible, setModalVisible] = useState(false);\r\n    const [loading, setLoading] = useState(true); // Initially loading is true\r\n    const [selectedTopicId, setSelectedTopicId] = useState(null);\r\n    const [error, setError] = useState(null);\r\n\r\n    // Column names for Training Topics\r\n    const columnNames = [\r\n        { label: \"SL\", key: \"id\" },\r\n        { label: \"Toppic Name\", key: \"name\" },\r\n        { label: \"Department\", key: \"department\" },\r\n        { label: \"Team\", key: \"team\" },\r\n        { label: \"Created By\", key: \"created_by\" },\r\n        { label: \"Updated By\", key: \"updated_by\" },\r\n    ];\r\n\r\n    useEffect(() => {\r\n        const fetchTopics = async () => {\r\n            if (!isTokenValid()) {\r\n                setError('No authentication token found.');\r\n                setLoading(false);\r\n                return;\r\n            }\r\n\r\n            const token = localStorage.getItem('token');\r\n\r\n            try {\r\n                const response = await fetch(`${API_URL}/training-topics`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n                if (!response.ok) {\r\n                    throw new Error('Network response was not ok: ' + response.statusText);\r\n                }\r\n\r\n                const data = await response.json();\r\n                console.log('Topics', data);\r\n\r\n                setTopics(data.trainingTopics.map(topic => ({\r\n                    id: topic.id,\r\n                    name: topic.name,\r\n                    department: topic.department,\r\n                    team: topic.team,\r\n                    created_by: topic.created_by,\r\n                    updated_by: topic.updated_by,\r\n                })));\r\n            } catch (error) {\r\n                setError(error.message);\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchTopics();\r\n    }, []);\r\n\r\n    // Handle Delete\r\n    const handleDelete = async (id) => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n\r\n        const token = localStorage.getItem('token');\r\n\r\n        try {\r\n            const response = await fetch(`${API_URL}/training-topic/${id}`, {\r\n                method: 'DELETE',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Failed to delete training topic: ' + response.statusText);\r\n            }\r\n\r\n            // Update the topics list after deletion\r\n            setTopics(prevTopics => prevTopics.filter(topic => topic.id !== id));\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n\r\n    // Handle Edit\r\n    const handleEdit = (id) => {\r\n        setSelectedTopicId(id);\r\n        setModalVisible(true);\r\n    };\r\n\r\n    if (error) {\r\n        return <div className=\"text-red-500\">{error}</div>;\r\n    }\r\n\r\n    if (loading) {\r\n        return <div className=\"text-gray-500\">Loading...</div>;\r\n    }\r\n\r\n    // Show message when no topics are available\r\n    if (topics.length === 0) {\r\n        return <div className=\"text-gray-500\">No data available</div>;\r\n    }\r\n\r\n    return (\r\n        <div>\r\n            <TableContent\r\n                tableContent={topics}\r\n                columnNames={columnNames}\r\n                onDelete={handleDelete}\r\n                onEdit={handleEdit}\r\n                setModalVisible={setModalVisible}\r\n                setSelectedServiceId={setSelectedTopicId}\r\n            />\r\n            {modalVisible && (\r\n                <EditTrainingTopic\r\n                    isVisible={modalVisible}\r\n                    setVisible={setModalVisible}\r\n                    topicId={selectedTopicId}\r\n                />\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default TrainingTopicList;\r\n", "import React from 'react';\r\nimport TableLayoutWrapper2 from '../../common/table/TableLayoutWrapper2';\r\nimport TableHeader from '../../common/table/TableHeader';\r\nimport TablePagination from '../../common/table/TablePagination';\r\nimport TrainingTopicList from '../../pages/training/training-topic/TrainingTopicList';\r\n\r\nconst TrainingTopic = () => {\r\n  return (\r\n    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>\r\n      <TableLayoutWrapper2>\r\n        <TableHeader routeName=\"/add-trainingtopic\" buttonName=\"Add Training Topic\" />\r\n        <TrainingTopicList />\r\n        <TablePagination />\r\n      </TableLayoutWrapper2>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TrainingTopic;\r\n"], "names": ["API_URL", "process", "_ref", "isVisible", "setVisible", "topicId", "useNavigate", "departments", "setDepartments", "useState", "teams", "setTeams", "selectedDepartment", "setSelectedDepartment", "selectedTeam", "setSelectedTeam", "topicName", "setTopicName", "error", "setError", "successMessage", "setSuccessMessage", "useEffect", "async", "token", "localStorage", "getItem", "response", "fetch", "method", "headers", "ok", "Error", "data", "json", "message", "fetchDepartments", "length", "trainingTopic", "name", "department", "team", "find", "dep", "fetchTrainingTopic", "_jsx", "_Fragment", "children", "className", "_jsxs", "onClick", "handleClose", "onSubmit", "event", "preventDefault", "body", "JSON", "stringify", "result", "console", "log", "setTimeout", "htmlFor", "id", "value", "onChange", "e", "departmentName", "target", "required", "disabled", "map", "type", "isTokenValid", "TrainingTopicList", "topics", "setTopics", "modalVisible", "setModalVisible", "loading", "setLoading", "selectedTopicId", "setSelectedTopicId", "statusText", "trainingTopics", "topic", "created_by", "updated_by", "fetchTopics", "TableContent", "tableContent", "columnNames", "label", "key", "onDelete", "prevTopics", "filter", "onEdit", "setSelectedServiceId", "EditTrainingTopic", "TrainingTopic", "TableLayoutWrapper2", "TableHeader", "routeName", "buttonName", "TablePagination"], "sourceRoot": ""}