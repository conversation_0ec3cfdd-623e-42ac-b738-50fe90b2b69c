"use strict";(self.webpackChunkcreativeapp=self.webpackChunkcreativeapp||[]).push([[72],{39072:(e,t,a)=>{a.r(t),a.d(t,{default:()=>j});var r=a(65043),s=a(58786),o=a(13076),l=a(82949),n=a(83003),i=a(47554),c=a(72450),d=a(11238),u=a(78854),m=a(73216),h=a(70579);const x="https://creativeapp.sebpo.net/banner/test/backend/public/api",p=e=>{let{isVisible:t,setVisible:a,dataItemsId:s}=e;const[o,n]=(0,r.useState)(""),[i,c]=(0,r.useState)(""),[d,u]=(0,r.useState)([]),[m,p]=(0,r.useState)([]),[g,f]=(0,r.useState)(""),[b,y]=(0,r.useState)(""),[v,j]=(0,r.useState)(null),[w,N]=(0,r.useState)(!1);(0,r.useEffect)((()=>{const e=localStorage.getItem("user_id");e&&j(e)}),[]),(0,r.useEffect)((()=>{(async()=>{if(!s)return;const e=localStorage.getItem("token");if(e)try{const r=await fetch(`${x}/branches`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!r.ok)throw new Error("Failed to fetch branches: "+r.statusText);const o=(await r.json()).branches;if(!Array.isArray(o))throw new Error("Expected branches to be an array.");const l=o.find((e=>e.id===s));if(!l)throw new Error("Branch not found. Please check the ID.");{var t,a;n(l.name);const e=(null===(t=l.locations)||void 0===t||null===(a=t[0])||void 0===a?void 0:a.id)||"";c(e)}}catch(g){f(g.message)}else f("No authentication token found.")})(),(async()=>{const e=localStorage.getItem("token");try{const t=await fetch(`${x}/locations`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to fetch locations");const a=await t.json();p(a.locations||[])}catch(g){f(g.message)}})()}),[s]);return t?(0,h.jsx)("div",{className:"fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50",onClick:()=>a(!1),children:(0,h.jsxs)("div",{className:"relative bg-white rounded-lg shadow-lg max-w-md w-full",onClick:e=>e.stopPropagation(),children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-4 bg-gray-100 p-4",children:[(0,h.jsx)("h3",{className:"text-base text-left font-medium text-gray-800",children:"Update Branch"}),(0,h.jsx)("button",{className:"text-2xl text-gray-500 hover:text-gray-800",onClick:()=>a(!1),children:"\xd7"})]}),g&&(0,h.jsx)("div",{className:"text-red-500",children:g}),b&&(0,h.jsx)("div",{className:"text-green-500",children:b}),(0,h.jsxs)("form",{onSubmit:async e=>{e.preventDefault();const t=o.trim(),r=v;if(r){if(Array.isArray(d)){if(d.some((e=>e.name.toLowerCase().trim()===t.toLowerCase()))){f("Branch already exists. Please add a different branch.");const e=setTimeout((()=>f("")),3e3);return()=>clearTimeout(e)}}f("");try{const e=localStorage.getItem("token"),o=await fetch(`${x}/branches/${s}`,{method:"PUT",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"},body:JSON.stringify({name:t,location_id:i,updated_by:r})});if(!o.ok)throw new Error("Failed to update branch: "+o.statusText);const d=await o.json();d.branch.name;(0,l.GW)({icon:"success",title:"Success!",text:(null===d||void 0===d?void 0:d.message)||"Office branch updated successfully."}),n(""),c("");const m=await fetch(`${x}/branches`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!m.ok)throw new Error("Failed to fetch branches: "+m.statusText);const h=await m.json();u(h.branches||[]),setTimeout((()=>{a(!1),y("")}),1e3)}catch(g){(0,l.GW)("error")}}else f("User is not logged in.")},className:"p-6",children:[(0,h.jsxs)("div",{className:"mb-4",children:[(0,h.jsx)("label",{htmlFor:"name",className:"block mb-2",children:"Branch Name"}),(0,h.jsx)("input",{type:"text",id:"name",value:o,onChange:e=>n(e.target.value),className:"border rounded w-full p-2",required:!0})]}),(0,h.jsxs)("div",{className:"mb-4",children:[(0,h.jsx)("label",{htmlFor:"location",className:"block mb-2",children:"Select Location"}),(0,h.jsxs)("select",{id:"location",value:i,onChange:e=>c(e.target.value),className:"border rounded w-full p-2",required:!0,children:[(0,h.jsx)("option",{value:"",children:"Select a Location"}),0===m.length?(0,h.jsx)("option",{disabled:!0,children:"No locations available"}):m.map((e=>(0,h.jsx)("option",{value:e.id,children:e.locations_name},e.id)))]})]}),(0,h.jsx)("div",{className:"text-left p-6",children:(0,h.jsxs)("button",{type:"submit",className:"w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4",children:[(0,h.jsx)("span",{class:"material-symbols-rounded text-white text-xl font-regular",children:"add_circle"}),w?"Updating...":"Update Branch"]})})]})]})}):null};var g=a(75037),f=a(17974),b=a(58598);const y="Branch",v=()=>{const[e,t]=(0,r.useState)({}),[a,x]=(0,r.useState)({}),[v,j]=(0,r.useState)(""),[w,N]=(0,r.useState)(""),[_,k]=(0,r.useState)(!1),[S,C]=(0,r.useState)(!1),[A,$]=(0,r.useState)(null),[E,T]=(0,r.useState)(null),[F,B]=(0,r.useState)(null),[O,P]=((0,m.Zp)(),(0,r.useState)(!1)),[I,D]=(0,r.useState)("created_at"),[R,U]=(0,r.useState)("desc"),[z,L]=(0,r.useState)("10"),[M,V]=(0,r.useState)(1),{data:G,isFetching:W,error:q}=(0,u.UP9)({sort_by:I,order:R,page:M,per_page:z,query:w}),[H,{data:Q,error:Z}]=(0,u.vXj)(),[J]=(0,u._G8)(),X=e=>{let t=Object.entries(e).reduce(((e,t)=>{let[a,r]=t;if("string"===typeof r)return e+`&${a}=${r}`;if(Array.isArray(r)){return e+`&${a}=${r.map((e=>e.value)).join(",")}`}return e}),"");N(t)},Y=e=>{(0,i.$3)(e,["id","team","department","updated_at","updated_by","updater","created_at","creator","created_by","updated_by"]);B(null),k(!0)},K=e=>{B(null),$(e),k(!0)},ee=e=>{(0,l.YU)({onConfirm:()=>{J(e),B(null)}})};let te=1;const{rolePermissions:ae}=(0,f.h)(),[re,se]=(0,r.useState)((()=>[{id:te++,name:"Action",width:"180px",className:"bg-red-300",cell:e=>(0,h.jsxs)("div",{className:"flex gap-1 mx-2 !min-w-[200px] pl-3",children:[(0,h.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>B(e),children:(0,h.jsx)("span",{className:"material-symbols-outlined text-lg",children:"visibility"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,h.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>K(e.id),children:(0,h.jsx)("span",{className:"material-symbols-outlined text-lg",children:"stylus_note"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,h.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>Y(e),children:(0,h.jsx)("span",{className:"material-symbols-outlined text-lg",children:"content_copy"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,h.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>ee(e.id),children:(0,h.jsx)("span",{className:"material-symbols-outlined text-sm",children:"delete"})})]})},{id:te++,name:"S.No",selector:(e,t)=>(M-1)*z+t+1,width:"80px",omit:!1},{id:te++,name:"Location",db_field:"locations",selector:e=>e.locations&&Array.isArray(e.locations)?e.locations.map((e=>e.locations_name)).join(", "):"N/A",omit:!1,sortable:!1,filterable:!0},{id:te++,name:"Branch Name",db_field:"name",selector:e=>e.name||"",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created by",selector:e=>{var t,a;return`${(null===(t=e.creator)||void 0===t?void 0:t.fname)||""} ${(null===(a=e.creator)||void 0===a?void 0:a.lname)||""}`},db_field:"created_by",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created Date",selector:e=>(0,b.hb)(e.created_at),db_field:"created_at",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created Time",selector:e=>(0,b.DF)(e.created_at),db_field:"created_at",omit:!1,sortable:!0,filterable:!1},{id:te++,name:"Updated by",selector:e=>{var t,a;return`${(null===(t=e.updater)||void 0===t?void 0:t.fname)||""} ${(null===(a=e.updater)||void 0===a?void 0:a.lname)||""}`},db_field:"updated_by",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Updated Date",selector:e=>(0,b.hb)(e.updated_at),db_field:"updated_at",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Updated Time",selector:e=>(0,b.DF)(e.updated_at),db_field:"updated_at",omit:!1,sortable:!0,filterable:!1}]));(0,r.useEffect)((()=>{se((e=>[...e.map((e=>"Action"===e.name?{...e,cell:e=>(0,h.jsxs)("div",{className:"flex gap-1 mx-2 !min-w-[200px] pl-3",children:[(0,h.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>B(e),children:(0,h.jsx)("span",{className:"material-symbols-outlined text-lg",children:"visibility"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,h.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>K(e.id),children:(0,h.jsx)("span",{className:"material-symbols-outlined text-lg",children:"stylus_note"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,h.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>Y(e),children:(0,h.jsx)("span",{className:"material-symbols-outlined text-lg",children:"content_copy"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,h.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>ee(e.id),children:(0,h.jsx)("span",{className:"material-symbols-outlined text-sm",children:"delete"})})]})}:e))]))}),[ae]);const oe=(0,n.wA)(),le=(0,r.useCallback)((async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"group",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"select",o=e.db_field||"title";try{j(o),C(!0);var l=[];const n=await H({type:a.trim(),column:o.trim(),text:r.trim()});if(n.data&&(l=n.data),l.length){if("searchable"===s)return t((e=>({...e,[o]:l}))),l;const a=l.map((t=>{if(e.selector){let a=e.selector(t);return a?(t.total&&t.total>1&&(a+=` (${t.total})`),{label:a,value:t[o]}):null}})).filter(Boolean);return t((t=>({...t,[e.id]:(0,i.eb)(a)}))),a}}catch(E){T(E.message)}finally{C(!1)}}),[]);return(0,h.jsx)("section",{className:"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]",children:(0,h.jsxs)("div",{className:"mx-auto pb-6 ",children:[(0,h.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4",children:[(0,h.jsx)("div",{className:"w-4/12 md:w-10/12 text-start",children:(0,h.jsx)("h2",{className:"text-2xl font-bold ",children:y})}),(0,h.jsxs)("div",{className:"w-8/12 flex items-end justify-end gap-1",children:[(0,h.jsx)(l.DF,{columns:re,setColumns:se}),!W&&G&&parseInt(G.total)>0&&(0,h.jsx)(h.Fragment,{children:(0,h.jsxs)("button",{className:"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:async()=>{try{const t=await oe(u.H_Z.endpoints.getBranchData.initiate({sort_by:I,order:R,page:M,per_page:(null===G||void 0===G?void 0:G.total)||10,query:w})).unwrap();if(null===t||void 0===t||!t.total||t.total<1)return!1;var e=1;let a=t.data.map((t=>{if(re.length){let a={};return re.forEach((r=>{!r.omit&&r.selector&&(a[r.name]="S.No"===r.name?e++:r.selector(t)||"")})),a}}));const r=d.Wp.json_to_sheet(a),s=d.Wp.book_new();d.Wp.book_append_sheet(s,r,"Sheet1");const o=d.M9(s,{bookType:"xlsx",type:"array"}),l=new Blob([o],{type:"application/octet-stream"});(0,c.saveAs)(l,`${y.replace(/ /g,"_")}_${a.length}.xlsx`)}catch(E){console.error("Error exporting to Excel:",E)}},children:[W&&(0,h.jsx)(h.Fragment,{children:(0,h.jsx)("span",{className:"material-symbols-outlined animate-spin text-sm me-2",children:"progress_activity"})}),!W&&(0,h.jsx)("span",{className:"material-symbols-outlined text-sm me-2",children:"file_export"}),"Export to Excel (",G.total,")"]})}),ae.hasManagerRole&&(0,h.jsx)("button",{className:" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:()=>P(!0),children:"Add New"})]})]}),(0,h.jsx)(l.$6,{columns:re,selectedFilterOptions:a,setSelectedFilterOptions:x,fetchDataOptionsForFilterBy:le,filterOptions:e,filterOptionLoading:S,showFilterOption:v,resetPage:()=>{if(Object.keys(a).length){let e={};Object.keys(a).map((t=>{"string"===typeof a[t]?e[t]="":e[t]=[]})),x({...e}),X({...e})}V(1)},setCurrentPage:V,buildQueryParams:X}),q&&(0,h.jsx)("div",{className:"text-red-500",children:E}),W&&(0,h.jsx)(o.A,{}),(0,h.jsx)("div",{className:"border border-gray-200 p-0 pb-1 rounded-lg my-5 ",children:(0,h.jsx)(s.Ay,{columns:re,data:(null===G||void 0===G?void 0:G.data)||[],className:"p-0 scrollbar-horizontal-10",fixedHeader:!0,highlightOnHover:!0,responsive:!0,pagination:!0,paginationServer:!0,paginationPerPage:z,paginationTotalRows:(null===G||void 0===G?void 0:G.total)||0,onChangePage:e=>{e!==M&&V(e)},onChangeRowsPerPage:e=>{e!==z&&(L(e),V(1))},paginationComponentOptions:{selectAllRowsItem:!0,selectAllRowsItemText:"ALL"},sortServer:!0,onSort:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"desc";Object.keys(e).length&&(D(e.db_field||e.name||"created_at"),U(t||"desc"))}})}),O&&(0,h.jsx)(g.A,{isVisible:O,setVisible:P}),_&&(0,h.jsx)(p,{isVisible:_,setVisible:k,dataItemsId:A}),F&&(0,h.jsx)(l.Qg,{item:F,setViewData:B,columns:re,handleEdit:K,handleDelete:ee})]})})},j=()=>(0,h.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-xl",children:(0,h.jsx)(v,{})})}}]);
//# sourceMappingURL=72.b90a83ca.chunk.js.map