{"version": 3, "file": "static/js/260.7a6d2fed.chunk.js", "mappings": "uNAIA,MCFMA,EAAUC,+DAoIhB,EAlIgCC,IAA4C,IAA3C,UAAEC,EAAS,WAAEC,EAAU,WAAEC,GAAYH,EAClE,MAAOI,EAAcC,IAAmBC,EAAAA,EAAAA,UAAS,KAC1CC,EAAOC,IAAYF,EAAAA,EAAAA,UAAS,KAC5BG,EAAgBC,IAAqBJ,EAAAA,EAAAA,UAAS,KAErDK,EAAAA,EAAAA,YAAU,KACgBC,WAClB,GAAIT,EAAY,CACZ,MAAMU,EAAQC,aAAaC,QAAQ,SACnC,IACI,MAAMC,QAAiBC,MAAM,GAAGnB,2BAAiCK,IAAc,CAC3Ee,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUN,IAC3B,eAAgB,sBAIxB,IAAKG,EAASI,GACV,MAAM,IAAIC,MAAM,6BAA+BL,EAASM,YAG5D,MAAMC,QAAaP,EAASQ,OAC5BnB,EAAgBkB,EAAKE,SAASC,KAClC,CAAE,MAAOnB,GACLC,EAASD,EAAMoB,QACnB,CACJ,GAGJC,EAAe,GAChB,CAACzB,IAqDJ,OAAKF,GAGD4B,EAAAA,EAAAA,KAAA,OACIC,UAAU,sGACVC,QAASA,IAAM7B,GAAW,GAAO8B,UAEjCC,EAAAA,EAAAA,MAAA,OACIH,UAAU,6DACVC,QAAUG,GAAMA,EAAEC,kBAAkBH,SAAA,EAEpCC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,yCAAwCE,SAAA,EACnDH,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAAuBE,SAAC,kCACtCH,EAAAA,EAAAA,KAAA,UACIC,UAAU,oCACVC,QAASA,IAAM7B,GAAW,GAAO8B,SACpC,YAIJzB,IAASsB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcE,SAAEzB,IACxCE,IAAkBoB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBE,SAAEvB,KACpDwB,EAAAA,EAAAA,MAAA,QAAMG,SAzEGxB,UACjByB,EAAMC,iBACN,MAAMzB,EAAQC,aAAaC,QAAQ,SAEnC,IAAKF,EAED,YADAL,EAAS,oCAIb,IAAI+B,EAAYzB,aAAaC,QAAQ,SACjCyB,EAAW1B,aAAaC,QAAQ,SAE/BwB,GAAcC,IACfC,QAAQC,KAAK,iFACbH,EAAYA,GAAa,UACzBC,EAAWA,GAAY,QAG3B,MAAMG,EAAW,GAAGJ,KAAaC,IAEjC,IACI,MAAMxB,QAAiBC,MAAM,GAAGnB,2BAAiCK,IAAc,CAC3Ee,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUN,IAC3B,eAAgB,oBAEpB+B,KAAMC,KAAKC,UAAU,CACjBpB,KAAMtB,EAAa2C,OACnBC,WAAYL,MAIpB,IAAK3B,EAASI,GACV,MAAM,IAAIC,MAAM,8BAAgCL,EAASM,YAG7D,MAAM2B,QAAejC,EAASQ,OAC9Bd,EAAkB,aAAauC,EAAOvB,+BAEtCwB,YAAW,KACPhD,GAAW,GACXQ,EAAkB,GAAG,GACtB,IAEP,CAAE,MAAOH,GACLC,EAASD,EAAMoB,QACnB,GA0BqCK,SAAA,EACzBC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,OAAME,SAAA,EACjBH,EAAAA,EAAAA,KAAA,SAAOsB,QAAQ,OAAOrB,UAAU,aAAYE,SAAC,mBAC7CH,EAAAA,EAAAA,KAAA,SACIuB,KAAK,OACLC,GAAG,OACHC,MAAOlD,EACPmD,SAAWrB,GAAM7B,EAAgB6B,EAAEsB,OAAOF,OAC1CxB,UAAU,4BACV2B,UAAQ,QAGhB5B,EAAAA,EAAAA,KAAA,UACIuB,KAAK,SACLtB,UAAU,gEAA+DE,SAC5E,4BArCM,IA0Cb,EC9HR0B,EAAeA,IAEA,OADH5C,aAAaC,QAAQ,SAIjCjB,EAAUC,+DAyHhB,EAvHgC4D,KAC5B,MAAOC,EAAYC,IAAiBvD,EAAAA,EAAAA,UAAS,KACtCwD,EAAcC,IAAmBzD,EAAAA,EAAAA,WAAS,IAC1C0D,EAASC,IAAc3D,EAAAA,EAAAA,WAAS,IAChC4D,EAAoBC,IAAyB7D,EAAAA,EAAAA,UAAS,OACtDC,EAAOC,IAAYF,EAAAA,EAAAA,UAAS,OASnCK,EAAAA,EAAAA,YAAU,KACkBC,WACpB,IAAK8C,IAGD,OAFAlD,EAAS,uCACTyD,GAAW,GAIf,MAAMpD,EAAQC,aAAaC,QAAQ,SAEnC,IACI,MAAMC,QAAiBC,MAAM,GAAGnB,0BAAiC,CAC7DoB,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUN,IAC3B,eAAgB,sBAIxB,IAAKG,EAASI,GACV,MAAM,IAAIC,MAAM,gCAAkCL,EAASM,YAG/D,MAAMC,QAAaP,EAASQ,OAC5BqC,EAActC,EAAKqC,WAAWQ,KAAI3C,IAAQ,CACtC4B,GAAI5B,EAAS4B,GACb3B,KAAMD,EAASC,KACf2C,WAAY5C,EAAS4C,WACrBrB,WAAYvB,EAASuB,eAE7B,CAAE,MAAOzC,GACLC,EAASD,EAAMoB,QACnB,CAAC,QACGsC,GAAW,EACf,GAGJK,EAAiB,GAClB,IAkCH,OAAI/D,GACOsB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcE,SAAEzB,IAGtCyD,GACOnC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeE,SAAC,eAGhB,IAAtB4B,EAAWW,QACJ1C,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeE,SAAC,uBAItCC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACIH,EAAAA,EAAAA,KAAC2C,EAAAA,EAAY,CACTC,aAAcb,EACdc,YA/FQ,CAChB,CAAEC,MAAO,KAAMC,IAAK,MACpB,CAAED,MAAO,gBAAiBC,IAAK,QAC/B,CAAED,MAAO,aAAcC,IAAK,cAC5B,CAAED,MAAO,aAAcC,IAAK,eA4FpBC,SAjDSjE,UACjB,IAAK8C,IAED,YADAlD,EAAS,kCAIb,MAAMK,EAAQC,aAAaC,QAAQ,SAEnC,IACI,MAAMC,QAAiBC,MAAM,GAAGnB,2BAAiCuD,IAAM,CACnEnC,OAAQ,SACRC,QAAS,CACL,cAAiB,UAAUN,IAC3B,eAAgB,sBAIxB,IAAKG,EAASI,GACV,MAAM,IAAIC,MAAM,8BAAgCL,EAASM,YAG7DuC,GAAciB,GAAkBA,EAAeC,QAAOtD,GAAYA,EAAS4B,KAAOA,KACtF,CAAE,MAAO9C,GACLC,EAASD,EAAMoB,QACnB,GA0BQqD,OAvBQ3B,IAChBc,EAAsBd,GACtBU,GAAgB,EAAK,EAsBbA,gBAAiBA,EACjBkB,qBAAsBd,IAEzBL,IACGjC,EAAAA,EAAAA,KAACqD,EAAuB,CACpBjF,UAAW6D,EACX5D,WAAY6D,EACZ5D,WAAY+D,MAGlB,ECtGd,EAhB4BiB,KAExBtD,EAAAA,EAAAA,KAAAuD,EAAAA,SAAA,CAAApD,UAGAH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCE,UACrDC,EAAAA,EAAAA,MAACoD,EAAAA,EAAmB,CAAArD,SAAA,EAClBH,EAAAA,EAAAA,KAACyD,EAAAA,EAAW,CAACC,UAAU,uBAAuBC,WAAW,yBACzD3D,EAAAA,EAAAA,KAAC8B,EAAuB,KACxB9B,EAAAA,EAAAA,KAAC4D,EAAAA,EAAe,U,wECdxB,MAAM3F,EAAUC,+DAgJhB,EA9IkBC,IAA6C,IAA5C,UAAEC,EAAS,WAAEC,EAAU,YAAEwF,GAAa1F,EACrD,MAAO2F,EAAWC,IAAgBtF,EAAAA,EAAAA,UAAS,KACpCC,EAAOC,IAAYF,EAAAA,EAAAA,UAAS,KAC5BG,EAAgBC,IAAqBJ,EAAAA,EAAAA,UAAS,KAC9CuF,EAAcC,IAAmBxF,EAAAA,EAAAA,UAAS,OAGjDK,EAAAA,EAAAA,YAAU,KACN,MAAMoF,EAASjF,aAAaC,QAAQ,WAChCgF,GACAD,EAAgBC,EACpB,GACD,KAEHpF,EAAAA,EAAAA,YAAU,KACaC,WACf,GAAI8E,EAAa,CACb,MAAM7E,EAAQC,aAAaC,QAAQ,SACnC,IACI,MAAMC,QAAiBC,MAAM,GAAGnB,YAAkB4F,IAAe,CAC7DxE,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUN,IAC3B,eAAgB,sBAIxB,IAAKG,EAASI,GACV,MAAM,IAAIC,MAAM,gCAAkCL,EAASM,YAG/D,MAAMC,QAAaP,EAASQ,OAC5BoE,EAAarE,EAAKyE,MAAMtE,KAC5B,CAAE,MAAOnB,GACLC,EAASD,EAAMoB,QACnB,CACJ,GAGJsE,EAAY,GACb,CAACP,IAwDJ,OAAKzF,GAGD4B,EAAAA,EAAAA,KAAA,OACIC,UAAU,sGACVC,QAASA,IAAM7B,GAAW,GAAO8B,UAEjCC,EAAAA,EAAAA,MAAA,OACIH,UAAU,6DACVC,QAAUG,GAAMA,EAAEC,kBAAmBH,SAAA,EAErCC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,yCAAwCE,SAAA,EACnDH,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAAuBE,SAAC,wBACtCH,EAAAA,EAAAA,KAAA,UACIC,UAAU,oCACVC,QAASA,IAAM7B,GAAW,GAAO8B,SACpC,YAIJzB,IAASsB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcE,SAAEzB,IACxCE,IAAkBoB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBE,SAAEvB,KACpDwB,EAAAA,EAAAA,MAAA,QAAMG,SA5EGxB,UACjByB,EAAMC,iBACN,MAAMzB,EAAQC,aAAaC,QAAQ,SAE7BmF,EAAYL,EAElB,GAAKK,EAKL,GAAKrF,EAKL,IACI,MAAMG,QAAiBC,MAAM,GAAGnB,YAAkB4F,IAAe,CAC7DxE,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUN,IAC3B,eAAgB,oBAEpB+B,KAAMC,KAAKC,UAAU,CACjBpB,KAAMiE,EAAU5C,OAChBC,WAAYkD,MAIpB,IAAKlF,EAASI,GACV,MAAM,IAAIC,MAAM,iCAAmCL,EAASM,YAGhE,MAAM2B,QAAejC,EAASQ,QAG9B2E,EAAAA,EAAAA,IAAa,CACTC,KAAM,UACNC,MAAO,WACPC,MAAY,OAANrD,QAAM,IAANA,OAAM,EAANA,EAAQtB,UAAW,sCAI7BuB,YAAW,KACPhD,GAAW,GACXQ,EAAkB,GAAG,GACtB,IAEP,CAAE,MAAOH,IACL4F,EAAAA,EAAAA,IAAa,QACjB,MAtCI3F,EAAS,yCALTA,EAAS,yBA2Cb,EA0BqCwB,SAAA,EACzBC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,OAAME,SAAA,EACjBH,EAAAA,EAAAA,KAAA,SAAOsB,QAAQ,OAAOrB,UAAU,aAAYE,SAAC,sBAC7CH,EAAAA,EAAAA,KAAA,SACIuB,KAAK,OACLC,GAAG,OACHC,MAAOqC,EACPpC,SAAWrB,GAAM0D,EAAa1D,EAAEsB,OAAOF,OACvCxB,UAAU,4BACV2B,UAAQ,QAGhB5B,EAAAA,EAAAA,KAAA,UACIuB,KAAK,SACLtB,UAAU,gEAA+DE,SAC5E,+BArCM,IA0Cb,C", "sources": ["pages/blood/BloodList.jsx", "pages/settings/noticeboardcategory/EditNoticeBoardCategory.jsx", "pages/settings/noticeboardcategory/NoticeBoardCategoryList.jsx", "dashboard/settings/NoticeBoardCategory.jsx", "pages/blood/EditBlood.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport TableContent from '../../common/table/TableContent';\r\nimport EditBlood from './EditBlood';\r\n\r\nconst isTokenValid = () => {\r\n    const token = localStorage.getItem('token');\r\n    return token !== null; // Additional validation logic can be added here\r\n};\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst BloodList = () => {\r\n    const [bloods, setBloods] = useState([]);\r\n    const [modalVisible, setModalVisible] = useState(false);\r\n    const [loading, setLoading] = useState(true); // Initially loading is true\r\n    const [selectedBloodId, setSelectedBloodId] = useState(null);\r\n    const [error, setError] = useState(null);\r\n\r\n    // Update column names for Blood group\r\n    const columnNames = [\r\n        { label: \"SL\", key: \"id\" },\r\n        { label: \"Blood Group\", key: \"name\" },\r\n        { label: \"Created By\", key: \"created_by\" },\r\n        { label: \"Updated By\", key: \"updated_by\" },\r\n    ];\r\n\r\n    useEffect(() => {\r\n        const fetchBloods = async () => {\r\n            if (!isTokenValid()) {\r\n                setError('No authentication token found.');\r\n                setLoading(false);\r\n                return;\r\n            }\r\n\r\n            const token = localStorage.getItem('token');\r\n\r\n            try {\r\n                const response = await fetch(`${API_URL}/bloods`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n                if (!response.ok) {\r\n                    throw new Error('Network response was not ok: ' + response.statusText);\r\n                }\r\n\r\n                const data = await response.json();\r\n\r\n                setBloods(data.bloods.map(blood => ({\r\n                    id: blood.id,\r\n                    name: blood.name,\r\n                    created_by: blood.created_by,\r\n                    updated_by: blood.updated_by,\r\n                })));\r\n            } catch (error) {\r\n                setError(error.message);\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchBloods();\r\n    }, []);\r\n\r\n    // Handle Delete\r\n    const handleDelete = async (id) => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n\r\n        const token = localStorage.getItem('token');\r\n\r\n        try {\r\n            const response = await fetch(`${API_URL}/bloods/${id}`, {\r\n                method: 'DELETE',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Failed to delete blood group: ' + response.statusText);\r\n            }\r\n\r\n            // Update the blood group list after deletion\r\n            setBloods(prevBloods => prevBloods.filter(blood => blood.id !== id));\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n\r\n    // Handle Edit\r\n    const handleEdit = (id) => {\r\n        setSelectedBloodId(id);\r\n        setModalVisible(true);\r\n    };\r\n\r\n    if (error) {\r\n        return <div className=\"text-red-500\">{error}</div>;\r\n    }\r\n\r\n    if (loading) {\r\n        return <div className=\"text-gray-500\">Loading...</div>;\r\n    }\r\n\r\n    // Show message when no bloods are available\r\n    if (bloods.length === 0) {\r\n        return <div className=\"text-gray-500\">No data available</div>; // Show \"No data available\" if bloods array is empty\r\n    }\r\n\r\n    return (\r\n        <div>\r\n            <TableContent\r\n                tableContent={bloods}\r\n                columnNames={columnNames}\r\n                onDelete={handleDelete}\r\n                onEdit={handleEdit}\r\n                setModalVisible={setModalVisible}\r\n                setSelectedServiceId={setSelectedBloodId}\r\n            />\r\n            {modalVisible && (\r\n                <EditBlood\r\n                    isVisible={modalVisible}\r\n                    setVisible={setModalVisible}\r\n                    bloodId={selectedBloodId}\r\n                />\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default BloodList;\r\n", "import React, { useEffect, useState } from 'react';\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst EditNoticeBoardCategory = ({ isVisible, setVisible, categoryId }) => {\r\n    const [categoryName, setCategoryName] = useState('');\r\n    const [error, setError] = useState('');\r\n    const [successMessage, setSuccessMessage] = useState('');\r\n\r\n    useEffect(() => {\r\n        const fetchCategory = async () => {\r\n            if (categoryId) {\r\n                const token = localStorage.getItem('token');\r\n                try {\r\n                    const response = await fetch(`${API_URL}/notice-board-category/${categoryId}`, {\r\n                        method: 'GET',\r\n                        headers: {\r\n                            'Authorization': `Bearer ${token}`,\r\n                            'Content-Type': 'application/json',\r\n                        },\r\n                    });\r\n\r\n                    if (!response.ok) {\r\n                        throw new Error('Failed to fetch category: ' + response.statusText);\r\n                    }\r\n\r\n                    const data = await response.json();\r\n                    setCategoryName(data.category.name);\r\n                } catch (error) {\r\n                    setError(error.message);\r\n                }\r\n            }\r\n        };\r\n\r\n        fetchCategory();\r\n    }, [categoryId]);\r\n\r\n    const handleSubmit = async (event) => {\r\n        event.preventDefault();\r\n        const token = localStorage.getItem('token');\r\n        \r\n        if (!token) {\r\n            setError('Authentication token is missing.');\r\n            return;\r\n        }\r\n    \r\n        let firstName = localStorage.getItem('fname');\r\n        let lastName = localStorage.getItem('lname');\r\n    \r\n        if (!firstName || !lastName) {\r\n            console.warn(\"User first and last name are missing in localStorage. Setting default values.\");\r\n            firstName = firstName || \"Unknown\";\r\n            lastName = lastName || \"User\";\r\n        }\r\n    \r\n        const fullName = `${firstName} ${lastName}`;\r\n    \r\n        try {\r\n            const response = await fetch(`${API_URL}/notice-board-category/${categoryId}`, {\r\n                method: 'PUT',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n                body: JSON.stringify({\r\n                    name: categoryName.trim(),\r\n                    updated_by: fullName,\r\n                }),\r\n            });\r\n    \r\n            if (!response.ok) {\r\n                throw new Error('Failed to update category: ' + response.statusText);\r\n            }\r\n    \r\n            const result = await response.json();\r\n            setSuccessMessage(`Category \"${result.name}\" updated successfully!`);\r\n    \r\n            setTimeout(() => {\r\n                setVisible(false);\r\n                setSuccessMessage('');\r\n            }, 1000);\r\n            \r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n    \r\n    \r\n    if (!isVisible) return null;\r\n\r\n    return (\r\n        <div\r\n            className=\"fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50\"\r\n            onClick={() => setVisible(false)}\r\n        >\r\n            <div\r\n                className=\"relative bg-white rounded-lg shadow-lg max-w-md w-full p-5\"\r\n                onClick={(e) => e.stopPropagation()}\r\n            >\r\n                <div className=\"flex justify-between items-center mb-4\">\r\n                    <h3 className=\"text-lg font-semibold\">Update Notice Board Category</h3>\r\n                    <button\r\n                        className=\"text-gray-500 hover:text-gray-800\"\r\n                        onClick={() => setVisible(false)}\r\n                    >\r\n                        &times;\r\n                    </button>\r\n                </div>\r\n                {error && <div className=\"text-red-500\">{error}</div>}\r\n                {successMessage && <div className=\"text-green-500\">{successMessage}</div>}\r\n                <form onSubmit={handleSubmit}>\r\n                    <div className=\"mb-4\">\r\n                        <label htmlFor=\"name\" className=\"block mb-2\">Category Name</label>\r\n                        <input\r\n                            type=\"text\"\r\n                            id=\"name\"\r\n                            value={categoryName}\r\n                            onChange={(e) => setCategoryName(e.target.value)}\r\n                            className=\"border rounded w-full p-2\"\r\n                            required\r\n                        />\r\n                    </div>\r\n                    <button\r\n                        type=\"submit\"\r\n                        className=\"bg-primary hover:bg-secondary text-white rounded-md px-4 py-2\"\r\n                    >\r\n                        Update Category\r\n                    </button>\r\n                </form>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default EditNoticeBoardCategory;\r\n", "import React, { useEffect, useState } from 'react';\r\nimport TableContent from '../../../common/table/TableContent';\r\nimport EditNoticeBoardCategory from './EditNoticeBoardCategory';\r\n\r\nconst isTokenValid = () => {\r\n    const token = localStorage.getItem('token');\r\n    return token !== null;\r\n};\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst NoticeBoardCategoryList = () => {\r\n    const [categories, setCategories] = useState([]);\r\n    const [modalVisible, setModalVisible] = useState(false);\r\n    const [loading, setLoading] = useState(true);\r\n    const [selectedCategoryId, setSelectedCategoryId] = useState(null);\r\n    const [error, setError] = useState(null);\r\n\r\n    const columnNames = [\r\n        { label: \"SL\", key: \"id\" },\r\n        { label: \"Category Name\", key: \"name\" },\r\n        { label: \"Created By\", key: \"created_by\" },\r\n        { label: \"Updated By\", key: \"updated_by\" },\r\n    ];\r\n\r\n    useEffect(() => {\r\n        const fetchCategories = async () => {\r\n            if (!isTokenValid()) {\r\n                setError('No authentication token found.');\r\n                setLoading(false);\r\n                return;\r\n            }\r\n\r\n            const token = localStorage.getItem('token');\r\n\r\n            try {\r\n                const response = await fetch(`${API_URL}/notice-board-category`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n                if (!response.ok) {\r\n                    throw new Error('Network response was not ok: ' + response.statusText);\r\n                }\r\n\r\n                const data = await response.json();\r\n                setCategories(data.categories.map(category => ({\r\n                    id: category.id,\r\n                    name: category.name,\r\n                    created_by: category.created_by,\r\n                    updated_by: category.updated_by,\r\n                })));\r\n            } catch (error) {\r\n                setError(error.message);\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchCategories();\r\n    }, []);\r\n\r\n    const handleDelete = async (id) => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n\r\n        const token = localStorage.getItem('token');\r\n\r\n        try {\r\n            const response = await fetch(`${API_URL}/notice-board-category/${id}`, {\r\n                method: 'DELETE',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Failed to delete category: ' + response.statusText);\r\n            }\r\n\r\n            setCategories(prevCategories => prevCategories.filter(category => category.id !== id));\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n\r\n    const handleEdit = (id) => {\r\n        setSelectedCategoryId(id);\r\n        setModalVisible(true);\r\n    };\r\n\r\n    if (error) {\r\n        return <div className=\"text-red-500\">{error}</div>;\r\n    }\r\n\r\n    if (loading) {\r\n        return <div className=\"text-gray-500\">Loading...</div>;\r\n    }\r\n\r\n    if (categories.length === 0) {\r\n        return <div className=\"text-gray-500\">No data available</div>;\r\n    }\r\n\r\n    return (\r\n        <div>\r\n            <TableContent\r\n                tableContent={categories}\r\n                columnNames={columnNames}\r\n                onDelete={handleDelete}\r\n                onEdit={handleEdit}\r\n                setModalVisible={setModalVisible}\r\n                setSelectedServiceId={setSelectedCategoryId}\r\n            />\r\n            {modalVisible && (\r\n                <EditNoticeBoardCategory\r\n                    isVisible={modalVisible}\r\n                    setVisible={setModalVisible}\r\n                    categoryId={selectedCategoryId}\r\n                />\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default NoticeBoardCategoryList;\r\n", "\r\nimport React from 'react';\r\nimport TableLayoutWrapper2 from '../../common/table/TableLayoutWrapper2';\r\nimport TableHeader from '../../common/table/TableHeader';\r\nimport TablePagination from '../../common/table/TablePagination';\r\nimport BloodList from '../../pages/blood/BloodList';\r\nimport NoticeBoardCategoryList from '../../pages/settings/noticeboardcategory/NoticeBoardCategoryList';\r\n\r\nconst NoticeBoardCategory = () => {\r\n  return (\r\n    <>\r\n    \r\n   \r\n    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>\r\n      <TableLayoutWrapper2>\r\n        <TableHeader routeName=\"/add-notice-category\" buttonName=\"Add Notice Category\" />\r\n        <NoticeBoardCategoryList/>\r\n        <TablePagination />\r\n      </TableLayoutWrapper2>\r\n    </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default NoticeBoardCategory;\r\n", "import React, { useEffect, useState } from 'react';\r\nimport { alertMessage } from '../../common/coreui';\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst EditBlood = ({ isVisible, setVisible, dataItemsId }) => {\r\n    const [bloodName, setBloodName] = useState('');\r\n    const [error, setError] = useState('');\r\n    const [successMessage, setSuccessMessage] = useState('');\r\n    const [loggedInUser, setLoggedInUser] = useState(null);\r\n\r\n    // Fetch logged-in user data (user_id)\r\n    useEffect(() => {\r\n        const userId = localStorage.getItem('user_id');\r\n        if (userId) {\r\n            setLoggedInUser(userId);\r\n        }\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        const fetchBlood = async () => {\r\n            if (dataItemsId) {\r\n                const token = localStorage.getItem('token');\r\n                try {\r\n                    const response = await fetch(`${API_URL}/bloods/${dataItemsId}`, {\r\n                        method: 'GET',\r\n                        headers: {\r\n                            'Authorization': `Bearer ${token}`,\r\n                            'Content-Type': 'application/json',\r\n                        },\r\n                    });\r\n\r\n                    if (!response.ok) {\r\n                        throw new Error('Failed to fetch blood group: ' + response.statusText);\r\n                    }\r\n\r\n                    const data = await response.json();\r\n                    setBloodName(data.blood.name); // Update this line to access blood group name correctly\r\n                } catch (error) {\r\n                    setError(error.message);\r\n                }\r\n            }\r\n        };\r\n\r\n        fetchBlood();\r\n    }, [dataItemsId]); // Re-fetch when dataItemsId changes\r\n\r\n    const handleSubmit = async (event) => {\r\n        event.preventDefault(); // Prevent default form submission behavior\r\n        const token = localStorage.getItem('token');\r\n\r\n        const updatedBy = loggedInUser;\r\n\r\n        if (!updatedBy) {\r\n            setError('User is not logged in.');\r\n            return;\r\n        }\r\n        \r\n        if (!token) {\r\n            setError('Authentication token is missing.');\r\n            return; // Exit if token is not available\r\n        }\r\n    \r\n        try {\r\n            const response = await fetch(`${API_URL}/bloods/${dataItemsId}`, {\r\n                method: 'PUT',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n                body: JSON.stringify({\r\n                    name: bloodName.trim(), // The updated blood group name\r\n                    updated_by: updatedBy,\r\n                }),\r\n            });\r\n    \r\n            if (!response.ok) {\r\n                throw new Error('Failed to update blood group: ' + response.statusText);\r\n            }\r\n    \r\n            const result = await response.json();\r\n            //setSuccessMessage(`Blood group \"${result.name}\" updated successfully!`);\r\n\r\n            alertMessage({\r\n                icon: 'success',\r\n                title: 'Success!',\r\n                text: result?.message || 'Blood group updated successfully.',\r\n            });\r\n    \r\n            // Close the modal after a short delay\r\n            setTimeout(() => {\r\n                setVisible(false);\r\n                setSuccessMessage(''); // Clear the success message\r\n            }, 1000);\r\n            \r\n        } catch (error) {\r\n            alertMessage('error');\r\n        }\r\n    };\r\n    \r\n\r\n    if (!isVisible) return null; // Don't render the modal if not visible\r\n\r\n    return (\r\n        <div\r\n            className=\"fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50\"\r\n            onClick={() => setVisible(false)}\r\n        >\r\n            <div\r\n                className=\"relative bg-white rounded-lg shadow-lg max-w-md w-full p-5\"\r\n                onClick={(e) => e.stopPropagation()} // Prevent click from closing the modal\r\n            >\r\n                <div className=\"flex justify-between items-center mb-4\">\r\n                    <h3 className=\"text-lg font-semibold\">Update Blood Group</h3>\r\n                    <button\r\n                        className=\"text-gray-500 hover:text-gray-800\"\r\n                        onClick={() => setVisible(false)}\r\n                    >\r\n                        &times;\r\n                    </button>\r\n                </div>\r\n                {error && <div className=\"text-red-500\">{error}</div>}\r\n                {successMessage && <div className=\"text-green-500\">{successMessage}</div>}\r\n                <form onSubmit={handleSubmit}>\r\n                    <div className=\"mb-4\">\r\n                        <label htmlFor=\"name\" className=\"block mb-2\">Blood Group Name</label>\r\n                        <input\r\n                            type=\"text\"\r\n                            id=\"name\"\r\n                            value={bloodName}\r\n                            onChange={(e) => setBloodName(e.target.value)}\r\n                            className=\"border rounded w-full p-2\"\r\n                            required\r\n                        />\r\n                    </div>\r\n                    <button\r\n                        type=\"submit\"\r\n                        className=\"bg-primary hover:bg-secondary text-white rounded-md px-4 py-2\"\r\n                    >\r\n                        Update Blood Group\r\n                    </button>\r\n                </form>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default EditBlood;\r\n"], "names": ["API_URL", "process", "_ref", "isVisible", "setVisible", "categoryId", "categoryName", "setCategoryName", "useState", "error", "setError", "successMessage", "setSuccessMessage", "useEffect", "async", "token", "localStorage", "getItem", "response", "fetch", "method", "headers", "ok", "Error", "statusText", "data", "json", "category", "name", "message", "fetchCategory", "_jsx", "className", "onClick", "children", "_jsxs", "e", "stopPropagation", "onSubmit", "event", "preventDefault", "firstName", "lastName", "console", "warn", "fullName", "body", "JSON", "stringify", "trim", "updated_by", "result", "setTimeout", "htmlFor", "type", "id", "value", "onChange", "target", "required", "isTokenValid", "NoticeBoardCategoryList", "categories", "setCategories", "modalVisible", "setModalVisible", "loading", "setLoading", "selectedCategoryId", "setSelectedCategoryId", "map", "created_by", "fetchCategories", "length", "TableContent", "tableContent", "columnNames", "label", "key", "onDelete", "prevCategories", "filter", "onEdit", "setSelectedServiceId", "EditNoticeBoardCategory", "NoticeBoardCategory", "_Fragment", "TableLayoutWrapper2", "TableHeader", "routeName", "buttonName", "TablePagination", "dataItemsId", "bloodName", "setBloodName", "loggedInUser", "setLoggedInUser", "userId", "blood", "fetchBlood", "updatedBy", "alertMessage", "icon", "title", "text"], "sourceRoot": ""}