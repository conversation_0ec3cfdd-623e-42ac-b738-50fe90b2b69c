{"ast": null, "code": "import React from'react';import{createBrowserRouter,Navigate}from'react-router-dom';import MainLayout from'./dashboard/MainLayout';import Login from'./common/Login';import Dashboard from'./dashboard/Dashboard';import UserList from'./pages/team-member/TeamMemberList';import Teams from'./dashboard/settings/Teams';import AddLocation from'./pages/location/AddLocation';import AddBranch from'./pages/branch/AddBranch';import Department from'./dashboard/settings/Department';import AddTeam from'./pages/team/AddTeam';import AddMember from'./pages/team-member/AddMember';import Settings from'./dashboard/Settings';import AddRole from'./pages/role/AddRole';import AddDepartment from'./pages/department/AddDepartment';import AddBillingStatus from'./pages/billing-status/AddBillingStatus';import AddResourceStatus from'./pages/resource-status/AddResourceStatus';import AddResourceType from'./pages/resource-type/AddResourceType';import AddDesignation from'./pages/designation/AddDesignation';import MemberIndex from'./dashboard/MemberIndex';import AddBlood from'./pages/blood/AddBlood';import AddAvailableStatus from'./pages/available-status/AddAvailableStatus';import AddContactType from'./pages/contact-type/AddContactType';import AddMemberStatus from'./pages/member-status/AddMemberStatus';import AddOnsiteStatus from'./pages/onsite-status/AddOnsiteStatus';import MemberOnboard from'./dashboard/MemberOnboard';import AddSchedule from'./pages/schedule/AddSchedule';import ProtectedRoute from'./route/ProtectedRoute';import TeamContacts from'./dashboard/TeamContacts';// Abdur Rahman\nimport Holiday from'./dashboard/Holiday';import QuickAccess from'./dashboard/QuickAccessHubs';import Training from'./dashboard/Training';import AddHolidayCalender from'./pages/holiday-calender/AddHolidayCalender';import AddTrainingCategory from'./pages/training/training-category/AddTrainingCategory';import AddTrainingTopic from'./pages/training/training-topic/AddTrainingTopic';import AddQuickAccessHub from'./pages/quickaccesshub/AddQuickAccessHub';import AddTraining from'./pages/training/AddTraining';import TaskDetails from'./dashboard/task-details/TaskDetails';import Formation from'./dashboard/task-details/Formation';import AddTaskRecord from'./pages/task-details/task-record/AddTaskRecord';import AddTimeCard from'./pages/time-card/AddTimeCard';import TimeCard from'./dashboard/time-card/TimeCard';import AddTeamShiftPlan from'./pages/team-shift-plan/AddTeamShiftPlan';import TeamShiftPlan from'./dashboard/TeamShiftPlan';import Profile from'./dashboard/Profile';import WorldTime from'./pages/world-time/WorldTime';import TimeZoneConvert from'./pages/world-time/TimeZoneConvert';import AttendanceFormation from'./pages/attendance/AttendanceFormation/AttendanceFormationList';import Attendance from'./pages/attendance/Attendance/Attendance';import SchedulePlaners from'./pages/schedule-planers/SchedulePlaners';// Imran Ahmed\nimport Todo from'./dashboard/Todo';import AddTodo from'./pages/todo/commonTodo/AddTodo';import Abouttheapp from'./dashboard/Abouttheapp';import AddAboutTheApp from'./pages/about-the-app/AddAboutTheApp';import AddChangeLog from'./pages/change-log/AddChangeLog';import Changelog from'./dashboard/Changelog';import AddReporter from'./pages/time-card/reporter/AddReporter';import Reporter from'./dashboard/time-card/Reporter';import Appsupport from'./dashboard/Appsupport';import AddAppsupport from'./pages/app-support/AddAppsupport';import Givefeedback from'./dashboard/Givefeedback';import Reportproblem from'./dashboard/Reportproblem';import AddGiveFeedback from'./pages/give-feedback/AddGiveFeedback';import AddReportProblem from'./pages/report-problem/AddReportProblem';import Teamsnapshot from'./dashboard/Teamsnapshot';import Notice from'./dashboard/NoticeBoard';import NoticeBoard from'./dashboard/NoticeBoard';import AddNoticeBoardCategory from'./pages/settings/noticeboardcategory/AddNoticeBoardCategory';import AddNotice from'./pages/notice/AddNotice';import HolidayCalendarGoogleList from'./pages/holiday-calender/HolidayCalenderGoogleList';//import OfficeSeatPlan from './pages/holiday-calender/OfficeSeatPlan';\nimport OfficeSeatPlan from'./pages/seat-plan/OfficeSeatPlan';import NotFound from'./common/utility/NotFound';import Unauthorized from'./common/utility/UnAuthorized';import ResetPassword from'./common/login/ResetPassword';import UpdatePassword from'./common/login/UpdatePassword';import Welcome from'./dashboard/Welcome';import Creativetools from'./dashboard/Creativetools';// Define your routes\nimport{jsx as _jsx}from\"react/jsx-runtime\";const CreativeRoutes=[{path:'/login',element:/*#__PURE__*/_jsx(Login,{})},// {\n//   path: '*',\n//   element: <NotFound />,\n// },\n{path:'reset-password',element:/*#__PURE__*/_jsx(ResetPassword,{})},{path:'/password/reset/:token',element:/*#__PURE__*/_jsx(UpdatePassword,{})},{path:'world-time-share',element:/*#__PURE__*/_jsx(WorldTime,{})},{path:'time-zone-convert-share',element:/*#__PURE__*/_jsx(TimeZoneConvert,{})},{path:'/',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead','coordinator','shift-lead','team-member'],children:/*#__PURE__*/_jsx(MainLayout,{})}),children:[{path:'/',element:/*#__PURE__*/_jsx(Dashboard,{})// Default to Dashboard\n},{path:'unauthorized',element:/*#__PURE__*/_jsx(Unauthorized,{})},{path:'member-onboard',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin'],children:/*#__PURE__*/_jsx(MemberOnboard,{})})},{path:'world-time',element:/*#__PURE__*/_jsx(WorldTime,{})},{path:'time-zone-convert',element:/*#__PURE__*/_jsx(TimeZoneConvert,{})},{path:'time-zone-convert',element:/*#__PURE__*/_jsx(TimeZoneConvert,{})},{path:'attendance',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead','coordinator','shift-lead','team-member'],children:/*#__PURE__*/_jsx(Attendance,{})})},{path:'attendance-formation',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AttendanceFormation,{})})},{path:'team-members',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead','coordinator','shift-lead','team-member'],children:/*#__PURE__*/_jsx(TeamContacts,{})})},{path:'member-index',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin'],children:/*#__PURE__*/_jsx(MemberIndex,{})})},{path:'profile',element:/*#__PURE__*/_jsx(Profile,{})},{path:'add-member',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin'],children:/*#__PURE__*/_jsx(AddMember,{})})},{path:'teams',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(Teams,{})})},{path:'add-team',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddTeam,{})})},{path:'add-role',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead','coordinator','shift-lead','team-member'],children:/*#__PURE__*/_jsx(AddRole,{})})},{path:'departments',element:/*#__PURE__*/_jsx(Department,{}),element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(Department,{})})},{path:'add-department',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddDepartment,{})})},{path:'add-billing-status',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddBillingStatus,{})})},{path:'add-resource-status',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddResourceStatus,{})})},{path:'add-resource-type',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddResourceType,{})})},{path:'add-designation',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddDesignation,{})})},{path:'add-location',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddLocation,{})})},{path:'add-branch',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddBranch,{})})},{path:'add-schedule',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddSchedule,{})})},{path:'add-available-status',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddAvailableStatus,{})})},{path:'add-contact-type',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddContactType,{})})},{path:'add-blood',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddBlood,{})})},{path:'add-member-status',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddMemberStatus,{})})},{path:'add-onsite-status',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddOnsiteStatus,{})})},{path:'settings',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin'],children:/*#__PURE__*/_jsx(Settings,{})})},// Task Details\n{path:'add-task',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead','coordinator','shift-lead'],children:/*#__PURE__*/_jsx(AddTaskRecord,{})})},{path:'task-records',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead','coordinator','shift-lead'],children:/*#__PURE__*/_jsx(TaskDetails,{})})},{path:'formation',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(Formation,{})})},{path:'add-reporter',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead','coordinator','shift-lead'],children:/*#__PURE__*/_jsx(AddReporter,{})})},{path:'reporters',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead','coordinator','shift-lead','team-member'],children:/*#__PURE__*/_jsx(Reporter,{})})},// Routes for All Users\n{path:'add-time',element:/*#__PURE__*/_jsx(AddTimeCard,{})},{path:'time-cards',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead','coordinator','shift-lead','team-member'],children:/*#__PURE__*/_jsx(TimeCard,{})})},// Abdur Rahman\n{path:'holidaycalenders',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead','coordinator','shift-lead','team-member'],children:/*#__PURE__*/_jsx(Holiday,{})})},{path:'add-holiday',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddHolidayCalender,{})})},{path:'quickaccesshub',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead','coordinator','shift-lead','team-member'],children:/*#__PURE__*/_jsx(QuickAccess,{})})},{path:'add-hub',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddQuickAccessHub,{})})},{path:'training',element:/*#__PURE__*/_jsx(Training,{})},{path:'add-training',element:/*#__PURE__*/_jsx(AddTraining,{})},{path:'add-training-category',element:/*#__PURE__*/_jsx(AddTrainingCategory,{})},{path:'add-trainingtopic',element:/*#__PURE__*/_jsx(AddTrainingTopic,{})},{path:'add-team-shift-plan',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead'],children:/*#__PURE__*/_jsx(AddTeamShiftPlan,{})})},{path:'shift-plan',element:/*#__PURE__*/_jsx(TeamShiftPlan,{})},{path:'schedule-planners',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead','shift-lead'],children:/*#__PURE__*/_jsx(SchedulePlaners,{})})},{path:'achieve',element:/*#__PURE__*/_jsx(Todo,{})},{path:'achieve/add-todo',element:/*#__PURE__*/_jsx(AddTodo,{})},{path:'about-the-app',element:/*#__PURE__*/_jsx(Abouttheapp,{})},{path:'add-about-the-app',element:/*#__PURE__*/_jsx(AddAboutTheApp,{})},{path:'change-log',element:/*#__PURE__*/_jsx(Changelog,{})},{path:'add-change-log',element:/*#__PURE__*/_jsx(AddChangeLog,{})},{path:'app-support',element:/*#__PURE__*/_jsx(Appsupport,{})},{path:'add-app-support',element:/*#__PURE__*/_jsx(AddAppsupport,{})},{path:'give-feedback',element:/*#__PURE__*/_jsx(Givefeedback,{})},{path:'add-feedback',element:/*#__PURE__*/_jsx(AddGiveFeedback,{})},{path:'report-problem',element:/*#__PURE__*/_jsx(Reportproblem,{})},{path:'add-report-problem',element:/*#__PURE__*/_jsx(AddReportProblem,{})},{path:'team-snapshot',element:/*#__PURE__*/_jsx(Teamsnapshot,{})},{path:'noticeboard',element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRoles:['admin','super-admin','hod','manager','team-lead','coordinator','shift-lead'],children:/*#__PURE__*/_jsx(NoticeBoard,{})})},{path:'add-notice-category',element:/*#__PURE__*/_jsx(AddNoticeBoardCategory,{})},{path:'add-notice',element:/*#__PURE__*/_jsx(AddNotice,{})},{path:'events',element:/*#__PURE__*/_jsx(HolidayCalendarGoogleList,{})},{path:'seat-plan',element:/*#__PURE__*/_jsx(OfficeSeatPlan,{})},{path:'welcome',element:/*#__PURE__*/_jsx(Welcome,{})},{path:'passwordmanager',element:/*#__PURE__*/_jsx(Welcome,{})},{path:'creative-tools',element:/*#__PURE__*/_jsx(Creativetools,{})}]},{path:'users',element:/*#__PURE__*/_jsx(UserList,{})},{path:'*',element:/*#__PURE__*/_jsx(Navigate,{to:\"/login\"})}];// Create the router\nconst router=createBrowserRouter(CreativeRoutes);export default router;", "map": {"version": 3, "names": ["React", "createBrowserRouter", "Navigate", "MainLayout", "<PERSON><PERSON>", "Dashboard", "UserList", "Teams", "AddLocation", "AddBranch", "Department", "AddTeam", "AddMember", "Settings", "AddRole", "AddDepartment", "AddBillingStatus", "AddResourceStatus", "AddResourceType", "AddDesignation", "MemberIndex", "AddBlood", "AddAvailableStatus", "AddContactType", "AddMemberStatus", "AddOnsiteStatus", "MemberOnboard", "AddSchedule", "ProtectedRoute", "TeamContacts", "Holiday", "QuickAccess", "Training", "AddHolidayCalender", "AddTrainingCategory", "AddTrainingTopic", "AddQuickAccessHub", "AddTraining", "TaskDetails", "Formation", "AddTaskRecord", "AddTimeCard", "TimeCard", "AddTeamShiftPlan", "TeamShiftPlan", "Profile", "WorldTime", "TimeZoneConvert", "AttendanceFormation", "Attendance", "SchedulePlaners", "Todo", "AddTodo", "Abouttheapp", "AddAboutTheApp", "AddChangeLog", "Changelog", "AddReport<PERSON>", "Reporter", "Appsupport", "AddAppsupport", "Givefeedback", "Reportproblem", "AddGiveFeedback", "AddReportProblem", "Teamsnapshot", "Notice", "NoticeBoard", "AddNoticeBoardCategory", "AddNotice", "HolidayCalendarGoogleList", "OfficeSeatPlan", "NotFound", "Unauthorized", "ResetPassword", "UpdatePassword", "Welcome", "Creativetools", "jsx", "_jsx", "CreativeRoutes", "path", "element", "requiredRoles", "children", "to", "router"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/routes.js"], "sourcesContent": ["import React from 'react';\r\nimport { createBrowserRouter, Navigate } from 'react-router-dom';\r\nimport MainLayout from './dashboard/MainLayout';\r\n\r\nimport Login from './common/Login';\r\nimport Dashboard from './dashboard/Dashboard';\r\nimport UserList from './pages/team-member/TeamMemberList';\r\nimport Teams from './dashboard/settings/Teams';\r\nimport AddLocation from './pages/location/AddLocation';\r\nimport AddBranch from './pages/branch/AddBranch';\r\nimport Department from './dashboard/settings/Department';\r\nimport AddTeam from './pages/team/AddTeam';\r\nimport AddMember from './pages/team-member/AddMember';\r\nimport Settings from './dashboard/Settings';\r\nimport AddRole from './pages/role/AddRole';\r\nimport AddDepartment from './pages/department/AddDepartment';\r\nimport AddBillingStatus from './pages/billing-status/AddBillingStatus';\r\nimport AddResourceStatus from './pages/resource-status/AddResourceStatus';\r\nimport AddResourceType from './pages/resource-type/AddResourceType';\r\nimport AddDesignation from './pages/designation/AddDesignation';\r\nimport MemberIndex from './dashboard/MemberIndex';\r\nimport AddBlood from './pages/blood/AddBlood';\r\nimport AddAvailableStatus from './pages/available-status/AddAvailableStatus';\r\nimport AddContactType from './pages/contact-type/AddContactType';\r\nimport AddMemberStatus from './pages/member-status/AddMemberStatus';\r\nimport AddOnsiteStatus from './pages/onsite-status/AddOnsiteStatus';\r\nimport MemberOnboard from './dashboard/MemberOnboard';\r\nimport AddSchedule from './pages/schedule/AddSchedule';\r\nimport ProtectedRoute from './route/ProtectedRoute';\r\nimport TeamContacts from './dashboard/TeamContacts';\r\n\r\n// Abdur Rahman\r\nimport Holiday from './dashboard/Holiday';\r\nimport QuickAccess from './dashboard/QuickAccessHubs';\r\nimport Training from './dashboard/Training'\r\nimport AddHolidayCalender from './pages/holiday-calender/AddHolidayCalender';\r\nimport AddTrainingCategory from './pages/training/training-category/AddTrainingCategory';\r\nimport AddTrainingTopic from './pages/training/training-topic/AddTrainingTopic';\r\nimport AddQuickAccessHub from './pages/quickaccesshub/AddQuickAccessHub';\r\nimport AddTraining from './pages/training/AddTraining';\r\nimport TaskDetails from './dashboard/task-details/TaskDetails';\r\nimport Formation from './dashboard/task-details/Formation';\r\nimport AddTaskRecord from './pages/task-details/task-record/AddTaskRecord';\r\nimport AddTimeCard from './pages/time-card/AddTimeCard';\r\nimport TimeCard from './dashboard/time-card/TimeCard';\r\nimport AddTeamShiftPlan from './pages/team-shift-plan/AddTeamShiftPlan';\r\nimport TeamShiftPlan from './dashboard/TeamShiftPlan';\r\nimport Profile from './dashboard/Profile';\r\nimport WorldTime from './pages/world-time/WorldTime';\r\nimport TimeZoneConvert from './pages/world-time/TimeZoneConvert';\r\nimport AttendanceFormation from './pages/attendance/AttendanceFormation/AttendanceFormationList';\r\nimport Attendance from './pages/attendance/Attendance/Attendance';\r\nimport SchedulePlaners from './pages/schedule-planers/SchedulePlaners';\r\n\r\n// Imran Ahmed\r\nimport Todo from './dashboard/Todo';\r\nimport AddTodo from './pages/todo/commonTodo/AddTodo';\r\nimport Abouttheapp from './dashboard/Abouttheapp';\r\nimport AddAboutTheApp from './pages/about-the-app/AddAboutTheApp';\r\nimport AddChangeLog from './pages/change-log/AddChangeLog';\r\nimport Changelog from './dashboard/Changelog';\r\nimport AddReporter from './pages/time-card/reporter/AddReporter';\r\nimport Reporter from './dashboard/time-card/Reporter';\r\nimport Appsupport from './dashboard/Appsupport';\r\nimport AddAppsupport from './pages/app-support/AddAppsupport';\r\nimport Givefeedback from './dashboard/Givefeedback';\r\nimport Reportproblem from './dashboard/Reportproblem';\r\nimport AddGiveFeedback from './pages/give-feedback/AddGiveFeedback';\r\nimport AddReportProblem from './pages/report-problem/AddReportProblem';\r\nimport Teamsnapshot from './dashboard/Teamsnapshot';\r\nimport Notice from './dashboard/NoticeBoard';\r\nimport NoticeBoard from './dashboard/NoticeBoard';\r\nimport AddNoticeBoardCategory from './pages/settings/noticeboardcategory/AddNoticeBoardCategory';\r\nimport AddNotice from './pages/notice/AddNotice';\r\nimport HolidayCalendarGoogleList from './pages/holiday-calender/HolidayCalenderGoogleList';\r\n//import OfficeSeatPlan from './pages/holiday-calender/OfficeSeatPlan';\r\nimport OfficeSeatPlan from './pages/seat-plan/OfficeSeatPlan';\r\nimport NotFound from './common/utility/NotFound';\r\nimport Unauthorized from './common/utility/UnAuthorized';\r\nimport ResetPassword from './common/login/ResetPassword';\r\nimport UpdatePassword from './common/login/UpdatePassword';\r\nimport Welcome from './dashboard/Welcome';\r\nimport Creativetools from './dashboard/Creativetools';\r\n\r\n\r\n// Define your routes\r\nconst CreativeRoutes = [\r\n  {\r\n    path: '/login',\r\n    element: <Login />,\r\n  },\r\n  // {\r\n  //   path: '*',\r\n  //   element: <NotFound />,\r\n  // },\r\n  {\r\n    path: 'reset-password',\r\n    element: <ResetPassword />,\r\n  },\r\n  {\r\n    path: '/password/reset/:token',\r\n    element: <UpdatePassword />,\r\n  },\r\n  {\r\n    path: 'world-time-share',\r\n    element: <WorldTime />,\r\n  },\r\n  {\r\n    path: 'time-zone-convert-share',\r\n    element: <TimeZoneConvert />,\r\n  },\r\n  {\r\n    path: '/',\r\n    element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><MainLayout /></ProtectedRoute>,\r\n    children: [\r\n      {\r\n        path: '/',\r\n        element: <Dashboard />, // Default to Dashboard\r\n      },\r\n      {\r\n        path: 'unauthorized',\r\n        element: <Unauthorized />,\r\n      },\r\n      {\r\n        path: 'member-onboard',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin']}><MemberOnboard /></ProtectedRoute>,\r\n        \r\n      },\r\n      \r\n      {\r\n        path: 'world-time',\r\n        element: <WorldTime />,\r\n      },\r\n\r\n      {\r\n        path: 'time-zone-convert',\r\n        element: <TimeZoneConvert />,\r\n      },\r\n      {\r\n        path: 'time-zone-convert',\r\n        element: <TimeZoneConvert />,\r\n      },\r\n      {\r\n        path: 'attendance',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><Attendance/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'attendance-formation',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AttendanceFormation/></ProtectedRoute>,\r\n      },\r\n\r\n      {\r\n        path: 'team-members',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><TeamContacts/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'member-index',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin']}><MemberIndex /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'profile',\r\n        element: <Profile />,\r\n      },\r\n      {\r\n        path: 'add-member',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin']}><AddMember/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'teams',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><Teams/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-team',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddTeam/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-role',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><AddRole/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'departments',\r\n        element: <Department />,\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><Department/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-department',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddDepartment/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-billing-status',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddBillingStatus/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-resource-status',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddResourceStatus/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-resource-type',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddResourceType/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-designation',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddDesignation/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-location',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddLocation/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-branch',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddBranch/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-schedule',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddSchedule/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-available-status',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddAvailableStatus/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-contact-type',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddContactType/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-blood',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddBlood/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-member-status',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddMemberStatus/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-onsite-status',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddOnsiteStatus/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'settings',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin']}><Settings /></ProtectedRoute>,\r\n      },\r\n\r\n      // Task Details\r\n      {\r\n        path: 'add-task',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead']}><AddTaskRecord /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'task-records',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead']}><TaskDetails /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'formation',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><Formation /></ProtectedRoute>,\r\n      },\r\n\r\n      {\r\n        path: 'add-reporter',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead']}><AddReporter /></ProtectedRoute>,\r\n      },\r\n\r\n      {\r\n        path: 'reporters',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><Reporter /></ProtectedRoute>,\r\n      },\r\n\r\n      // Routes for All Users\r\n      {\r\n        path: 'add-time',\r\n        element: <AddTimeCard />,\r\n      },\r\n\r\n      {\r\n        path: 'time-cards',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><TimeCard /></ProtectedRoute>,\r\n      },\r\n\r\n      // Abdur Rahman\r\n      {\r\n        path: 'holidaycalenders',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><Holiday /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-holiday',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddHolidayCalender /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'quickaccesshub',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><QuickAccess/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-hub',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddQuickAccessHub /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'training',\r\n        element: <Training />,\r\n      },\r\n      {\r\n        path: 'add-training',\r\n        element: <AddTraining/>,\r\n      },\r\n      {\r\n        path: 'add-training-category',\r\n        element: <AddTrainingCategory />,\r\n      },\r\n\r\n      {\r\n        path: 'add-trainingtopic',\r\n        element: <AddTrainingTopic />,\r\n      },\r\n\r\n      {\r\n        path: 'add-team-shift-plan',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddTeamShiftPlan /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'shift-plan',\r\n        element: <TeamShiftPlan/>,\r\n      },\r\n      \r\n      {\r\n        path: 'schedule-planners',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'shift-lead']}><SchedulePlaners /></ProtectedRoute>,\r\n      },\r\n\r\n      {\r\n        path: 'achieve',\r\n        element: <Todo />\r\n      },\r\n      {\r\n        path: 'achieve/add-todo',\r\n        element: <AddTodo />\r\n      },\r\n      {\r\n        path: 'about-the-app',\r\n        element: <Abouttheapp/>\r\n      },\r\n      {\r\n        path: 'add-about-the-app',\r\n        element: <AddAboutTheApp/>\r\n      },\r\n      {\r\n        path: 'change-log',\r\n        element: <Changelog/>\r\n      },\r\n      {\r\n        path: 'add-change-log',\r\n        element: <AddChangeLog/>\r\n      },\r\n      {\r\n        path: 'app-support',\r\n        element: <Appsupport/>\r\n      },\r\n      {\r\n        path: 'add-app-support',\r\n        element: <AddAppsupport/>\r\n      },\r\n      {\r\n        path: 'give-feedback',\r\n        element: <Givefeedback/>\r\n      },\r\n      {\r\n        path: 'add-feedback',\r\n        element: <AddGiveFeedback/>\r\n      },\r\n      \r\n      {\r\n        path: 'report-problem',\r\n        element: <Reportproblem/>\r\n      },\r\n      {\r\n        path: 'add-report-problem',\r\n        element: <AddReportProblem/>\r\n      },\r\n      {\r\n        path: 'team-snapshot',\r\n        element: <Teamsnapshot/>\r\n      },\r\n      {\r\n        path: 'noticeboard',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead']}><NoticeBoard/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-notice-category',\r\n        element: <AddNoticeBoardCategory/>\r\n      },\r\n      {\r\n        path: 'add-notice',\r\n        element: <AddNotice/>\r\n      },\r\n     \r\n      {\r\n        path: 'events',\r\n        element: <HolidayCalendarGoogleList/>\r\n      },\r\n      {\r\n        path: 'seat-plan',\r\n        element: <OfficeSeatPlan/>\r\n      },\r\n\r\n      {\r\n        path: 'welcome',\r\n        element: <Welcome/>\r\n      },\r\n      {\r\n        path: 'passwordmanager',\r\n        element: <Welcome/>\r\n      },\r\n      {\r\n        path: 'creative-tools',\r\n        element: <Creativetools/>\r\n      }\r\n     \r\n      \r\n\r\n      \r\n    ],\r\n  },\r\n  {\r\n    path: 'users',\r\n    element: <UserList />,\r\n  },\r\n  {\r\n    path: '*',\r\n    element: <Navigate to=\"/login\" />,\r\n  }, \r\n];\r\n\r\n// Create the router\r\nconst router = createBrowserRouter(CreativeRoutes);\r\n\r\nexport default router;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,mBAAmB,CAAEC,QAAQ,KAAQ,kBAAkB,CAChE,MAAO,CAAAC,UAAU,KAAM,wBAAwB,CAE/C,MAAO,CAAAC,KAAK,KAAM,gBAAgB,CAClC,MAAO,CAAAC,SAAS,KAAM,uBAAuB,CAC7C,MAAO,CAAAC,QAAQ,KAAM,oCAAoC,CACzD,MAAO,CAAAC,KAAK,KAAM,4BAA4B,CAC9C,MAAO,CAAAC,WAAW,KAAM,8BAA8B,CACtD,MAAO,CAAAC,SAAS,KAAM,0BAA0B,CAChD,MAAO,CAAAC,UAAU,KAAM,iCAAiC,CACxD,MAAO,CAAAC,OAAO,KAAM,sBAAsB,CAC1C,MAAO,CAAAC,SAAS,KAAM,+BAA+B,CACrD,MAAO,CAAAC,QAAQ,KAAM,sBAAsB,CAC3C,MAAO,CAAAC,OAAO,KAAM,sBAAsB,CAC1C,MAAO,CAAAC,aAAa,KAAM,kCAAkC,CAC5D,MAAO,CAAAC,gBAAgB,KAAM,yCAAyC,CACtE,MAAO,CAAAC,iBAAiB,KAAM,2CAA2C,CACzE,MAAO,CAAAC,eAAe,KAAM,uCAAuC,CACnE,MAAO,CAAAC,cAAc,KAAM,oCAAoC,CAC/D,MAAO,CAAAC,WAAW,KAAM,yBAAyB,CACjD,MAAO,CAAAC,QAAQ,KAAM,wBAAwB,CAC7C,MAAO,CAAAC,kBAAkB,KAAM,6CAA6C,CAC5E,MAAO,CAAAC,cAAc,KAAM,qCAAqC,CAChE,MAAO,CAAAC,eAAe,KAAM,uCAAuC,CACnE,MAAO,CAAAC,eAAe,KAAM,uCAAuC,CACnE,MAAO,CAAAC,aAAa,KAAM,2BAA2B,CACrD,MAAO,CAAAC,WAAW,KAAM,8BAA8B,CACtD,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,YAAY,KAAM,0BAA0B,CAEnD;AACA,MAAO,CAAAC,OAAO,KAAM,qBAAqB,CACzC,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,QAAQ,KAAM,sBAAsB,CAC3C,MAAO,CAAAC,kBAAkB,KAAM,6CAA6C,CAC5E,MAAO,CAAAC,mBAAmB,KAAM,wDAAwD,CACxF,MAAO,CAAAC,gBAAgB,KAAM,kDAAkD,CAC/E,MAAO,CAAAC,iBAAiB,KAAM,0CAA0C,CACxE,MAAO,CAAAC,WAAW,KAAM,8BAA8B,CACtD,MAAO,CAAAC,WAAW,KAAM,sCAAsC,CAC9D,MAAO,CAAAC,SAAS,KAAM,oCAAoC,CAC1D,MAAO,CAAAC,aAAa,KAAM,gDAAgD,CAC1E,MAAO,CAAAC,WAAW,KAAM,+BAA+B,CACvD,MAAO,CAAAC,QAAQ,KAAM,gCAAgC,CACrD,MAAO,CAAAC,gBAAgB,KAAM,0CAA0C,CACvE,MAAO,CAAAC,aAAa,KAAM,2BAA2B,CACrD,MAAO,CAAAC,OAAO,KAAM,qBAAqB,CACzC,MAAO,CAAAC,SAAS,KAAM,8BAA8B,CACpD,MAAO,CAAAC,eAAe,KAAM,oCAAoC,CAChE,MAAO,CAAAC,mBAAmB,KAAM,gEAAgE,CAChG,MAAO,CAAAC,UAAU,KAAM,0CAA0C,CACjE,MAAO,CAAAC,eAAe,KAAM,0CAA0C,CAEtE;AACA,MAAO,CAAAC,IAAI,KAAM,kBAAkB,CACnC,MAAO,CAAAC,OAAO,KAAM,iCAAiC,CACrD,MAAO,CAAAC,WAAW,KAAM,yBAAyB,CACjD,MAAO,CAAAC,cAAc,KAAM,sCAAsC,CACjE,MAAO,CAAAC,YAAY,KAAM,iCAAiC,CAC1D,MAAO,CAAAC,SAAS,KAAM,uBAAuB,CAC7C,MAAO,CAAAC,WAAW,KAAM,wCAAwC,CAChE,MAAO,CAAAC,QAAQ,KAAM,gCAAgC,CACrD,MAAO,CAAAC,UAAU,KAAM,wBAAwB,CAC/C,MAAO,CAAAC,aAAa,KAAM,mCAAmC,CAC7D,MAAO,CAAAC,YAAY,KAAM,0BAA0B,CACnD,MAAO,CAAAC,aAAa,KAAM,2BAA2B,CACrD,MAAO,CAAAC,eAAe,KAAM,uCAAuC,CACnE,MAAO,CAAAC,gBAAgB,KAAM,yCAAyC,CACtE,MAAO,CAAAC,YAAY,KAAM,0BAA0B,CACnD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,WAAW,KAAM,yBAAyB,CACjD,MAAO,CAAAC,sBAAsB,KAAM,6DAA6D,CAChG,MAAO,CAAAC,SAAS,KAAM,0BAA0B,CAChD,MAAO,CAAAC,yBAAyB,KAAM,oDAAoD,CAC1F;AACA,MAAO,CAAAC,cAAc,KAAM,kCAAkC,CAC7D,MAAO,CAAAC,QAAQ,KAAM,2BAA2B,CAChD,MAAO,CAAAC,YAAY,KAAM,+BAA+B,CACxD,MAAO,CAAAC,aAAa,KAAM,8BAA8B,CACxD,MAAO,CAAAC,cAAc,KAAM,+BAA+B,CAC1D,MAAO,CAAAC,OAAO,KAAM,qBAAqB,CACzC,MAAO,CAAAC,aAAa,KAAM,2BAA2B,CAGrD;AAAA,OAAAC,GAAA,IAAAC,IAAA,yBACA,KAAM,CAAAC,cAAc,CAAG,CACrB,CACEC,IAAI,CAAE,QAAQ,CACdC,OAAO,cAAEH,IAAA,CAAC3E,KAAK,GAAE,CACnB,CAAC,CACD;AACA;AACA;AACA;AACA,CACE6E,IAAI,CAAE,gBAAgB,CACtBC,OAAO,cAAEH,IAAA,CAACL,aAAa,GAAE,CAC3B,CAAC,CACD,CACEO,IAAI,CAAE,wBAAwB,CAC9BC,OAAO,cAAEH,IAAA,CAACJ,cAAc,GAAE,CAC5B,CAAC,CACD,CACEM,IAAI,CAAE,kBAAkB,CACxBC,OAAO,cAAEH,IAAA,CAACjC,SAAS,GAAE,CACvB,CAAC,CACD,CACEmC,IAAI,CAAE,yBAAyB,CAC/BC,OAAO,cAAEH,IAAA,CAAChC,eAAe,GAAE,CAC7B,CAAC,CACD,CACEkC,IAAI,CAAE,GAAG,CACTC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,aAAa,CAAE,YAAY,CAAE,aAAa,CAAE,CAAAC,QAAA,cAACL,IAAA,CAAC5E,UAAU,GAAE,CAAC,CAAgB,CAAC,CAC5KiF,QAAQ,CAAE,CACR,CACEH,IAAI,CAAE,GAAG,CACTC,OAAO,cAAEH,IAAA,CAAC1E,SAAS,GAAE,CAAG;AAC1B,CAAC,CACD,CACE4E,IAAI,CAAE,cAAc,CACpBC,OAAO,cAAEH,IAAA,CAACN,YAAY,GAAE,CAC1B,CAAC,CACD,CACEQ,IAAI,CAAE,gBAAgB,CACtBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACrD,aAAa,GAAE,CAAC,CAAgB,CAErG,CAAC,CAED,CACEuD,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAEH,IAAA,CAACjC,SAAS,GAAE,CACvB,CAAC,CAED,CACEmC,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAEH,IAAA,CAAChC,eAAe,GAAE,CAC7B,CAAC,CACD,CACEkC,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAEH,IAAA,CAAChC,eAAe,GAAE,CAC7B,CAAC,CACD,CACEkC,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,aAAa,CAAE,YAAY,CAAE,aAAa,CAAE,CAAAC,QAAA,cAACL,IAAA,CAAC9B,UAAU,GAAC,CAAC,CAAgB,CAC5K,CAAC,CACD,CACEgC,IAAI,CAAE,sBAAsB,CAC5BC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAAC/B,mBAAmB,GAAC,CAAC,CAAgB,CACzI,CAAC,CAED,CACEiC,IAAI,CAAE,cAAc,CACpBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,aAAa,CAAE,YAAY,CAAE,aAAa,CAAE,CAAAC,QAAA,cAACL,IAAA,CAAClD,YAAY,GAAC,CAAC,CAAgB,CAC9K,CAAC,CACD,CACEoD,IAAI,CAAE,cAAc,CACpBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,CAAAC,QAAA,cAACL,IAAA,CAAC3D,WAAW,GAAE,CAAC,CAAgB,CACnG,CAAC,CACD,CACE6D,IAAI,CAAE,SAAS,CACfC,OAAO,cAAEH,IAAA,CAAClC,OAAO,GAAE,CACrB,CAAC,CACD,CACEoC,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACnE,SAAS,GAAC,CAAC,CAAgB,CAChG,CAAC,CACD,CACEqE,IAAI,CAAE,OAAO,CACbC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACxE,KAAK,GAAC,CAAC,CAAgB,CAC3H,CAAC,CACD,CACE0E,IAAI,CAAE,UAAU,CAChBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACpE,OAAO,GAAC,CAAC,CAAgB,CAC7H,CAAC,CACD,CACEsE,IAAI,CAAE,UAAU,CAChBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,aAAa,CAAE,YAAY,CAAE,aAAa,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACjE,OAAO,GAAC,CAAC,CAAgB,CACzK,CAAC,CACD,CACEmE,IAAI,CAAE,aAAa,CACnBC,OAAO,cAAEH,IAAA,CAACrE,UAAU,GAAE,CAAC,CACvBwE,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACrE,UAAU,GAAC,CAAC,CAAgB,CAChI,CAAC,CACD,CACEuE,IAAI,CAAE,gBAAgB,CACtBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAAChE,aAAa,GAAC,CAAC,CAAgB,CACnI,CAAC,CACD,CACEkE,IAAI,CAAE,oBAAoB,CAC1BC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAAC/D,gBAAgB,GAAC,CAAC,CAAgB,CACtI,CAAC,CACD,CACEiE,IAAI,CAAE,qBAAqB,CAC3BC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAAC9D,iBAAiB,GAAC,CAAC,CAAgB,CACvI,CAAC,CACD,CACEgE,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAAC7D,eAAe,GAAC,CAAC,CAAgB,CACrI,CAAC,CACD,CACE+D,IAAI,CAAE,iBAAiB,CACvBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAAC5D,cAAc,GAAC,CAAC,CAAgB,CACpI,CAAC,CACD,CACE8D,IAAI,CAAE,cAAc,CACpBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACvE,WAAW,GAAC,CAAC,CAAgB,CACjI,CAAC,CACD,CACEyE,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACtE,SAAS,GAAC,CAAC,CAAgB,CAC/H,CAAC,CACD,CACEwE,IAAI,CAAE,cAAc,CACpBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACpD,WAAW,GAAC,CAAC,CAAgB,CACjI,CAAC,CACD,CACEsD,IAAI,CAAE,sBAAsB,CAC5BC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACzD,kBAAkB,GAAC,CAAC,CAAgB,CACxI,CAAC,CACD,CACE2D,IAAI,CAAE,kBAAkB,CACxBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACxD,cAAc,GAAC,CAAC,CAAgB,CACpI,CAAC,CACD,CACE0D,IAAI,CAAE,WAAW,CACjBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAAC1D,QAAQ,GAAC,CAAC,CAAgB,CAC9H,CAAC,CACD,CACE4D,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACvD,eAAe,GAAC,CAAC,CAAgB,CACrI,CAAC,CACD,CACEyD,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACtD,eAAe,GAAC,CAAC,CAAgB,CACrI,CAAC,CACD,CACEwD,IAAI,CAAE,UAAU,CAChBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,CAAAC,QAAA,cAACL,IAAA,CAAClE,QAAQ,GAAE,CAAC,CAAgB,CAChG,CAAC,CAED;AACA,CACEoE,IAAI,CAAE,UAAU,CAChBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,aAAa,CAAE,YAAY,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACvC,aAAa,GAAE,CAAC,CAAgB,CACjK,CAAC,CACD,CACEyC,IAAI,CAAE,cAAc,CACpBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,aAAa,CAAE,YAAY,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACzC,WAAW,GAAE,CAAC,CAAgB,CAC/J,CAAC,CACD,CACE2C,IAAI,CAAE,WAAW,CACjBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACxC,SAAS,GAAE,CAAC,CAAgB,CAChI,CAAC,CAED,CACE0C,IAAI,CAAE,cAAc,CACpBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,aAAa,CAAE,YAAY,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACtB,WAAW,GAAE,CAAC,CAAgB,CAC/J,CAAC,CAED,CACEwB,IAAI,CAAE,WAAW,CACjBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,aAAa,CAAE,YAAY,CAAE,aAAa,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACrB,QAAQ,GAAE,CAAC,CAAgB,CAC3K,CAAC,CAED;AACA,CACEuB,IAAI,CAAE,UAAU,CAChBC,OAAO,cAAEH,IAAA,CAACtC,WAAW,GAAE,CACzB,CAAC,CAED,CACEwC,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,aAAa,CAAE,YAAY,CAAE,aAAa,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACrC,QAAQ,GAAE,CAAC,CAAgB,CAC3K,CAAC,CAED;AACA,CACEuC,IAAI,CAAE,kBAAkB,CACxBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,aAAa,CAAE,YAAY,CAAE,aAAa,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACjD,OAAO,GAAE,CAAC,CAAgB,CAC1K,CAAC,CACD,CACEmD,IAAI,CAAE,aAAa,CACnBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAAC9C,kBAAkB,GAAE,CAAC,CAAgB,CACzI,CAAC,CACD,CACEgD,IAAI,CAAE,gBAAgB,CACtBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,aAAa,CAAE,YAAY,CAAE,aAAa,CAAE,CAAAC,QAAA,cAACL,IAAA,CAAChD,WAAW,GAAC,CAAC,CAAgB,CAC7K,CAAC,CACD,CACEkD,IAAI,CAAE,SAAS,CACfC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAAC3C,iBAAiB,GAAE,CAAC,CAAgB,CACxI,CAAC,CACD,CACE6C,IAAI,CAAE,UAAU,CAChBC,OAAO,cAAEH,IAAA,CAAC/C,QAAQ,GAAE,CACtB,CAAC,CACD,CACEiD,IAAI,CAAE,cAAc,CACpBC,OAAO,cAAEH,IAAA,CAAC1C,WAAW,GAAC,CACxB,CAAC,CACD,CACE4C,IAAI,CAAE,uBAAuB,CAC7BC,OAAO,cAAEH,IAAA,CAAC7C,mBAAmB,GAAE,CACjC,CAAC,CAED,CACE+C,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAEH,IAAA,CAAC5C,gBAAgB,GAAE,CAC9B,CAAC,CAED,CACE8C,IAAI,CAAE,qBAAqB,CAC3BC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACpC,gBAAgB,GAAE,CAAC,CAAgB,CACvI,CAAC,CACD,CACEsC,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAEH,IAAA,CAACnC,aAAa,GAAC,CAC1B,CAAC,CAED,CACEqC,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,YAAY,CAAE,CAAAC,QAAA,cAACL,IAAA,CAAC7B,eAAe,GAAE,CAAC,CAAgB,CACpJ,CAAC,CAED,CACE+B,IAAI,CAAE,SAAS,CACfC,OAAO,cAAEH,IAAA,CAAC5B,IAAI,GAAE,CAClB,CAAC,CACD,CACE8B,IAAI,CAAE,kBAAkB,CACxBC,OAAO,cAAEH,IAAA,CAAC3B,OAAO,GAAE,CACrB,CAAC,CACD,CACE6B,IAAI,CAAE,eAAe,CACrBC,OAAO,cAAEH,IAAA,CAAC1B,WAAW,GAAC,CACxB,CAAC,CACD,CACE4B,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAEH,IAAA,CAACzB,cAAc,GAAC,CAC3B,CAAC,CACD,CACE2B,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAEH,IAAA,CAACvB,SAAS,GAAC,CACtB,CAAC,CACD,CACEyB,IAAI,CAAE,gBAAgB,CACtBC,OAAO,cAAEH,IAAA,CAACxB,YAAY,GAAC,CACzB,CAAC,CACD,CACE0B,IAAI,CAAE,aAAa,CACnBC,OAAO,cAAEH,IAAA,CAACpB,UAAU,GAAC,CACvB,CAAC,CACD,CACEsB,IAAI,CAAE,iBAAiB,CACvBC,OAAO,cAAEH,IAAA,CAACnB,aAAa,GAAC,CAC1B,CAAC,CACD,CACEqB,IAAI,CAAE,eAAe,CACrBC,OAAO,cAAEH,IAAA,CAAClB,YAAY,GAAC,CACzB,CAAC,CACD,CACEoB,IAAI,CAAE,cAAc,CACpBC,OAAO,cAAEH,IAAA,CAAChB,eAAe,GAAC,CAC5B,CAAC,CAED,CACEkB,IAAI,CAAE,gBAAgB,CACtBC,OAAO,cAAEH,IAAA,CAACjB,aAAa,GAAC,CAC1B,CAAC,CACD,CACEmB,IAAI,CAAE,oBAAoB,CAC1BC,OAAO,cAAEH,IAAA,CAACf,gBAAgB,GAAC,CAC7B,CAAC,CACD,CACEiB,IAAI,CAAE,eAAe,CACrBC,OAAO,cAAEH,IAAA,CAACd,YAAY,GAAC,CACzB,CAAC,CACD,CACEgB,IAAI,CAAE,aAAa,CACnBC,OAAO,cAAEH,IAAA,CAACnD,cAAc,EAACuD,aAAa,CAAE,CAAC,OAAO,CAAE,aAAa,CAAE,KAAK,CAAE,SAAS,CAAE,WAAW,CAAE,aAAa,CAAE,YAAY,CAAE,CAAAC,QAAA,cAACL,IAAA,CAACZ,WAAW,GAAC,CAAC,CAAgB,CAC9J,CAAC,CACD,CACEc,IAAI,CAAE,qBAAqB,CAC3BC,OAAO,cAAEH,IAAA,CAACX,sBAAsB,GAAC,CACnC,CAAC,CACD,CACEa,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAEH,IAAA,CAACV,SAAS,GAAC,CACtB,CAAC,CAED,CACEY,IAAI,CAAE,QAAQ,CACdC,OAAO,cAAEH,IAAA,CAACT,yBAAyB,GAAC,CACtC,CAAC,CACD,CACEW,IAAI,CAAE,WAAW,CACjBC,OAAO,cAAEH,IAAA,CAACR,cAAc,GAAC,CAC3B,CAAC,CAED,CACEU,IAAI,CAAE,SAAS,CACfC,OAAO,cAAEH,IAAA,CAACH,OAAO,GAAC,CACpB,CAAC,CACD,CACEK,IAAI,CAAE,iBAAiB,CACvBC,OAAO,cAAEH,IAAA,CAACH,OAAO,GAAC,CACpB,CAAC,CACD,CACEK,IAAI,CAAE,gBAAgB,CACtBC,OAAO,cAAEH,IAAA,CAACF,aAAa,GAAC,CAC1B,CAAC,CAML,CAAC,CACD,CACEI,IAAI,CAAE,OAAO,CACbC,OAAO,cAAEH,IAAA,CAACzE,QAAQ,GAAE,CACtB,CAAC,CACD,CACE2E,IAAI,CAAE,GAAG,CACTC,OAAO,cAAEH,IAAA,CAAC7E,QAAQ,EAACmF,EAAE,CAAC,QAAQ,CAAE,CAClC,CAAC,CACF,CAED;AACA,KAAM,CAAAC,MAAM,CAAGrF,mBAAmB,CAAC+E,cAAc,CAAC,CAElD,cAAe,CAAAM,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}