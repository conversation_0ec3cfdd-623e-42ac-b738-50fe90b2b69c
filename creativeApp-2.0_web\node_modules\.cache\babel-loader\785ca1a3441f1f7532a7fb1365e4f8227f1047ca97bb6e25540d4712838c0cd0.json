{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\components\\\\password-manager\\\\PasswordCardsTable.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport AddPasswordCardForm from \"./AddPasswordCardForm\";\nimport { confirmationAlert } from \"../../common/coreui\";\nimport FetchLoggedInRole from \"../../common/fetchData/FetchLoggedInRole\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PasswordCardsTable = ({\n  generatedPassword,\n  passwordStrength\n}) => {\n  _s();\n  // Get current user data\n  const {\n    userData\n  } = FetchLoggedInRole();\n  const currentUserId = userData === null || userData === void 0 ? void 0 : userData.id;\n\n  // Sample data - in real app this would come from API/state management\n  const [passwordCards, setPasswordCards] = useState([{\n    id: 1,\n    title: \"Gmail Account\",\n    platform: \"Gmail\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn\",\n    team: \"Team Name\",\n    department: \"IT\",\n    strength: \"Weak Password\",\n    strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n    authorId: currentUserId // Add author ID\n  }, {\n    id: 2,\n    title: \"Slack Workspace\",\n    platform: \"Slack\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"StrongPass123!@#\",\n    team: \"Team Name\",\n    department: \"IT\",\n    strength: \"Strong Password\",\n    strengthColor: \"bg-green-100 text-green-600 border-green-300\",\n    authorId: currentUserId\n  }, {\n    id: 3,\n    title: \"GitHub Repository\",\n    platform: \"GitHub\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"ModeratePass456\",\n    team: \"Team Name\",\n    department: \"Development\",\n    strength: \"Moderate Password\",\n    strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\",\n    authorId: 999 // Different author ID to test permissions\n  }, {\n    id: 4,\n    title: \"AWS Console\",\n    platform: \"AWS\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"WeakPass\",\n    team: \"Team Name\",\n    department: \"DevOps\",\n    strength: \"Weak Password\",\n    strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n    authorId: currentUserId\n  }, {\n    id: 5,\n    title: \"Jira Project\",\n    platform: \"Jira\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"AnotherStrongPass789!\",\n    team: \"Team Name\",\n    department: \"Project Management\",\n    strength: \"Strong Password\",\n    strengthColor: \"bg-green-100 text-green-600 border-green-300\",\n    authorId: 998 // Different author ID to test permissions\n  }, {\n    id: 6,\n    title: \"Office 365\",\n    platform: \"Microsoft 365\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"ModerateSecure123\",\n    team: \"Team Name\",\n    department: \"HR\",\n    strength: \"Moderate Password\",\n    strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\",\n    authorId: currentUserId\n  }, {\n    id: 7,\n    title: \"Database Admin\",\n    platform: \"MySQL\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"VeryWeakPass\",\n    team: \"Team Name\",\n    department: \"Database\",\n    strength: \"Weak Password\",\n    strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n    authorId: currentUserId\n  }]);\n  const [visiblePasswords, setVisiblePasswords] = useState({});\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [showNewTable, setShowNewTable] = useState(false);\n  const [showTeamTable] = useState(true);\n  const [newPasswordCards, setNewPasswordCards] = useState([]);\n  const [editingRowId, setEditingRowId] = useState(null);\n  const [shareableCards, setShareableCards] = useState([]);\n  const [sortConfig, setSortConfig] = useState({\n    key: null,\n    direction: \"asc\"\n  });\n  const togglePasswordVisibility = id => {\n    setVisiblePasswords(prev => ({\n      ...prev,\n      [id]: !prev[id]\n    }));\n  };\n  const copyToClipboard = text => {\n    navigator.clipboard.writeText(text);\n    // You could add a toast notification here\n  };\n\n  // Handle individual row editing\n  const handleEdit = id => {\n    setEditingRowId(editingRowId === id ? null : id);\n  };\n\n  // Handle creating new table\n  const handleCreateNewTable = () => {\n    setShowNewTable(true);\n  };\n\n  // Handle delete entire team table - only author can delete\n  const handleDeleteTeamTable = () => {\n    // Check if current user is author of any cards\n    const isAuthor = passwordCards.some(card => card.authorId === currentUserId);\n    if (!isAuthor) {\n      alert(\"Only the author can delete the entire table.\");\n      return;\n    }\n\n    // Only proceed if there are selected cards or if user is author\n    if (shareableCards.length === 0) {\n      alert(\"Please select at least one row to delete the table.\");\n      return;\n    }\n    confirmationAlert({\n      onConfirm: () => {\n        setPasswordCards([]);\n      }\n    });\n  };\n\n  // Handle select all checkboxes password and\n\n  const toggleSelectAll = () => {\n    if (shareableCards.length === passwordCards.length) {\n      setShareableCards([]);\n    } else {\n      setShareableCards(passwordCards.map(card => card.id));\n    }\n  };\n\n  // Handle sorting\n  const handleSort = key => {\n    let direction = \"asc\";\n    if (sortConfig.key === key && sortConfig.direction === \"asc\") {\n      direction = \"desc\";\n    }\n    setSortConfig({\n      key,\n      direction\n    });\n  };\n\n  // Sort cards based on current sort config\n  const getSortedCards = cards => {\n    if (!sortConfig.key) return cards;\n    return [...cards].sort((a, b) => {\n      const aValue = a[sortConfig.key].toLowerCase();\n      const bValue = b[sortConfig.key].toLowerCase();\n      if (sortConfig.direction === \"asc\") {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n  };\n\n  // Handle shareable toggle\n  const toggleShareable = id => {\n    setShareableCards(prev => prev.includes(id) ? prev.filter(cardId => cardId !== id) : [...prev, id]);\n  };\n\n  // Handle form submission for new password cards\n  const handleAddPasswordCard = cardData => {\n    const newCard = {\n      ...cardData,\n      id: Date.now(),\n      password: \"••••••••••••\",\n      actualPassword: cardData.password,\n      authorId: currentUserId // Add current user as author\n    };\n    setNewPasswordCards(prev => [...prev, newCard]);\n    setShowAddForm(false);\n  };\n  const handleDelete = id => {\n    confirmationAlert({\n      onConfirm: () => {\n        setPasswordCards(prev => prev.filter(card => card.id !== id));\n      }\n    });\n  };\n\n  // Handle share functionality for department - only author can share\n  const handleShare = () => {\n    var _userData$departments, _userData$departments2;\n    // Check if current user is author of any cards\n    const isAuthor = passwordCards.some(card => card.authorId === currentUserId);\n    if (!isAuthor) {\n      alert(\"Only the author can share the table.\");\n      return;\n    }\n\n    // Get current user's department or team\n    const currentUserDepartment = (userData === null || userData === void 0 ? void 0 : (_userData$departments = userData.departments) === null || _userData$departments === void 0 ? void 0 : (_userData$departments2 = _userData$departments[0]) === null || _userData$departments2 === void 0 ? void 0 : _userData$departments2.name) || \"IT\";\n\n    // Filter cards that belong to the same department and are authored by current user\n    const departmentCards = passwordCards.filter(card => card.department === currentUserDepartment && card.authorId === currentUserId);\n    if (departmentCards.length === 0) {\n      alert(\"No password cards available to share in your department.\");\n      return;\n    }\n\n    // Create shareable data\n    const shareData = {\n      title: `${currentUserDepartment} Department Password Cards`,\n      cards: departmentCards,\n      sharedBy: `${userData === null || userData === void 0 ? void 0 : userData.fname} ${userData === null || userData === void 0 ? void 0 : userData.lname}` || \"Current User\",\n      sharedAt: new Date().toISOString()\n    };\n\n    // For now, copy to clipboard (you can implement actual sharing logic)\n    navigator.clipboard.writeText(JSON.stringify(shareData, null, 2)).then(() => {\n      alert(`${departmentCards.length} password cards from ${currentUserDepartment} department copied to clipboard!`);\n    }).catch(() => {\n      alert(\"Failed to copy to clipboard. Please try again.\");\n    });\n  };\n\n  // Placeholder avatar images\n  const avatarImages = [\"https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=A\", \"https://via.placeholder.com/32x32/10B981/FFFFFF?text=B\", \"https://via.placeholder.com/32x32/F59E0B/FFFFFF?text=C\", \"https://via.placeholder.com/32x32/EF4444/FFFFFF?text=D\"];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white dark:bg-gray-900\",\n    children: [showTeamTable && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-left text-2xl font-bold text-gray-900 dark:text-white\",\n            children: \"Teams Password Card\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleShare,\n            className: \"flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-rounded\",\n              children: \"share\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleDeleteTeamTable(),\n            className: \"flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg\",\n            title: \"Delete entire table\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-outlined text-sm\",\n              children: \"delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex -space-x-2\",\n            children: avatarImages.map((avatar, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n              src: avatar,\n              alt: `User ${index + 1}`,\n              className: \"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setEditingRowId(null);\n              setShowAddForm(!showAddForm);\n            },\n            className: `w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${showAddForm ? \"bg-primary text-white\" : \"bg-transparent text-primary border-2 border-primary\"}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-rounded mr-2\",\n              children: \"add\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this), \"Add Password\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this), showAddForm && /*#__PURE__*/_jsxDEV(AddPasswordCardForm, {\n        onSubmit: handleAddPasswordCard,\n        onCancel: () => {\n          setShowAddForm(false);\n          setEditingRowId(null);\n        },\n        generatedPassword: generatedPassword,\n        passwordStrength: passwordStrength\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"w-12 px-4 py-3 text-left\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"checkbox-all\",\n                  type: \"checkbox\",\n                  checked: shareableCards.length === passwordCards.length && passwordCards.length > 0,\n                  onChange: toggleSelectAll,\n                  className: \"w-5 h-5 appearance-none rounded border-2 border-gray-300 bg-white checked:bg-primary checked:border-transparent\",\n                  className: \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  onClick: () => handleSort(\"title\"),\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Title\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"title\" && sortConfig.direction === \"asc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25B2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"title\" && sortConfig.direction === \"desc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25BC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  onClick: () => handleSort(\"username\"),\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"User Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"username\" && sortConfig.direction === \"asc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25B2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 405,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"username\" && sortConfig.direction === \"desc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25BC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 415,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Department\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"divide-y divide-gray-200\",\n            children: getSortedCards(passwordCards).map(card => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: `shareable-${card.id}`,\n                  type: \"checkbox\",\n                  checked: shareableCards.includes(card.id),\n                  onChange: () => toggleShareable(card.id),\n                  className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\",\n                    children: \"TG\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 25\n                  }, this), editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    defaultValue: card.title,\n                    className: \"font-medium text-gray-900 border rounded px-2 py-1 w-full\",\n                    onBlur: e => {\n                      setPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                        ...c,\n                        title: e.target.value\n                      } : c));\n                    },\n                    onKeyDown: e => {\n                      if (e.key === \"Enter\") {\n                        e.target.blur();\n                      }\n                    },\n                    autoFocus: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: card.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    defaultValue: card.username,\n                    className: \"text-gray-900 border rounded px-2 py-1 w-full mr-2\",\n                    onBlur: e => {\n                      setPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                        ...c,\n                        username: e.target.value\n                      } : c));\n                    },\n                    onKeyDown: e => {\n                      if (e.key === \"Enter\") {\n                        e.target.blur();\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900\",\n                    children: card.username\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => copyToClipboard(card.username),\n                    className: \"ml-2 text-gray-400 hover:text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"content_copy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    defaultValue: card.actualPassword,\n                    className: \"text-gray-900 border rounded px-2 py-1 w-full mr-2\",\n                    onBlur: e => {\n                      setPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                        ...c,\n                        actualPassword: e.target.value\n                      } : c));\n                    },\n                    onKeyDown: e => {\n                      if (e.key === \"Enter\") {\n                        e.target.blur();\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 528,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900 mr-2\",\n                    children: visiblePasswords[card.id] ? card.actualPassword : card.password\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 548,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => togglePasswordVisibility(card.id),\n                    className: \"text-gray-400 hover:text-gray-600 mr-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: visiblePasswords[card.id] ? \"visibility_off\" : \"visibility\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 558,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => copyToClipboard(card.actualPassword),\n                    className: \"text-gray-400 hover:text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"content_copy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 568,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  defaultValue: card.team,\n                  className: \"text-gray-900 border rounded px-2 py-1 w-full\",\n                  onBlur: e => {\n                    setPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                      ...c,\n                      team: e.target.value\n                    } : c));\n                  },\n                  onKeyDown: e => {\n                    if (e.key === \"Enter\") {\n                      e.target.blur();\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-900\",\n                  children: card.team\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  defaultValue: card.department,\n                  className: \"text-gray-900 border rounded px-2 py-1 w-full\",\n                  onBlur: e => {\n                    setPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                      ...c,\n                      department: e.target.value\n                    } : c));\n                  },\n                  onKeyDown: e => {\n                    if (e.key === \"Enter\") {\n                      e.target.blur();\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-900\",\n                  children: card.department\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`,\n                  children: card.strength\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(card.id),\n                    className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n                    title: \"Edit\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-outlined text-lg\",\n                      children: \"stylus_note\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 638,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 633,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(card.id),\n                    className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n                    title: \"Delete\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-outlined text-sm\",\n                      children: \"delete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 645,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 21\n              }, this)]\n            }, card.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), showNewTable && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-left text-2xl font-bold text-gray-900 dark:text-white\",\n            children: \"Teams Password Card\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleShare,\n            className: \"flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-rounded\",\n              children: \"share\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleDeleteTeamTable(),\n            className: \"flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg\",\n            title: \"Delete entire table\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-outlined text-sm\",\n              children: \"delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex -space-x-2\",\n            children: avatarImages.map((avatar, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n              src: avatar,\n              alt: `User ${index + 1}`,\n              className: \"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setEditingRowId(null);\n              setShowAddForm(!showAddForm);\n            },\n            className: `w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${showAddForm ? \"bg-primary text-white\" : \"bg-transparent text-primary border-2 border-primary\"}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-rounded mr-2\",\n              children: \"add\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 17\n            }, this), \"Add Password\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 699,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 11\n      }, this), showAddForm && /*#__PURE__*/_jsxDEV(AddPasswordCardForm, {\n        onSubmit: handleAddPasswordCard,\n        onCancel: () => {\n          setShowAddForm(false);\n          setEditingRowId(null);\n        },\n        generatedPassword: generatedPassword,\n        passwordStrength: passwordStrength\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 720,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"w-12 px-4 py-3 text-left\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"checkbox-all-new\",\n                  type: \"checkbox\",\n                  checked: shareableCards.length === newPasswordCards.length && newPasswordCards.length > 0,\n                  onChange: () => {\n                    if (shareableCards.length === newPasswordCards.length) {\n                      setShareableCards([]);\n                    } else {\n                      setShareableCards(newPasswordCards.map(card => card.id));\n                    }\n                  },\n                  className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 736,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  onClick: () => handleSort(\"title\"),\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Title\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 758,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"title\" && sortConfig.direction === \"asc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25B2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 760,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"title\" && sortConfig.direction === \"desc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25BC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 770,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 759,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 754,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  onClick: () => handleSort(\"username\"),\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"User Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 788,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"username\" && sortConfig.direction === \"asc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25B2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 790,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"username\" && sortConfig.direction === \"desc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25BC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 800,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 789,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 784,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 783,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Department\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 733,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"divide-y divide-gray-200\",\n            children: newPasswordCards.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"8\",\n                className: \"px-6 py-8 text-center text-gray-500\",\n                children: \"No password cards added yet. Click \\\"Add Password\\\" to add your first card.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 833,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 832,\n              columnNumber: 19\n            }, this) : getSortedCards(newPasswordCards).map(card => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: shareableCards.includes(card.id),\n                  onChange: () => toggleShareable(card.id),\n                  className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 845,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\",\n                    children: \"TG\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 854,\n                    columnNumber: 27\n                  }, this), editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    defaultValue: card.title,\n                    className: \"font-medium text-gray-900 border rounded px-2 py-1 w-full\",\n                    onBlur: e => {\n                      setNewPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                        ...c,\n                        title: e.target.value\n                      } : c));\n                    },\n                    onKeyDown: e => {\n                      if (e.key === \"Enter\") {\n                        e.target.blur();\n                      }\n                    },\n                    autoFocus: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 858,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: card.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 879,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 853,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    defaultValue: card.username,\n                    className: \"text-gray-900 border rounded px-2 py-1 w-full mr-2\",\n                    onBlur: e => {\n                      setNewPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                        ...c,\n                        username: e.target.value\n                      } : c));\n                    },\n                    onKeyDown: e => {\n                      if (e.key === \"Enter\") {\n                        e.target.blur();\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 888,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900\",\n                    children: card.username\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 908,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => copyToClipboard(card.username),\n                    className: \"ml-2 text-gray-400 hover:text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"content_copy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 914,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 910,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 886,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 885,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    defaultValue: card.actualPassword,\n                    className: \"text-gray-900 border rounded px-2 py-1 w-full mr-2\",\n                    onBlur: e => {\n                      setNewPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                        ...c,\n                        actualPassword: e.target.value\n                      } : c));\n                    },\n                    onKeyDown: e => {\n                      if (e.key === \"Enter\") {\n                        e.target.blur();\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 923,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900 mr-2\",\n                    children: visiblePasswords[card.id] ? card.actualPassword : card.password\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 943,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => togglePasswordVisibility(card.id),\n                    className: \"text-gray-400 hover:text-gray-600 mr-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: visiblePasswords[card.id] ? \"visibility_off\" : \"visibility\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 953,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 949,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => copyToClipboard(card.actualPassword),\n                    className: \"text-gray-400 hover:text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"content_copy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 963,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 959,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 921,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 920,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  defaultValue: card.team,\n                  className: \"text-gray-900 border rounded px-2 py-1 w-full\",\n                  onBlur: e => {\n                    setNewPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                      ...c,\n                      team: e.target.value\n                    } : c));\n                  },\n                  onKeyDown: e => {\n                    if (e.key === \"Enter\") {\n                      e.target.blur();\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 971,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-900\",\n                  children: card.team\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 991,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  defaultValue: card.department,\n                  className: \"text-gray-900 border rounded px-2 py-1 w-full\",\n                  onBlur: e => {\n                    setNewPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                      ...c,\n                      department: e.target.value\n                    } : c));\n                  },\n                  onKeyDown: e => {\n                    if (e.key === \"Enter\") {\n                      e.target.blur();\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 996,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-900\",\n                  children: card.department\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1016,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 994,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`,\n                  children: card.strength\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1020,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1019,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(card.id),\n                    className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n                    title: \"Edit\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-outlined text-lg\",\n                      children: \"stylus_note\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1033,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1028,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      confirmationAlert({\n                        onConfirm: () => {\n                          setNewPasswordCards(prev => prev.filter(c => c.id !== card.id));\n                        }\n                      });\n                    },\n                    className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n                    title: \"Delete\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-outlined text-sm\",\n                      children: \"delete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1048,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1035,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1027,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1026,\n                columnNumber: 23\n              }, this)]\n            }, card.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 830,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 732,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 731,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 659,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center mt-6\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleCreateNewTable,\n        className: \"flex items-center justify-center px-6 py-2 text-sm font-medium text-white rounded-lg bg-primary hover:bg-primary/90 focus:ring-4 focus:ring-primary/30 transition-colors duration-200 focus:outline-none\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-rounded mr-2\",\n          children: \"add\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1067,\n          columnNumber: 11\n        }, this), \"Add New Password Card\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1063,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1062,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 275,\n    columnNumber: 5\n  }, this);\n};\n_s(PasswordCardsTable, \"AKjp91L6UWX2rlXODOweU/TNwvM=\");\n_c = PasswordCardsTable;\nexport default PasswordCardsTable;\nvar _c;\n$RefreshReg$(_c, \"PasswordCardsTable\");", "map": {"version": 3, "names": ["React", "useState", "AddPasswordCardForm", "<PERSON><PERSON><PERSON><PERSON>", "FetchLoggedInRole", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PasswordCardsTable", "generatedPassword", "passwordStrength", "_s", "userData", "currentUserId", "id", "passwordCards", "setPasswordCards", "title", "platform", "username", "password", "actualPassword", "team", "department", "strength", "strengthColor", "authorId", "visiblePasswords", "setVisiblePasswords", "showAddForm", "setShowAddForm", "showNewTable", "setShowNewTable", "showTeamTable", "newPasswordCards", "setNewPasswordCards", "editingRowId", "setEditingRowId", "shareableCards", "setShareableCards", "sortConfig", "setSortConfig", "key", "direction", "togglePasswordVisibility", "prev", "copyToClipboard", "text", "navigator", "clipboard", "writeText", "handleEdit", "handleCreateNewTable", "handleDeleteTeamTable", "is<PERSON><PERSON><PERSON>", "some", "card", "alert", "length", "onConfirm", "toggleSelectAll", "map", "handleSort", "getSortedCards", "cards", "sort", "a", "b", "aValue", "toLowerCase", "bValue", "toggleShareable", "includes", "filter", "cardId", "handleAddPasswordCard", "cardData", "newCard", "Date", "now", "handleDelete", "handleShare", "_userData$departments", "_userData$departments2", "currentUserDepartment", "departments", "name", "departmentCards", "shareData", "sharedBy", "fname", "lname", "sharedAt", "toISOString", "JSON", "stringify", "then", "catch", "avatarImages", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "avatar", "index", "src", "alt", "onSubmit", "onCancel", "type", "checked", "onChange", "defaultValue", "onBlur", "e", "c", "target", "value", "onKeyDown", "blur", "autoFocus", "colSpan", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/PasswordCardsTable.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport AddPasswordCardForm from \"./AddPasswordCardForm\";\nimport { confirmationAlert } from \"../../common/coreui\";\nimport FetchLoggedInRole from \"../../common/fetchData/FetchLoggedInRole\";\n\nconst PasswordCardsTable = ({ generatedPassword, passwordStrength }) => {\n  // Get current user data\n  const { userData } = FetchLoggedInRole();\n  const currentUserId = userData?.id;\n\n  // Sample data - in real app this would come from API/state management\n  const [passwordCards, setPasswordCards] = useState([\n    {\n      id: 1,\n      title: \"Gmail Account\",\n      platform: \"Gmail\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn\",\n      team: \"Team Name\",\n      department: \"IT\",\n      strength: \"Weak Password\",\n      strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n      authorId: currentUserId, // Add author ID\n    },\n    {\n      id: 2,\n      title: \"Slack Workspace\",\n      platform: \"Slack\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"StrongPass123!@#\",\n      team: \"Team Name\",\n      department: \"IT\",\n      strength: \"Strong Password\",\n      strengthColor: \"bg-green-100 text-green-600 border-green-300\",\n      authorId: currentUserId,\n    },\n    {\n      id: 3,\n      title: \"GitHub Repository\",\n      platform: \"GitHub\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"ModeratePass456\",\n      team: \"Team Name\",\n      department: \"Development\",\n      strength: \"Moderate Password\",\n      strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\",\n      authorId: 999, // Different author ID to test permissions\n    },\n    {\n      id: 4,\n      title: \"AWS Console\",\n      platform: \"AWS\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"WeakPass\",\n      team: \"Team Name\",\n      department: \"DevOps\",\n      strength: \"Weak Password\",\n      strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n      authorId: currentUserId,\n    },\n    {\n      id: 5,\n      title: \"Jira Project\",\n      platform: \"Jira\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"AnotherStrongPass789!\",\n      team: \"Team Name\",\n      department: \"Project Management\",\n      strength: \"Strong Password\",\n      strengthColor: \"bg-green-100 text-green-600 border-green-300\",\n      authorId: 998, // Different author ID to test permissions\n    },\n    {\n      id: 6,\n      title: \"Office 365\",\n      platform: \"Microsoft 365\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"ModerateSecure123\",\n      team: \"Team Name\",\n      department: \"HR\",\n      strength: \"Moderate Password\",\n      strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\",\n      authorId: currentUserId,\n    },\n    {\n      id: 7,\n      title: \"Database Admin\",\n      platform: \"MySQL\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"VeryWeakPass\",\n      team: \"Team Name\",\n      department: \"Database\",\n      strength: \"Weak Password\",\n      strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n      authorId: currentUserId,\n    },\n  ]);\n\n  const [visiblePasswords, setVisiblePasswords] = useState({});\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [showNewTable, setShowNewTable] = useState(false);\n  const [showTeamTable] = useState(true);\n  const [newPasswordCards, setNewPasswordCards] = useState([]);\n  const [editingRowId, setEditingRowId] = useState(null);\n  const [shareableCards, setShareableCards] = useState([]);\n  const [sortConfig, setSortConfig] = useState({ key: null, direction: \"asc\" });\n  const togglePasswordVisibility = (id) => {\n    setVisiblePasswords((prev) => ({\n      ...prev,\n      [id]: !prev[id],\n    }));\n  };\n\n  const copyToClipboard = (text) => {\n    navigator.clipboard.writeText(text);\n    // You could add a toast notification here\n  };\n\n  // Handle individual row editing\n  const handleEdit = (id) => {\n    setEditingRowId(editingRowId === id ? null : id);\n  };\n\n  // Handle creating new table\n  const handleCreateNewTable = () => {\n    setShowNewTable(true);\n  };\n\n  // Handle delete entire team table - only author can delete\n  const handleDeleteTeamTable = () => {\n    // Check if current user is author of any cards\n    const isAuthor = passwordCards.some(card => card.authorId === currentUserId);\n\n    if (!isAuthor) {\n      alert(\"Only the author can delete the entire table.\");\n      return;\n    }\n\n    // Only proceed if there are selected cards or if user is author\n    if (shareableCards.length === 0) {\n      alert(\"Please select at least one row to delete the table.\");\n      return;\n    }\n\n    confirmationAlert({\n      onConfirm: () => {\n        setPasswordCards([]);\n      },\n    });\n  };\n\n  // Handle select all checkboxes password and\n  \n  const toggleSelectAll = () => {\n    if (shareableCards.length === passwordCards.length) {\n      setShareableCards([]);\n    } else {\n      setShareableCards(passwordCards.map((card) => card.id));\n    }\n  };\n\n  // Handle sorting\n  const handleSort = (key) => {\n    let direction = \"asc\";\n    if (sortConfig.key === key && sortConfig.direction === \"asc\") {\n      direction = \"desc\";\n    }\n    setSortConfig({ key, direction });\n  };\n\n  // Sort cards based on current sort config\n  const getSortedCards = (cards) => {\n    if (!sortConfig.key) return cards;\n\n    return [...cards].sort((a, b) => {\n      const aValue = a[sortConfig.key].toLowerCase();\n      const bValue = b[sortConfig.key].toLowerCase();\n\n      if (sortConfig.direction === \"asc\") {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n  };\n\n  // Handle shareable toggle\n  const toggleShareable = (id) => {\n    setShareableCards((prev) =>\n      prev.includes(id) ? prev.filter((cardId) => cardId !== id) : [...prev, id]\n    );\n  };\n\n  // Handle form submission for new password cards\n  const handleAddPasswordCard = (cardData) => {\n    const newCard = {\n      ...cardData,\n      id: Date.now(),\n      password: \"••••••••••••\",\n      actualPassword: cardData.password,\n      authorId: currentUserId, // Add current user as author\n    };\n    setNewPasswordCards((prev) => [...prev, newCard]);\n    setShowAddForm(false);\n  };\n\n  const handleDelete = (id) => {\n    confirmationAlert({\n      onConfirm: () => {\n        setPasswordCards((prev) => prev.filter((card) => card.id !== id));\n      },\n    });\n  };\n\n  // Handle share functionality for department - only author can share\n  const handleShare = () => {\n    // Check if current user is author of any cards\n    const isAuthor = passwordCards.some(card => card.authorId === currentUserId);\n\n    if (!isAuthor) {\n      alert(\"Only the author can share the table.\");\n      return;\n    }\n\n    // Get current user's department or team\n    const currentUserDepartment = userData?.departments?.[0]?.name || \"IT\";\n\n    // Filter cards that belong to the same department and are authored by current user\n    const departmentCards = passwordCards.filter(\n      (card) => card.department === currentUserDepartment && card.authorId === currentUserId\n    );\n\n    if (departmentCards.length === 0) {\n      alert(\"No password cards available to share in your department.\");\n      return;\n    }\n\n    // Create shareable data\n    const shareData = {\n      title: `${currentUserDepartment} Department Password Cards`,\n      cards: departmentCards,\n      sharedBy: `${userData?.fname} ${userData?.lname}` || \"Current User\",\n      sharedAt: new Date().toISOString(),\n    };\n\n    // For now, copy to clipboard (you can implement actual sharing logic)\n    navigator.clipboard\n      .writeText(JSON.stringify(shareData, null, 2))\n      .then(() => {\n        alert(\n          `${departmentCards.length} password cards from ${currentUserDepartment} department copied to clipboard!`\n        );\n      })\n      .catch(() => {\n        alert(\"Failed to copy to clipboard. Please try again.\");\n      });\n  };\n\n  // Placeholder avatar images\n  const avatarImages = [\n    \"https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=A\",\n    \"https://via.placeholder.com/32x32/10B981/FFFFFF?text=B\",\n    \"https://via.placeholder.com/32x32/F59E0B/FFFFFF?text=C\",\n    \"https://via.placeholder.com/32x32/EF4444/FFFFFF?text=D\",\n  ];\n\n  return (\n    <div className=\"bg-white dark:bg-gray-900\">\n      {showTeamTable && (\n        <>\n          {/* Header */}\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\">\n            <div className=\"flex items-center space-x-4\">\n              <h2 className=\"text-left text-2xl font-bold text-gray-900 dark:text-white\">\n                Teams Password Card\n              </h2>\n\n              {/* Share Icon */}\n              <button\n                onClick={handleShare}\n                className=\"flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\"\n              >\n                <span className=\"material-symbols-rounded\">share</span>\n              </button>\n\n              {/* Delete Team Table Icon */}\n              <button\n                onClick={() => handleDeleteTeamTable()}\n                className=\"flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg\"\n                title=\"Delete entire table\"\n              >\n                {/* <span className=\"material-symbols-rounded\">delete</span> */}\n                <span className=\"material-symbols-outlined text-sm\">\n                  delete\n                </span>\n              </button>\n\n              {/* User Avatars */}\n              <div className=\"flex -space-x-2\">\n                {avatarImages.map((avatar, index) => (\n                  <img\n                    key={index}\n                    src={avatar}\n                    alt={`User ${index + 1}`}\n                    className=\"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"\n                  />\n                ))}\n              </div>\n            </div>\n\n            <div className=\"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0\">\n              {/* Add Password Card Button */}\n              <button\n                onClick={() => {\n                  setEditingRowId(null);\n                  setShowAddForm(!showAddForm);\n                }}\n                className={`w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${\n                  showAddForm\n                    ? \"bg-primary text-white\"\n                    : \"bg-transparent text-primary border-2 border-primary\"\n                }`}\n              >\n                <span className=\"material-symbols-rounded mr-2\">add</span>\n                Add Password\n              </button>\n            </div>\n          </div>\n\n          {/* Add/Edit Password Form - Embedded */}\n          {showAddForm && (\n            <AddPasswordCardForm\n              onSubmit={handleAddPasswordCard}\n              onCancel={() => {\n                setShowAddForm(false);\n                setEditingRowId(null);\n              }}\n              generatedPassword={generatedPassword}\n              passwordStrength={passwordStrength}\n            />\n          )}\n\n          {/* Table */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50 border-b border-gray-200\">\n                <tr>\n                  <th className=\"w-12 px-4 py-3 text-left\">\n                    <input\n                      id=\"checkbox-all\"\n                      type=\"checkbox\"\n                      checked={\n                        shareableCards.length === passwordCards.length &&\n                        passwordCards.length > 0\n                      }\n                      onChange={toggleSelectAll}\n                className=\"w-5 h-5 appearance-none rounded border-2 border-gray-300 bg-white checked:bg-primary checked:border-transparent\"\n                      className=\"\"\n                    />\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"title\")}\n                    >\n                      <span>Title</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"username\")}\n                    >\n                      <span>User Name</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Password\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Team\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Department\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Level\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Action\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"divide-y divide-gray-200\">\n                {getSortedCards(passwordCards).map((card) => (\n                  <tr key={card.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-4 py-4\">\n                      <input\n                        id={`shareable-${card.id}`}\n                        type=\"checkbox\"\n                        checked={shareableCards.includes(card.id)}\n                        onChange={() => toggleShareable(card.id)}\n                        className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                      />\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center\">\n                        <div className=\"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\">\n                          TG\n                        </div>\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.title}\n                            className=\"font-medium text-gray-900 border rounded px-2 py-1 w-full\"\n                            onBlur={(e) => {\n                              setPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, title: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                            autoFocus\n                          />\n                        ) : (\n                          <span className=\"font-medium text-gray-900\">\n                            {card.title}\n                          </span>\n                        )}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center\">\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.username}\n                            className=\"text-gray-900 border rounded px-2 py-1 w-full mr-2\"\n                            onBlur={(e) => {\n                              setPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, username: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                          />\n                        ) : (\n                          <span className=\"text-gray-900\">{card.username}</span>\n                        )}\n                        <button\n                          onClick={() => copyToClipboard(card.username)}\n                          className=\"ml-2 text-gray-400 hover:text-gray-600\"\n                        >\n                          <span className=\"material-symbols-rounded text-sm\">\n                            content_copy\n                          </span>\n                        </button>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center\">\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.actualPassword}\n                            className=\"text-gray-900 border rounded px-2 py-1 w-full mr-2\"\n                            onBlur={(e) => {\n                              setPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, actualPassword: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                          />\n                        ) : (\n                          <span className=\"text-gray-900 mr-2\">\n                            {visiblePasswords[card.id]\n                              ? card.actualPassword\n                              : card.password}\n                          </span>\n                        )}\n                        <button\n                          onClick={() => togglePasswordVisibility(card.id)}\n                          className=\"text-gray-400 hover:text-gray-600 mr-2\"\n                        >\n                          <span className=\"material-symbols-rounded text-sm\">\n                            {visiblePasswords[card.id]\n                              ? \"visibility_off\"\n                              : \"visibility\"}\n                          </span>\n                        </button>\n                        <button\n                          onClick={() => copyToClipboard(card.actualPassword)}\n                          className=\"text-gray-400 hover:text-gray-600\"\n                        >\n                          <span className=\"material-symbols-rounded text-sm\">\n                            content_copy\n                          </span>\n                        </button>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      {editingRowId === card.id ? (\n                        <input\n                          type=\"text\"\n                          defaultValue={card.team}\n                          className=\"text-gray-900 border rounded px-2 py-1 w-full\"\n                          onBlur={(e) => {\n                            setPasswordCards((prev) =>\n                              prev.map((c) =>\n                                c.id === card.id\n                                  ? { ...c, team: e.target.value }\n                                  : c\n                              )\n                            );\n                          }}\n                          onKeyDown={(e) => {\n                            if (e.key === \"Enter\") {\n                              e.target.blur();\n                            }\n                          }}\n                        />\n                      ) : (\n                        <span className=\"text-gray-900\">{card.team}</span>\n                      )}\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      {editingRowId === card.id ? (\n                        <input\n                          type=\"text\"\n                          defaultValue={card.department}\n                          className=\"text-gray-900 border rounded px-2 py-1 w-full\"\n                          onBlur={(e) => {\n                            setPasswordCards((prev) =>\n                              prev.map((c) =>\n                                c.id === card.id\n                                  ? { ...c, department: e.target.value }\n                                  : c\n                              )\n                            );\n                          }}\n                          onKeyDown={(e) => {\n                            if (e.key === \"Enter\") {\n                              e.target.blur();\n                            }\n                          }}\n                        />\n                      ) : (\n                        <span className=\"text-gray-900\">{card.department}</span>\n                      )}\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <span\n                        className={`px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`}\n                      >\n                        {card.strength}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center space-x-1\">\n                        <button\n                          onClick={() => handleEdit(card.id)}\n                          className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\n                          title=\"Edit\"\n                        >\n                          <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\n                        </button>\n                        <button\n                          onClick={() => handleDelete(card.id)}\n                          className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\n                          title=\"Delete\"\n                        >\n                          <span className=\"material-symbols-outlined text-sm\">delete</span>\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </>\n      )}\n\n      {/* New Password Cards Table */}\n      {showNewTable && (\n        <div className=\"mt-8\">\n          {/* Header */}\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\">\n            <div className=\"flex items-center space-x-4\">\n              <h2 className=\"text-left text-2xl font-bold text-gray-900 dark:text-white\">\n                Teams Password Card\n              </h2>\n\n              {/* Share Icon */}\n              <button\n                onClick={handleShare}\n                className=\"flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\"\n              >\n                <span className=\"material-symbols-rounded\">share</span>\n              </button>\n\n              {/* Delete Team Table Icon */}\n              <button\n                onClick={() => handleDeleteTeamTable()}\n                className=\"flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg\"\n                title=\"Delete entire table\"\n              >\n                <span className=\"material-symbols-outlined text-sm\">\n                  delete\n                </span>\n              </button>\n\n              {/* User Avatars */}\n              <div className=\"flex -space-x-2\">\n                {avatarImages.map((avatar, index) => (\n                  <img\n                    key={index}\n                    src={avatar}\n                    alt={`User ${index + 1}`}\n                    className=\"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"\n                  />\n                ))}\n              </div>\n            </div>\n\n            <div className=\"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0\">\n              {/* Add Password Card Button */}\n              <button\n                onClick={() => {\n                  setEditingRowId(null);\n                  setShowAddForm(!showAddForm);\n                }}\n                className={`w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${\n                  showAddForm\n                    ? \"bg-primary text-white\"\n                    : \"bg-transparent text-primary border-2 border-primary\"\n                }`}\n              >\n                <span className=\"material-symbols-rounded mr-2\">add</span>\n                Add Password\n              </button>\n            </div>\n          </div>\n\n          {/* Add/Edit Password Form - Embedded */}\n          {showAddForm && (\n            <AddPasswordCardForm\n              onSubmit={handleAddPasswordCard}\n              onCancel={() => {\n                setShowAddForm(false);\n                setEditingRowId(null);\n              }}\n              generatedPassword={generatedPassword}\n              passwordStrength={passwordStrength}\n            />\n          )}\n\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50 border-b border-gray-200\">\n                <tr>\n                  <th className=\"w-12 px-4 py-3 text-left\">\n                    <input\n                      id=\"checkbox-all-new\"\n                      type=\"checkbox\"\n                      checked={\n                        shareableCards.length === newPasswordCards.length &&\n                        newPasswordCards.length > 0\n                      }\n                      onChange={() => {\n                        if (shareableCards.length === newPasswordCards.length) {\n                          setShareableCards([]);\n                        } else {\n                          setShareableCards(newPasswordCards.map((card) => card.id));\n                        }\n                      }}\n                      className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                    />\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"title\")}\n                    >\n                      <span>Title</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"username\")}\n                    >\n                      <span>User Name</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Password\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Team\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Department\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Level\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Action\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"divide-y divide-gray-200\">\n                {newPasswordCards.length === 0 ? (\n                  <tr>\n                    <td\n                      colSpan=\"8\"\n                      className=\"px-6 py-8 text-center text-gray-500\"\n                    >\n                      No password cards added yet. Click \"Add Password\" to add\n                      your first card.\n                    </td>\n                  </tr>\n                ) : (\n                  getSortedCards(newPasswordCards).map((card) => (\n                    <tr key={card.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-4 py-4\">\n                        <input\n                          type=\"checkbox\"\n                          checked={shareableCards.includes(card.id)}\n                          onChange={() => toggleShareable(card.id)}\n                          className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                        />\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          <div className=\"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\">\n                            TG\n                          </div>\n                          {editingRowId === card.id ? (\n                            <input\n                              type=\"text\"\n                              defaultValue={card.title}\n                              className=\"font-medium text-gray-900 border rounded px-2 py-1 w-full\"\n                              onBlur={(e) => {\n                                setNewPasswordCards((prev) =>\n                                  prev.map((c) =>\n                                    c.id === card.id\n                                      ? { ...c, title: e.target.value }\n                                      : c\n                                  )\n                                );\n                              }}\n                              onKeyDown={(e) => {\n                                if (e.key === \"Enter\") {\n                                  e.target.blur();\n                                }\n                              }}\n                              autoFocus\n                            />\n                          ) : (\n                            <span className=\"font-medium text-gray-900\">\n                              {card.title}\n                            </span>\n                          )}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          {editingRowId === card.id ? (\n                            <input\n                              type=\"text\"\n                              defaultValue={card.username}\n                              className=\"text-gray-900 border rounded px-2 py-1 w-full mr-2\"\n                              onBlur={(e) => {\n                                setNewPasswordCards((prev) =>\n                                  prev.map((c) =>\n                                    c.id === card.id\n                                      ? { ...c, username: e.target.value }\n                                      : c\n                                  )\n                                );\n                              }}\n                              onKeyDown={(e) => {\n                                if (e.key === \"Enter\") {\n                                  e.target.blur();\n                                }\n                              }}\n                            />\n                          ) : (\n                            <span className=\"text-gray-900\">{card.username}</span>\n                          )}\n                          <button\n                            onClick={() => copyToClipboard(card.username)}\n                            className=\"ml-2 text-gray-400 hover:text-gray-600\"\n                          >\n                            <span className=\"material-symbols-rounded text-sm\">\n                              content_copy\n                            </span>\n                          </button>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          {editingRowId === card.id ? (\n                            <input\n                              type=\"text\"\n                              defaultValue={card.actualPassword}\n                              className=\"text-gray-900 border rounded px-2 py-1 w-full mr-2\"\n                              onBlur={(e) => {\n                                setNewPasswordCards((prev) =>\n                                  prev.map((c) =>\n                                    c.id === card.id\n                                      ? { ...c, actualPassword: e.target.value }\n                                      : c\n                                  )\n                                );\n                              }}\n                              onKeyDown={(e) => {\n                                if (e.key === \"Enter\") {\n                                  e.target.blur();\n                                }\n                              }}\n                            />\n                          ) : (\n                            <span className=\"text-gray-900 mr-2\">\n                              {visiblePasswords[card.id]\n                                ? card.actualPassword\n                                : card.password}\n                            </span>\n                          )}\n                          <button\n                            onClick={() => togglePasswordVisibility(card.id)}\n                            className=\"text-gray-400 hover:text-gray-600 mr-2\"\n                          >\n                            <span className=\"material-symbols-rounded text-sm\">\n                              {visiblePasswords[card.id]\n                                ? \"visibility_off\"\n                                : \"visibility\"}\n                            </span>\n                          </button>\n                          <button\n                            onClick={() => copyToClipboard(card.actualPassword)}\n                            className=\"text-gray-400 hover:text-gray-600\"\n                          >\n                            <span className=\"material-symbols-rounded text-sm\">\n                              content_copy\n                            </span>\n                          </button>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.team}\n                            className=\"text-gray-900 border rounded px-2 py-1 w-full\"\n                            onBlur={(e) => {\n                              setNewPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, team: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                          />\n                        ) : (\n                          <span className=\"text-gray-900\">{card.team}</span>\n                        )}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.department}\n                            className=\"text-gray-900 border rounded px-2 py-1 w-full\"\n                            onBlur={(e) => {\n                              setNewPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, department: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                          />\n                        ) : (\n                          <span className=\"text-gray-900\">{card.department}</span>\n                        )}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <span\n                          className={`px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`}\n                        >\n                          {card.strength}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center space-x-1\">\n                          <button\n                            onClick={() => handleEdit(card.id)}\n                            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\n                            title=\"Edit\"\n                          >\n                            <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\n                          </button>\n                          <button\n                            onClick={() => {\n                              confirmationAlert({\n                                onConfirm: () => {\n                                  setNewPasswordCards((prev) =>\n                                    prev.filter((c) => c.id !== card.id)\n                                  );\n                                },\n                              });\n                            }}\n                            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\n                            title=\"Delete\"\n                          >\n                            <span className=\"material-symbols-outlined text-sm\">delete</span>\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))\n                )}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      )}\n\n      {/* Add New Password Card Button (Bottom) */}\n      <div className=\"flex justify-center mt-6\">\n        <button\n          onClick={handleCreateNewTable}\n          className=\"flex items-center justify-center px-6 py-2 text-sm font-medium text-white rounded-lg bg-primary hover:bg-primary/90 focus:ring-4 focus:ring-primary/30 transition-colors duration-200 focus:outline-none\"\n        >\n          <span className=\"material-symbols-rounded mr-2\">add</span>\n          Add New Password Card\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default PasswordCardsTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,OAAOC,iBAAiB,MAAM,0CAA0C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzE,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,iBAAiB;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EACtE;EACA,MAAM;IAAEC;EAAS,CAAC,GAAGT,iBAAiB,CAAC,CAAC;EACxC,MAAMU,aAAa,GAAGD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,EAAE;;EAElC;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,CACjD;IACEc,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,6CAA6C;IAC7DC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE,wCAAwC;IACvDC,QAAQ,EAAEb,aAAa,CAAE;EAC3B,CAAC,EACD;IACEC,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,iBAAiB;IACxBC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,kBAAkB;IAClCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,iBAAiB;IAC3BC,aAAa,EAAE,8CAA8C;IAC7DC,QAAQ,EAAEb;EACZ,CAAC,EACD;IACEC,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,mBAAmB;IAC1BC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,iBAAiB;IACjCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,aAAa;IACzBC,QAAQ,EAAE,mBAAmB;IAC7BC,aAAa,EAAE,iDAAiD;IAChEC,QAAQ,EAAE,GAAG,CAAE;EACjB,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,aAAa;IACpBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,UAAU;IAC1BC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE,wCAAwC;IACvDC,QAAQ,EAAEb;EACZ,CAAC,EACD;IACEC,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,uBAAuB;IACvCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,oBAAoB;IAChCC,QAAQ,EAAE,iBAAiB;IAC3BC,aAAa,EAAE,8CAA8C;IAC7DC,QAAQ,EAAE,GAAG,CAAE;EACjB,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,eAAe;IACzBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,mBAAmB;IACnCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,mBAAmB;IAC7BC,aAAa,EAAE,iDAAiD;IAChEC,QAAQ,EAAEb;EACZ,CAAC,EACD;IACEC,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,gBAAgB;IACvBC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,cAAc;IAC9BC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE,wCAAwC;IACvDC,QAAQ,EAAEb;EACZ,CAAC,CACF,CAAC;EAEF,MAAM,CAACc,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC;IAAE0C,GAAG,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAM,CAAC,CAAC;EAC7E,MAAMC,wBAAwB,GAAI9B,EAAE,IAAK;IACvCc,mBAAmB,CAAEiB,IAAI,KAAM;MAC7B,GAAGA,IAAI;MACP,CAAC/B,EAAE,GAAG,CAAC+B,IAAI,CAAC/B,EAAE;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMgC,eAAe,GAAIC,IAAI,IAAK;IAChCC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMI,UAAU,GAAIrC,EAAE,IAAK;IACzBuB,eAAe,CAACD,YAAY,KAAKtB,EAAE,GAAG,IAAI,GAAGA,EAAE,CAAC;EAClD,CAAC;;EAED;EACA,MAAMsC,oBAAoB,GAAGA,CAAA,KAAM;IACjCpB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMqB,qBAAqB,GAAGA,CAAA,KAAM;IAClC;IACA,MAAMC,QAAQ,GAAGvC,aAAa,CAACwC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC9B,QAAQ,KAAKb,aAAa,CAAC;IAE5E,IAAI,CAACyC,QAAQ,EAAE;MACbG,KAAK,CAAC,8CAA8C,CAAC;MACrD;IACF;;IAEA;IACA,IAAInB,cAAc,CAACoB,MAAM,KAAK,CAAC,EAAE;MAC/BD,KAAK,CAAC,qDAAqD,CAAC;MAC5D;IACF;IAEAvD,iBAAiB,CAAC;MAChByD,SAAS,EAAEA,CAAA,KAAM;QACf3C,gBAAgB,CAAC,EAAE,CAAC;MACtB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;;EAEA,MAAM4C,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAItB,cAAc,CAACoB,MAAM,KAAK3C,aAAa,CAAC2C,MAAM,EAAE;MAClDnB,iBAAiB,CAAC,EAAE,CAAC;IACvB,CAAC,MAAM;MACLA,iBAAiB,CAACxB,aAAa,CAAC8C,GAAG,CAAEL,IAAI,IAAKA,IAAI,CAAC1C,EAAE,CAAC,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAMgD,UAAU,GAAIpB,GAAG,IAAK;IAC1B,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIH,UAAU,CAACE,GAAG,KAAKA,GAAG,IAAIF,UAAU,CAACG,SAAS,KAAK,KAAK,EAAE;MAC5DA,SAAS,GAAG,MAAM;IACpB;IACAF,aAAa,CAAC;MAAEC,GAAG;MAAEC;IAAU,CAAC,CAAC;EACnC,CAAC;;EAED;EACA,MAAMoB,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAI,CAACxB,UAAU,CAACE,GAAG,EAAE,OAAOsB,KAAK;IAEjC,OAAO,CAAC,GAAGA,KAAK,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC/B,MAAMC,MAAM,GAAGF,CAAC,CAAC1B,UAAU,CAACE,GAAG,CAAC,CAAC2B,WAAW,CAAC,CAAC;MAC9C,MAAMC,MAAM,GAAGH,CAAC,CAAC3B,UAAU,CAACE,GAAG,CAAC,CAAC2B,WAAW,CAAC,CAAC;MAE9C,IAAI7B,UAAU,CAACG,SAAS,KAAK,KAAK,EAAE;QAClC,OAAOyB,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MACvD,CAAC,MAAM;QACL,OAAOF,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MACvD;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIzD,EAAE,IAAK;IAC9ByB,iBAAiB,CAAEM,IAAI,IACrBA,IAAI,CAAC2B,QAAQ,CAAC1D,EAAE,CAAC,GAAG+B,IAAI,CAAC4B,MAAM,CAAEC,MAAM,IAAKA,MAAM,KAAK5D,EAAE,CAAC,GAAG,CAAC,GAAG+B,IAAI,EAAE/B,EAAE,CAC3E,CAAC;EACH,CAAC;;EAED;EACA,MAAM6D,qBAAqB,GAAIC,QAAQ,IAAK;IAC1C,MAAMC,OAAO,GAAG;MACd,GAAGD,QAAQ;MACX9D,EAAE,EAAEgE,IAAI,CAACC,GAAG,CAAC,CAAC;MACd3D,QAAQ,EAAE,cAAc;MACxBC,cAAc,EAAEuD,QAAQ,CAACxD,QAAQ;MACjCM,QAAQ,EAAEb,aAAa,CAAE;IAC3B,CAAC;IACDsB,mBAAmB,CAAEU,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEgC,OAAO,CAAC,CAAC;IACjD/C,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMkD,YAAY,GAAIlE,EAAE,IAAK;IAC3BZ,iBAAiB,CAAC;MAChByD,SAAS,EAAEA,CAAA,KAAM;QACf3C,gBAAgB,CAAE6B,IAAI,IAAKA,IAAI,CAAC4B,MAAM,CAAEjB,IAAI,IAAKA,IAAI,CAAC1C,EAAE,KAAKA,EAAE,CAAC,CAAC;MACnE;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMmE,WAAW,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACxB;IACA,MAAM7B,QAAQ,GAAGvC,aAAa,CAACwC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC9B,QAAQ,KAAKb,aAAa,CAAC;IAE5E,IAAI,CAACyC,QAAQ,EAAE;MACbG,KAAK,CAAC,sCAAsC,CAAC;MAC7C;IACF;;IAEA;IACA,MAAM2B,qBAAqB,GAAG,CAAAxE,QAAQ,aAARA,QAAQ,wBAAAsE,qBAAA,GAARtE,QAAQ,CAAEyE,WAAW,cAAAH,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAwB,CAAC,CAAC,cAAAC,sBAAA,uBAA1BA,sBAAA,CAA4BG,IAAI,KAAI,IAAI;;IAEtE;IACA,MAAMC,eAAe,GAAGxE,aAAa,CAAC0D,MAAM,CACzCjB,IAAI,IAAKA,IAAI,CAACjC,UAAU,KAAK6D,qBAAqB,IAAI5B,IAAI,CAAC9B,QAAQ,KAAKb,aAC3E,CAAC;IAED,IAAI0E,eAAe,CAAC7B,MAAM,KAAK,CAAC,EAAE;MAChCD,KAAK,CAAC,0DAA0D,CAAC;MACjE;IACF;;IAEA;IACA,MAAM+B,SAAS,GAAG;MAChBvE,KAAK,EAAE,GAAGmE,qBAAqB,4BAA4B;MAC3DpB,KAAK,EAAEuB,eAAe;MACtBE,QAAQ,EAAE,GAAG7E,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE8E,KAAK,IAAI9E,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE+E,KAAK,EAAE,IAAI,cAAc;MACnEC,QAAQ,EAAE,IAAId,IAAI,CAAC,CAAC,CAACe,WAAW,CAAC;IACnC,CAAC;;IAED;IACA7C,SAAS,CAACC,SAAS,CAChBC,SAAS,CAAC4C,IAAI,CAACC,SAAS,CAACP,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAC7CQ,IAAI,CAAC,MAAM;MACVvC,KAAK,CACH,GAAG8B,eAAe,CAAC7B,MAAM,wBAAwB0B,qBAAqB,kCACxE,CAAC;IACH,CAAC,CAAC,CACDa,KAAK,CAAC,MAAM;MACXxC,KAAK,CAAC,gDAAgD,CAAC;IACzD,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMyC,YAAY,GAAG,CACnB,wDAAwD,EACxD,wDAAwD,EACxD,wDAAwD,EACxD,wDAAwD,CACzD;EAED,oBACE7F,OAAA;IAAK8F,SAAS,EAAC,2BAA2B;IAAAC,QAAA,GACvCnE,aAAa,iBACZ5B,OAAA,CAAAE,SAAA;MAAA6F,QAAA,gBAEE/F,OAAA;QAAK8F,SAAS,EAAC,iGAAiG;QAAAC,QAAA,gBAC9G/F,OAAA;UAAK8F,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C/F,OAAA;YAAI8F,SAAS,EAAC,4DAA4D;YAAAC,QAAA,EAAC;UAE3E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGLnG,OAAA;YACEoG,OAAO,EAAExB,WAAY;YACrBkB,SAAS,EAAC,+KAA+K;YAAAC,QAAA,eAEzL/F,OAAA;cAAM8F,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAGTnG,OAAA;YACEoG,OAAO,EAAEA,CAAA,KAAMpD,qBAAqB,CAAC,CAAE;YACvC8C,SAAS,EAAC,qGAAqG;YAC/GlF,KAAK,EAAC,qBAAqB;YAAAmF,QAAA,eAG3B/F,OAAA;cAAM8F,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGTnG,OAAA;YAAK8F,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BF,YAAY,CAACrC,GAAG,CAAC,CAAC6C,MAAM,EAAEC,KAAK,kBAC9BtG,OAAA;cAEEuG,GAAG,EAAEF,MAAO;cACZG,GAAG,EAAE,QAAQF,KAAK,GAAG,CAAC,EAAG;cACzBR,SAAS,EAAC;YAAiE,GAHtEQ,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnG,OAAA;UAAK8F,SAAS,EAAC,0IAA0I;UAAAC,QAAA,eAEvJ/F,OAAA;YACEoG,OAAO,EAAEA,CAAA,KAAM;cACbpE,eAAe,CAAC,IAAI,CAAC;cACrBP,cAAc,CAAC,CAACD,WAAW,CAAC;YAC9B,CAAE;YACFsE,SAAS,EAAE,ucACTtE,WAAW,GACP,uBAAuB,GACvB,qDAAqD,EACxD;YAAAuE,QAAA,gBAEH/F,OAAA;cAAM8F,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAE5D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL3E,WAAW,iBACVxB,OAAA,CAACJ,mBAAmB;QAClB6G,QAAQ,EAAEnC,qBAAsB;QAChCoC,QAAQ,EAAEA,CAAA,KAAM;UACdjF,cAAc,CAAC,KAAK,CAAC;UACrBO,eAAe,CAAC,IAAI,CAAC;QACvB,CAAE;QACF5B,iBAAiB,EAAEA,iBAAkB;QACrCC,gBAAgB,EAAEA;MAAiB;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACF,eAGDnG,OAAA;QAAK8F,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnE/F,OAAA;UAAO8F,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACvB/F,OAAA;YAAO8F,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eACpD/F,OAAA;cAAA+F,QAAA,gBACE/F,OAAA;gBAAI8F,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,eACtC/F,OAAA;kBACES,EAAE,EAAC,cAAc;kBACjBkG,IAAI,EAAC,UAAU;kBACfC,OAAO,EACL3E,cAAc,CAACoB,MAAM,KAAK3C,aAAa,CAAC2C,MAAM,IAC9C3C,aAAa,CAAC2C,MAAM,GAAG,CACxB;kBACDwD,QAAQ,EAAEtD,eAAgB;kBAChCuC,SAAS,EAAC,iHAAiH;kBACrHA,SAAS,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,eACnE/F,OAAA;kBACE8F,SAAS,EAAC,4CAA4C;kBACtDM,OAAO,EAAEA,CAAA,KAAM3C,UAAU,CAAC,OAAO,CAAE;kBAAAsC,QAAA,gBAEnC/F,OAAA;oBAAA+F,QAAA,EAAM;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClBnG,OAAA;oBAAK8F,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B/F,OAAA;sBACE8F,SAAS,EAAE,WACT3D,UAAU,CAACE,GAAG,KAAK,OAAO,IAC1BF,UAAU,CAACG,SAAS,KAAK,KAAK,GAC1B,eAAe,GACf,eAAe,EAClB;sBAAAyD,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPnG,OAAA;sBACE8F,SAAS,EAAE,WACT3D,UAAU,CAACE,GAAG,KAAK,OAAO,IAC1BF,UAAU,CAACG,SAAS,KAAK,MAAM,GAC3B,eAAe,GACf,eAAe,EAClB;sBAAAyD,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,eACnE/F,OAAA;kBACE8F,SAAS,EAAC,4CAA4C;kBACtDM,OAAO,EAAEA,CAAA,KAAM3C,UAAU,CAAC,UAAU,CAAE;kBAAAsC,QAAA,gBAEtC/F,OAAA;oBAAA+F,QAAA,EAAM;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtBnG,OAAA;oBAAK8F,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B/F,OAAA;sBACE8F,SAAS,EAAE,WACT3D,UAAU,CAACE,GAAG,KAAK,UAAU,IAC7BF,UAAU,CAACG,SAAS,KAAK,KAAK,GAC1B,eAAe,GACf,eAAe,EAClB;sBAAAyD,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPnG,OAAA;sBACE8F,SAAS,EAAE,WACT3D,UAAU,CAACE,GAAG,KAAK,UAAU,IAC7BF,UAAU,CAACG,SAAS,KAAK,MAAM,GAC3B,eAAe,GACf,eAAe,EAClB;sBAAAyD,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRnG,OAAA;YAAO8F,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EACxCrC,cAAc,CAAChD,aAAa,CAAC,CAAC8C,GAAG,CAAEL,IAAI,iBACtCnD,OAAA;cAAkB8F,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC5C/F,OAAA;gBAAI8F,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB/F,OAAA;kBACES,EAAE,EAAE,aAAa0C,IAAI,CAAC1C,EAAE,EAAG;kBAC3BkG,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAE3E,cAAc,CAACkC,QAAQ,CAAChB,IAAI,CAAC1C,EAAE,CAAE;kBAC1CoG,QAAQ,EAAEA,CAAA,KAAM3C,eAAe,CAACf,IAAI,CAAC1C,EAAE,CAAE;kBACzCqF,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB/F,OAAA;kBAAK8F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC/F,OAAA;oBAAK8F,SAAS,EAAC,gGAAgG;oBAAAC,QAAA,EAAC;kBAEhH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,EACLpE,YAAY,KAAKoB,IAAI,CAAC1C,EAAE,gBACvBT,OAAA;oBACE2G,IAAI,EAAC,MAAM;oBACXG,YAAY,EAAE3D,IAAI,CAACvC,KAAM;oBACzBkF,SAAS,EAAC,2DAA2D;oBACrEiB,MAAM,EAAGC,CAAC,IAAK;sBACbrG,gBAAgB,CAAE6B,IAAI,IACpBA,IAAI,CAACgB,GAAG,CAAEyD,CAAC,IACTA,CAAC,CAACxG,EAAE,KAAK0C,IAAI,CAAC1C,EAAE,GACZ;wBAAE,GAAGwG,CAAC;wBAAErG,KAAK,EAAEoG,CAAC,CAACE,MAAM,CAACC;sBAAM,CAAC,GAC/BF,CACN,CACF,CAAC;oBACH,CAAE;oBACFG,SAAS,EAAGJ,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAAC3E,GAAG,KAAK,OAAO,EAAE;wBACrB2E,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC;sBACjB;oBACF,CAAE;oBACFC,SAAS;kBAAA;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,gBAEFnG,OAAA;oBAAM8F,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EACxC5C,IAAI,CAACvC;kBAAK;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB/F,OAAA;kBAAK8F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,GAC/BhE,YAAY,KAAKoB,IAAI,CAAC1C,EAAE,gBACvBT,OAAA;oBACE2G,IAAI,EAAC,MAAM;oBACXG,YAAY,EAAE3D,IAAI,CAACrC,QAAS;oBAC5BgF,SAAS,EAAC,oDAAoD;oBAC9DiB,MAAM,EAAGC,CAAC,IAAK;sBACbrG,gBAAgB,CAAE6B,IAAI,IACpBA,IAAI,CAACgB,GAAG,CAAEyD,CAAC,IACTA,CAAC,CAACxG,EAAE,KAAK0C,IAAI,CAAC1C,EAAE,GACZ;wBAAE,GAAGwG,CAAC;wBAAEnG,QAAQ,EAAEkG,CAAC,CAACE,MAAM,CAACC;sBAAM,CAAC,GAClCF,CACN,CACF,CAAC;oBACH,CAAE;oBACFG,SAAS,EAAGJ,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAAC3E,GAAG,KAAK,OAAO,EAAE;wBACrB2E,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC;sBACjB;oBACF;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEFnG,OAAA;oBAAM8F,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAE5C,IAAI,CAACrC;kBAAQ;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACtD,eACDnG,OAAA;oBACEoG,OAAO,EAAEA,CAAA,KAAM3D,eAAe,CAACU,IAAI,CAACrC,QAAQ,CAAE;oBAC9CgF,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eAElD/F,OAAA;sBAAM8F,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB/F,OAAA;kBAAK8F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,GAC/BhE,YAAY,KAAKoB,IAAI,CAAC1C,EAAE,gBACvBT,OAAA;oBACE2G,IAAI,EAAC,MAAM;oBACXG,YAAY,EAAE3D,IAAI,CAACnC,cAAe;oBAClC8E,SAAS,EAAC,oDAAoD;oBAC9DiB,MAAM,EAAGC,CAAC,IAAK;sBACbrG,gBAAgB,CAAE6B,IAAI,IACpBA,IAAI,CAACgB,GAAG,CAAEyD,CAAC,IACTA,CAAC,CAACxG,EAAE,KAAK0C,IAAI,CAAC1C,EAAE,GACZ;wBAAE,GAAGwG,CAAC;wBAAEjG,cAAc,EAAEgG,CAAC,CAACE,MAAM,CAACC;sBAAM,CAAC,GACxCF,CACN,CACF,CAAC;oBACH,CAAE;oBACFG,SAAS,EAAGJ,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAAC3E,GAAG,KAAK,OAAO,EAAE;wBACrB2E,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC;sBACjB;oBACF;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEFnG,OAAA;oBAAM8F,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EACjCzE,gBAAgB,CAAC6B,IAAI,CAAC1C,EAAE,CAAC,GACtB0C,IAAI,CAACnC,cAAc,GACnBmC,IAAI,CAACpC;kBAAQ;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CACP,eACDnG,OAAA;oBACEoG,OAAO,EAAEA,CAAA,KAAM7D,wBAAwB,CAACY,IAAI,CAAC1C,EAAE,CAAE;oBACjDqF,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eAElD/F,OAAA;sBAAM8F,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC/CzE,gBAAgB,CAAC6B,IAAI,CAAC1C,EAAE,CAAC,GACtB,gBAAgB,GAChB;oBAAY;sBAAAuF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACTnG,OAAA;oBACEoG,OAAO,EAAEA,CAAA,KAAM3D,eAAe,CAACU,IAAI,CAACnC,cAAc,CAAE;oBACpD8E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,eAE7C/F,OAAA;sBAAM8F,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtBhE,YAAY,KAAKoB,IAAI,CAAC1C,EAAE,gBACvBT,OAAA;kBACE2G,IAAI,EAAC,MAAM;kBACXG,YAAY,EAAE3D,IAAI,CAAClC,IAAK;kBACxB6E,SAAS,EAAC,+CAA+C;kBACzDiB,MAAM,EAAGC,CAAC,IAAK;oBACbrG,gBAAgB,CAAE6B,IAAI,IACpBA,IAAI,CAACgB,GAAG,CAAEyD,CAAC,IACTA,CAAC,CAACxG,EAAE,KAAK0C,IAAI,CAAC1C,EAAE,GACZ;sBAAE,GAAGwG,CAAC;sBAAEhG,IAAI,EAAE+F,CAAC,CAACE,MAAM,CAACC;oBAAM,CAAC,GAC9BF,CACN,CACF,CAAC;kBACH,CAAE;kBACFG,SAAS,EAAGJ,CAAC,IAAK;oBAChB,IAAIA,CAAC,CAAC3E,GAAG,KAAK,OAAO,EAAE;sBACrB2E,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC;oBACjB;kBACF;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEFnG,OAAA;kBAAM8F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAE5C,IAAI,CAAClC;gBAAI;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAClD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtBhE,YAAY,KAAKoB,IAAI,CAAC1C,EAAE,gBACvBT,OAAA;kBACE2G,IAAI,EAAC,MAAM;kBACXG,YAAY,EAAE3D,IAAI,CAACjC,UAAW;kBAC9B4E,SAAS,EAAC,+CAA+C;kBACzDiB,MAAM,EAAGC,CAAC,IAAK;oBACbrG,gBAAgB,CAAE6B,IAAI,IACpBA,IAAI,CAACgB,GAAG,CAAEyD,CAAC,IACTA,CAAC,CAACxG,EAAE,KAAK0C,IAAI,CAAC1C,EAAE,GACZ;sBAAE,GAAGwG,CAAC;sBAAE/F,UAAU,EAAE8F,CAAC,CAACE,MAAM,CAACC;oBAAM,CAAC,GACpCF,CACN,CACF,CAAC;kBACH,CAAE;kBACFG,SAAS,EAAGJ,CAAC,IAAK;oBAChB,IAAIA,CAAC,CAAC3E,GAAG,KAAK,OAAO,EAAE;sBACrB2E,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC;oBACjB;kBACF;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEFnG,OAAA;kBAAM8F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAE5C,IAAI,CAACjC;gBAAU;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cACxD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB/F,OAAA;kBACE8F,SAAS,EAAE,8CAA8C3C,IAAI,CAAC/B,aAAa,EAAG;kBAAA2E,QAAA,EAE7E5C,IAAI,CAAChC;gBAAQ;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB/F,OAAA;kBAAK8F,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C/F,OAAA;oBACEoG,OAAO,EAAEA,CAAA,KAAMtD,UAAU,CAACK,IAAI,CAAC1C,EAAE,CAAE;oBACnCqF,SAAS,EAAC,mLAAmL;oBAC7LlF,KAAK,EAAC,MAAM;oBAAAmF,QAAA,eAEZ/F,OAAA;sBAAM8F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACTnG,OAAA;oBACEoG,OAAO,EAAEA,CAAA,KAAMzB,YAAY,CAACxB,IAAI,CAAC1C,EAAE,CAAE;oBACrCqF,SAAS,EAAC,mLAAmL;oBAC7LlF,KAAK,EAAC,QAAQ;oBAAAmF,QAAA,eAEd/F,OAAA;sBAAM8F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAzMEhD,IAAI,CAAC1C,EAAE;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0MZ,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA,eACN,CACH,EAGAzE,YAAY,iBACX1B,OAAA;MAAK8F,SAAS,EAAC,MAAM;MAAAC,QAAA,gBAEnB/F,OAAA;QAAK8F,SAAS,EAAC,iGAAiG;QAAAC,QAAA,gBAC9G/F,OAAA;UAAK8F,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C/F,OAAA;YAAI8F,SAAS,EAAC,4DAA4D;YAAAC,QAAA,EAAC;UAE3E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGLnG,OAAA;YACEoG,OAAO,EAAExB,WAAY;YACrBkB,SAAS,EAAC,+KAA+K;YAAAC,QAAA,eAEzL/F,OAAA;cAAM8F,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAGTnG,OAAA;YACEoG,OAAO,EAAEA,CAAA,KAAMpD,qBAAqB,CAAC,CAAE;YACvC8C,SAAS,EAAC,qGAAqG;YAC/GlF,KAAK,EAAC,qBAAqB;YAAAmF,QAAA,eAE3B/F,OAAA;cAAM8F,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGTnG,OAAA;YAAK8F,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BF,YAAY,CAACrC,GAAG,CAAC,CAAC6C,MAAM,EAAEC,KAAK,kBAC9BtG,OAAA;cAEEuG,GAAG,EAAEF,MAAO;cACZG,GAAG,EAAE,QAAQF,KAAK,GAAG,CAAC,EAAG;cACzBR,SAAS,EAAC;YAAiE,GAHtEQ,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnG,OAAA;UAAK8F,SAAS,EAAC,0IAA0I;UAAAC,QAAA,eAEvJ/F,OAAA;YACEoG,OAAO,EAAEA,CAAA,KAAM;cACbpE,eAAe,CAAC,IAAI,CAAC;cACrBP,cAAc,CAAC,CAACD,WAAW,CAAC;YAC9B,CAAE;YACFsE,SAAS,EAAE,ucACTtE,WAAW,GACP,uBAAuB,GACvB,qDAAqD,EACxD;YAAAuE,QAAA,gBAEH/F,OAAA;cAAM8F,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAE5D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL3E,WAAW,iBACVxB,OAAA,CAACJ,mBAAmB;QAClB6G,QAAQ,EAAEnC,qBAAsB;QAChCoC,QAAQ,EAAEA,CAAA,KAAM;UACdjF,cAAc,CAAC,KAAK,CAAC;UACrBO,eAAe,CAAC,IAAI,CAAC;QACvB,CAAE;QACF5B,iBAAiB,EAAEA,iBAAkB;QACrCC,gBAAgB,EAAEA;MAAiB;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACF,eAEDnG,OAAA;QAAK8F,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnE/F,OAAA;UAAO8F,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACvB/F,OAAA;YAAO8F,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eACpD/F,OAAA;cAAA+F,QAAA,gBACE/F,OAAA;gBAAI8F,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,eACtC/F,OAAA;kBACES,EAAE,EAAC,kBAAkB;kBACrBkG,IAAI,EAAC,UAAU;kBACfC,OAAO,EACL3E,cAAc,CAACoB,MAAM,KAAKxB,gBAAgB,CAACwB,MAAM,IACjDxB,gBAAgB,CAACwB,MAAM,GAAG,CAC3B;kBACDwD,QAAQ,EAAEA,CAAA,KAAM;oBACd,IAAI5E,cAAc,CAACoB,MAAM,KAAKxB,gBAAgB,CAACwB,MAAM,EAAE;sBACrDnB,iBAAiB,CAAC,EAAE,CAAC;oBACvB,CAAC,MAAM;sBACLA,iBAAiB,CAACL,gBAAgB,CAAC2B,GAAG,CAAEL,IAAI,IAAKA,IAAI,CAAC1C,EAAE,CAAC,CAAC;oBAC5D;kBACF,CAAE;kBACFqF,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,eACnE/F,OAAA;kBACE8F,SAAS,EAAC,4CAA4C;kBACtDM,OAAO,EAAEA,CAAA,KAAM3C,UAAU,CAAC,OAAO,CAAE;kBAAAsC,QAAA,gBAEnC/F,OAAA;oBAAA+F,QAAA,EAAM;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClBnG,OAAA;oBAAK8F,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B/F,OAAA;sBACE8F,SAAS,EAAE,WACT3D,UAAU,CAACE,GAAG,KAAK,OAAO,IAC1BF,UAAU,CAACG,SAAS,KAAK,KAAK,GAC1B,eAAe,GACf,eAAe,EAClB;sBAAAyD,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPnG,OAAA;sBACE8F,SAAS,EAAE,WACT3D,UAAU,CAACE,GAAG,KAAK,OAAO,IAC1BF,UAAU,CAACG,SAAS,KAAK,MAAM,GAC3B,eAAe,GACf,eAAe,EAClB;sBAAAyD,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,eACnE/F,OAAA;kBACE8F,SAAS,EAAC,4CAA4C;kBACtDM,OAAO,EAAEA,CAAA,KAAM3C,UAAU,CAAC,UAAU,CAAE;kBAAAsC,QAAA,gBAEtC/F,OAAA;oBAAA+F,QAAA,EAAM;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtBnG,OAAA;oBAAK8F,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B/F,OAAA;sBACE8F,SAAS,EAAE,WACT3D,UAAU,CAACE,GAAG,KAAK,UAAU,IAC7BF,UAAU,CAACG,SAAS,KAAK,KAAK,GAC1B,eAAe,GACf,eAAe,EAClB;sBAAAyD,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPnG,OAAA;sBACE8F,SAAS,EAAE,WACT3D,UAAU,CAACE,GAAG,KAAK,UAAU,IAC7BF,UAAU,CAACG,SAAS,KAAK,MAAM,GAC3B,eAAe,GACf,eAAe,EAClB;sBAAAyD,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRnG,OAAA;YAAO8F,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EACxClE,gBAAgB,CAACwB,MAAM,KAAK,CAAC,gBAC5BrD,OAAA;cAAA+F,QAAA,eACE/F,OAAA;gBACEuH,OAAO,EAAC,GAAG;gBACXzB,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAChD;cAGD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GAELzC,cAAc,CAAC7B,gBAAgB,CAAC,CAAC2B,GAAG,CAAEL,IAAI,iBACxCnD,OAAA;cAAkB8F,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC5C/F,OAAA;gBAAI8F,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB/F,OAAA;kBACE2G,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAE3E,cAAc,CAACkC,QAAQ,CAAChB,IAAI,CAAC1C,EAAE,CAAE;kBAC1CoG,QAAQ,EAAEA,CAAA,KAAM3C,eAAe,CAACf,IAAI,CAAC1C,EAAE,CAAE;kBACzCqF,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB/F,OAAA;kBAAK8F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC/F,OAAA;oBAAK8F,SAAS,EAAC,gGAAgG;oBAAAC,QAAA,EAAC;kBAEhH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,EACLpE,YAAY,KAAKoB,IAAI,CAAC1C,EAAE,gBACvBT,OAAA;oBACE2G,IAAI,EAAC,MAAM;oBACXG,YAAY,EAAE3D,IAAI,CAACvC,KAAM;oBACzBkF,SAAS,EAAC,2DAA2D;oBACrEiB,MAAM,EAAGC,CAAC,IAAK;sBACblF,mBAAmB,CAAEU,IAAI,IACvBA,IAAI,CAACgB,GAAG,CAAEyD,CAAC,IACTA,CAAC,CAACxG,EAAE,KAAK0C,IAAI,CAAC1C,EAAE,GACZ;wBAAE,GAAGwG,CAAC;wBAAErG,KAAK,EAAEoG,CAAC,CAACE,MAAM,CAACC;sBAAM,CAAC,GAC/BF,CACN,CACF,CAAC;oBACH,CAAE;oBACFG,SAAS,EAAGJ,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAAC3E,GAAG,KAAK,OAAO,EAAE;wBACrB2E,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC;sBACjB;oBACF,CAAE;oBACFC,SAAS;kBAAA;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,gBAEFnG,OAAA;oBAAM8F,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EACxC5C,IAAI,CAACvC;kBAAK;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB/F,OAAA;kBAAK8F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,GAC/BhE,YAAY,KAAKoB,IAAI,CAAC1C,EAAE,gBACvBT,OAAA;oBACE2G,IAAI,EAAC,MAAM;oBACXG,YAAY,EAAE3D,IAAI,CAACrC,QAAS;oBAC5BgF,SAAS,EAAC,oDAAoD;oBAC9DiB,MAAM,EAAGC,CAAC,IAAK;sBACblF,mBAAmB,CAAEU,IAAI,IACvBA,IAAI,CAACgB,GAAG,CAAEyD,CAAC,IACTA,CAAC,CAACxG,EAAE,KAAK0C,IAAI,CAAC1C,EAAE,GACZ;wBAAE,GAAGwG,CAAC;wBAAEnG,QAAQ,EAAEkG,CAAC,CAACE,MAAM,CAACC;sBAAM,CAAC,GAClCF,CACN,CACF,CAAC;oBACH,CAAE;oBACFG,SAAS,EAAGJ,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAAC3E,GAAG,KAAK,OAAO,EAAE;wBACrB2E,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC;sBACjB;oBACF;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEFnG,OAAA;oBAAM8F,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAE5C,IAAI,CAACrC;kBAAQ;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACtD,eACDnG,OAAA;oBACEoG,OAAO,EAAEA,CAAA,KAAM3D,eAAe,CAACU,IAAI,CAACrC,QAAQ,CAAE;oBAC9CgF,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eAElD/F,OAAA;sBAAM8F,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB/F,OAAA;kBAAK8F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,GAC/BhE,YAAY,KAAKoB,IAAI,CAAC1C,EAAE,gBACvBT,OAAA;oBACE2G,IAAI,EAAC,MAAM;oBACXG,YAAY,EAAE3D,IAAI,CAACnC,cAAe;oBAClC8E,SAAS,EAAC,oDAAoD;oBAC9DiB,MAAM,EAAGC,CAAC,IAAK;sBACblF,mBAAmB,CAAEU,IAAI,IACvBA,IAAI,CAACgB,GAAG,CAAEyD,CAAC,IACTA,CAAC,CAACxG,EAAE,KAAK0C,IAAI,CAAC1C,EAAE,GACZ;wBAAE,GAAGwG,CAAC;wBAAEjG,cAAc,EAAEgG,CAAC,CAACE,MAAM,CAACC;sBAAM,CAAC,GACxCF,CACN,CACF,CAAC;oBACH,CAAE;oBACFG,SAAS,EAAGJ,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAAC3E,GAAG,KAAK,OAAO,EAAE;wBACrB2E,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC;sBACjB;oBACF;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEFnG,OAAA;oBAAM8F,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EACjCzE,gBAAgB,CAAC6B,IAAI,CAAC1C,EAAE,CAAC,GACtB0C,IAAI,CAACnC,cAAc,GACnBmC,IAAI,CAACpC;kBAAQ;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CACP,eACDnG,OAAA;oBACEoG,OAAO,EAAEA,CAAA,KAAM7D,wBAAwB,CAACY,IAAI,CAAC1C,EAAE,CAAE;oBACjDqF,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eAElD/F,OAAA;sBAAM8F,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC/CzE,gBAAgB,CAAC6B,IAAI,CAAC1C,EAAE,CAAC,GACtB,gBAAgB,GAChB;oBAAY;sBAAAuF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACTnG,OAAA;oBACEoG,OAAO,EAAEA,CAAA,KAAM3D,eAAe,CAACU,IAAI,CAACnC,cAAc,CAAE;oBACpD8E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,eAE7C/F,OAAA;sBAAM8F,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtBhE,YAAY,KAAKoB,IAAI,CAAC1C,EAAE,gBACvBT,OAAA;kBACE2G,IAAI,EAAC,MAAM;kBACXG,YAAY,EAAE3D,IAAI,CAAClC,IAAK;kBACxB6E,SAAS,EAAC,+CAA+C;kBACzDiB,MAAM,EAAGC,CAAC,IAAK;oBACblF,mBAAmB,CAAEU,IAAI,IACvBA,IAAI,CAACgB,GAAG,CAAEyD,CAAC,IACTA,CAAC,CAACxG,EAAE,KAAK0C,IAAI,CAAC1C,EAAE,GACZ;sBAAE,GAAGwG,CAAC;sBAAEhG,IAAI,EAAE+F,CAAC,CAACE,MAAM,CAACC;oBAAM,CAAC,GAC9BF,CACN,CACF,CAAC;kBACH,CAAE;kBACFG,SAAS,EAAGJ,CAAC,IAAK;oBAChB,IAAIA,CAAC,CAAC3E,GAAG,KAAK,OAAO,EAAE;sBACrB2E,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC;oBACjB;kBACF;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEFnG,OAAA;kBAAM8F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAE5C,IAAI,CAAClC;gBAAI;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAClD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtBhE,YAAY,KAAKoB,IAAI,CAAC1C,EAAE,gBACvBT,OAAA;kBACE2G,IAAI,EAAC,MAAM;kBACXG,YAAY,EAAE3D,IAAI,CAACjC,UAAW;kBAC9B4E,SAAS,EAAC,+CAA+C;kBACzDiB,MAAM,EAAGC,CAAC,IAAK;oBACblF,mBAAmB,CAAEU,IAAI,IACvBA,IAAI,CAACgB,GAAG,CAAEyD,CAAC,IACTA,CAAC,CAACxG,EAAE,KAAK0C,IAAI,CAAC1C,EAAE,GACZ;sBAAE,GAAGwG,CAAC;sBAAE/F,UAAU,EAAE8F,CAAC,CAACE,MAAM,CAACC;oBAAM,CAAC,GACpCF,CACN,CACF,CAAC;kBACH,CAAE;kBACFG,SAAS,EAAGJ,CAAC,IAAK;oBAChB,IAAIA,CAAC,CAAC3E,GAAG,KAAK,OAAO,EAAE;sBACrB2E,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC;oBACjB;kBACF;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEFnG,OAAA;kBAAM8F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAE5C,IAAI,CAACjC;gBAAU;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cACxD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB/F,OAAA;kBACE8F,SAAS,EAAE,8CAA8C3C,IAAI,CAAC/B,aAAa,EAAG;kBAAA2E,QAAA,EAE7E5C,IAAI,CAAChC;gBAAQ;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLnG,OAAA;gBAAI8F,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB/F,OAAA;kBAAK8F,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C/F,OAAA;oBACEoG,OAAO,EAAEA,CAAA,KAAMtD,UAAU,CAACK,IAAI,CAAC1C,EAAE,CAAE;oBACnCqF,SAAS,EAAC,mLAAmL;oBAC7LlF,KAAK,EAAC,MAAM;oBAAAmF,QAAA,eAEZ/F,OAAA;sBAAM8F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACTnG,OAAA;oBACEoG,OAAO,EAAEA,CAAA,KAAM;sBACbvG,iBAAiB,CAAC;wBAChByD,SAAS,EAAEA,CAAA,KAAM;0BACfxB,mBAAmB,CAAEU,IAAI,IACvBA,IAAI,CAAC4B,MAAM,CAAE6C,CAAC,IAAKA,CAAC,CAACxG,EAAE,KAAK0C,IAAI,CAAC1C,EAAE,CACrC,CAAC;wBACH;sBACF,CAAC,CAAC;oBACJ,CAAE;oBACFqF,SAAS,EAAC,mLAAmL;oBAC7LlF,KAAK,EAAC,QAAQ;oBAAAmF,QAAA,eAEd/F,OAAA;sBAAM8F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAhNEhD,IAAI,CAAC1C,EAAE;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiNZ,CACL;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDnG,OAAA;MAAK8F,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvC/F,OAAA;QACEoG,OAAO,EAAErD,oBAAqB;QAC9B+C,SAAS,EAAC,0MAA0M;QAAAC,QAAA,gBAEpN/F,OAAA;UAAM8F,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,yBAE5D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7F,EAAA,CA3iCIH,kBAAkB;AAAAqH,EAAA,GAAlBrH,kBAAkB;AA6iCxB,eAAeA,kBAAkB;AAAC,IAAAqH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}