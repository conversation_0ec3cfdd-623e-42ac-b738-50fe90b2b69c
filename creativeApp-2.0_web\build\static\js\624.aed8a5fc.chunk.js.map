{"version": 3, "file": "static/js/624.aed8a5fc.chunk.js", "mappings": "qQAGA,MAAMA,EAAUC,+DAgMhB,EA9LqBC,IAA6C,IAA5C,UAAEC,EAAS,WAAEC,EAAU,YAAEC,GAAaH,EACxD,MAAOI,EAAcC,IAAmBC,EAAAA,EAAAA,UAAS,KAC1CC,EAAWC,IAAgBF,EAAAA,EAAAA,UAAS,KACpCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,KAC5BK,EAAgBC,IAAqBN,EAAAA,EAAAA,UAAS,KAC9CO,EAAcC,IAAmBR,EAAAA,EAAAA,UAAS,OAGjDS,EAAAA,EAAAA,YAAU,KACN,MAAMC,EAASC,aAAaC,QAAQ,WAChCF,GACAF,EAAgBE,EACpB,GACD,KAEHD,EAAAA,EAAAA,YAAU,KACoBI,WACtB,IAAKhB,EAAa,OAElB,MAAMiB,EAAQH,aAAaC,QAAQ,SACnC,GAAKE,EAKL,IACI,MAAMC,QAAiBC,MAAM,GAAGxB,cAAqB,CACjDyB,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUJ,IAC3B,eAAgB,sBAIxB,IAAKC,EAASI,GACV,MAAM,IAAIC,MAAM,8BAAgCL,EAASM,YAG7D,MAEMC,SAFaP,EAASQ,QAEU,UACtC,IAAKC,MAAMC,QAAQH,GACf,MAAM,IAAIF,MAAM,sCAIpB,MAAMM,EAAeJ,EAAcK,MAAKC,GAAYA,EAASC,KAAOhC,IACpE,IAAI6B,EAGA,MAAM,IAAIN,MAAM,4CAFhBrB,EAAgB2B,EAAaI,eAIrC,CAAE,MAAO3B,GACLC,EAASD,EAAM4B,QACnB,MAjCI3B,EAAS,iCAiCb,EAGJ4B,EAAmB,GACpB,CAACnC,IAwFJ,OAAKF,GAGDsC,EAAAA,EAAAA,KAAA,OACIC,UAAU,sGACVC,QAASA,IAAMvC,GAAW,GAAOwC,UAEjCC,EAAAA,EAAAA,MAAA,OACIH,UAAU,6DACVC,QAAUG,GAAMA,EAAEC,kBAAmBH,SAAA,EAErCC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,yCAAwCE,SAAA,EACnDH,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAAuBE,SAAC,qBACtCH,EAAAA,EAAAA,KAAA,UACIC,UAAU,oCACVC,QAASA,IAAMvC,GAAW,GAAOwC,SACpC,aAILC,EAAAA,EAAAA,MAAA,QAAMG,SAzGG3B,UACjB4B,EAAMC,iBACN,MAAMC,EAAsB7C,EAAa8C,OAEnCC,EAAYtC,EAElB,GAAKsC,EAAL,CAKA,GAAIrB,MAAMC,QAAQxB,GAAY,CAM1B,GALuBA,EAAU6C,MAAKlB,GACRA,EAASE,eAAeiB,cAAcH,SACnCD,EAAoBI,gBAGjC,CAChB3C,EAAS,6DACT,MAAM4C,EAAYC,YAAW,IAAM7C,EAAS,KAAK,KACjD,MAAO,IAAM8C,aAAaF,EAC9B,CACJ,CAEA5C,EAAS,IAET,IACI,MAAMU,EAAQH,aAAaC,QAAQ,SAE7BG,QAAiBC,MAAM,GAAGxB,cAAqB,CACjDyB,OAAQ,OACRC,QAAS,CACL,cAAiB,UAAUJ,IAC3B,eAAgB,oBAEpBqC,KAAMC,KAAKC,UAAU,CACjBvB,eAAgBa,EAChBW,WAAYT,MAIpB,IAAK9B,EAASI,GACV,MAAM,IAAIC,MAAM,4BAA8BL,EAASM,YAG3D,MAAMkC,QAAexC,EAASQ,OAEFgC,EAAO3B,SAASE,gBAI5C0B,EAAAA,EAAAA,IAAa,CACTC,KAAM,UACNC,MAAO,WACPC,MAAY,OAANJ,QAAM,IAANA,OAAM,EAANA,EAAQxB,UAAW,mCAG7BhC,EAAgB,IAGhB,MAAM6D,QAA6B5C,MAAM,GAAGxB,cAAqB,CAC7DyB,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUJ,IAC3B,eAAgB,sBAIxB,IAAK8C,EAAqBzC,GACtB,MAAM,IAAIC,MAAM,8BAAgCwC,EAAqBvC,YAGzE,MAAMwC,QAAyBD,EAAqBrC,OACpDrB,EAAa2D,EAA4B,WAAK,IAE9CZ,YAAW,KACPrD,GAAW,GACXU,EAAkB,GAAG,GACtB,IAEP,CAAE,MAAOH,IACLqD,EAAAA,EAAAA,IAAa,QACjB,CAzEA,MAFIpD,EAAS,yBA2Eb,EAuBqCgC,SAAA,EACzBC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,OAAME,SAAA,EACjBH,EAAAA,EAAAA,KAAA,SAAO6B,QAAQ,OAAO5B,UAAU,aAAYE,SAAC,mBAC7CH,EAAAA,EAAAA,KAAA,SACI8B,KAAK,OACLlC,GAAG,OACHmC,MAAOlE,EACPmE,SAAW3B,GAAMvC,EAAgBuC,EAAE4B,OAAOF,OAC1C9B,UAAU,4BACViC,UAAQ,QAGhBlC,EAAAA,EAAAA,KAAA,UACI8B,KAAK,SACL7B,UAAU,gEAA+DE,SAC5E,4BAnCM,IAwCb,E,qCCtKd,MAAMgC,EAAc,gBAygBpB,EAtgByBC,KAEvB,MAAOC,EAAeC,IAAoBvE,EAAAA,EAAAA,UAAS,CAAC,IAC7CwE,EAAuBC,IAA4BzE,EAAAA,EAAAA,UAAS,CAAC,IAC7D0E,EAAkBC,IAAuB3E,EAAAA,EAAAA,UAAS,KAClD4E,EAAaC,IAAkB7E,EAAAA,EAAAA,UAAS,KACxC8E,EAAcC,IAAmB/E,EAAAA,EAAAA,WAAS,IAC1CgF,EAAqBC,IAA0BjF,EAAAA,EAAAA,WAAS,IACxDH,EAAaqF,IAAkBlF,EAAAA,EAAAA,UAAS,OACxCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,OAC5BmF,EAAUC,IAAepF,EAAAA,EAAAA,UAAS,OAElCqF,EAAiBC,KADPC,EAAAA,EAAAA,OAC6BvF,EAAAA,EAAAA,WAAS,KAIhDwF,EAAYC,IAAiBzF,EAAAA,EAAAA,UAAS,eACtC0F,EAAeC,IAAoB3F,EAAAA,EAAAA,UAAS,SAC5C4F,EAASC,IAAc7F,EAAAA,EAAAA,UAAS,OAChC8F,EAAaC,IAAkB/F,EAAAA,EAAAA,UAAS,IAGvCgG,KAAMC,EAAS,WAAEC,EAAY/F,MAAOgG,IAAeC,EAAAA,EAAAA,KAAwB,CAAEC,QAASb,EAAYc,MAAOZ,EAAea,KAAMT,EAAaU,SAAUZ,EAASa,MAAO7B,KAEtK8B,GAAwBV,KAAMW,EAAWxG,MAAOyG,KAAoBC,EAAAA,EAAAA,QAEpEC,IAAkBC,EAAAA,EAAAA,OAGnBC,EAAoBC,IACxB,IAAIC,EAAIC,OAAOC,QAAQH,GAAiBI,QAAO,CAACC,EAAG5H,KAAoB,IAAjB6H,EAAKvD,GAAMtE,EAC/D,GAAqB,kBAAVsE,EACT,OAAOsD,EAAM,IAAIC,KAAOvD,IAE1B,GAAIxC,MAAMC,QAAQuC,GAAQ,CAExB,OAAOsD,EAAM,IAAIC,KADJvD,EAAMwD,KAAKC,GAAMA,EAAEzD,QAAO0D,KAAK,MAE9C,CACA,OAAOJ,CAAG,GACT,IAEHzC,EAAeqC,EAAE,EAGbS,EAAc3B,KAEE4B,EAAAA,EAAAA,IAAW5B,EADV,CAAC,KAAM,OAAQ,aAAc,aAAc,aAAc,UAAW,aAAc,UAAW,aAAc,eAEhIZ,EAAY,MACZL,GAAgB,EAAK,EAGjB8C,EAAchG,IAClBuD,EAAY,MACZF,EAAerD,GACfkD,GAAgB,EAAK,EAGjB+C,GAAgBjG,KACpBkG,EAAAA,EAAAA,IAAkB,CAACC,UAAWA,KAE1BlB,EAAejF,GACfuD,EAAY,KAAK,GAChB,EAIP,IAAI6C,GAAe,EAEnB,MAAM,gBAAEC,KAAoBC,EAAAA,EAAAA,MAGrBC,GAASC,KAAcrI,EAAAA,EAAAA,WAAS,IAAM,CAC3C,CACI6B,GAAIoG,KACNK,KAAM,SACNC,MAAO,QACPrG,UAAW,aACXsG,KAAOC,IACLpG,EAAAA,EAAAA,MAAA,OAAKH,UAAU,sCAAqCE,SAAA,EAElDH,EAAAA,EAAAA,KAAA,UACEC,UAAU,wLACVC,QAASA,IAAMiD,EAAYqD,GAAMrG,UAEjCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,kBAItC,OAAf8F,SAAe,IAAfA,QAAe,EAAfA,GAAiBQ,kBAChBzG,EAAAA,EAAAA,KAAA,UACEC,UAAU,oLACVC,QAASA,IAAM0F,EAAWY,EAAK5G,IAAIO,UAEnCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,mBAKxC,OAAf8F,SAAe,IAAfA,QAAe,EAAfA,GAAiBQ,kBAChBzG,EAAAA,EAAAA,KAAA,UACEC,UAAU,sLACVC,QAASA,IAAMwF,EAAWc,GAAMrG,UAEhCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,oBAKxC,OAAf8F,SAAe,IAAfA,QAAe,EAAfA,GAAiBQ,kBAChBzG,EAAAA,EAAAA,KAAA,UACEC,UAAU,oLACVC,QAASA,IAAM2F,GAAaW,EAAK5G,IAAIO,UAErCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,iBAM9D,CACIP,GAAIoG,KACNK,KAAM,OACNK,SAAUA,CAACC,EAAKC,KAAW/C,EAAc,GAAKF,EAAUiD,EAAQ,EAChEN,MAAO,OACPO,MAAM,GAER,CACIjH,GAAIoG,KACNK,KAAM,WACNS,SAAU,OACVJ,SAAWC,GAAQA,EAAI9G,gBAAkB,GACzCgH,MAAM,EACNE,UAAU,EACVC,YAAY,GAEd,CACIpH,GAAIoG,KACJK,KAAM,aACNK,SAAWC,IAAG,IAAAM,EAAAC,EAAA,MAAK,IAAc,QAAXD,EAAAN,EAAIQ,eAAO,IAAAF,OAAA,EAAXA,EAAaG,QAAS,OAAiB,QAAXF,EAAAP,EAAIQ,eAAO,IAAAD,OAAA,EAAXA,EAAaG,QAAS,IAAI,EAC5EP,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,GAEhB,CACEpH,GAAIoG,KACJK,KAAM,eACNK,SAAWC,IAAQW,EAAAA,EAAAA,IAAkBX,EAAIY,YACzCT,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,GAEd,CACIpH,GAAIoG,KACJK,KAAM,eACNK,SAAWC,IAAQa,EAAAA,EAAAA,IAAmBb,EAAIY,YAC1CT,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,GAEd,CACEpH,GAAIoG,KACJK,KAAM,aACNK,SAAWC,IAAG,IAAAc,EAAAC,EAAA,MAAK,IAAc,QAAXD,EAAAd,EAAIgB,eAAO,IAAAF,OAAA,EAAXA,EAAaL,QAAS,OAAiB,QAAXM,EAAAf,EAAIgB,eAAO,IAAAD,OAAA,EAAXA,EAAaL,QAAS,IAAI,EAC5EP,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,GAEhB,CACEpH,GAAIoG,KACJK,KAAM,eACNK,SAAWC,IAAQW,EAAAA,EAAAA,IAAkBX,EAAIiB,YACzCd,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,GAEd,CACIpH,GAAIoG,KACJK,KAAM,eACNK,SAAWC,IAAQa,EAAAA,EAAAA,IAAmBb,EAAIiB,YAC1Cd,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,OAIlBxI,EAAAA,EAAAA,YAAU,KAER4H,IAAYyB,GAAgB,IACvBA,EAAYtC,KAAKuC,GACD,WAAbA,EAAIzB,KAEC,IACFyB,EACHvB,KAAOC,IACLpG,EAAAA,EAAAA,MAAA,OAAKH,UAAU,sCAAqCE,SAAA,EAClDH,EAAAA,EAAAA,KAAA,UACEC,UAAU,wLACVC,QAASA,IAAMiD,EAAYqD,GAAMrG,UAEjCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,kBAEtC,OAAf8F,SAAe,IAAfA,QAAe,EAAfA,GAAiBQ,kBAChBzG,EAAAA,EAAAA,KAAA,UACEC,UAAU,oLACVC,QAASA,IAAM0F,EAAWY,EAAK5G,IAAIO,UAEnCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,mBAIxC,OAAf8F,SAAe,IAAfA,QAAe,EAAfA,GAAiBQ,kBAClBzG,EAAAA,EAAAA,KAAA,UACEC,UAAU,sLACVC,QAASA,IAAMwF,EAAWc,GAAMrG,UAEhCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,oBAItC,OAAf8F,SAAe,IAAfA,QAAe,EAAfA,GAAiBQ,kBAClBzG,EAAAA,EAAAA,KAAA,UACEC,UAAU,oLACVC,QAASA,IAAM2F,GAAaW,EAAK5G,IAAIO,UAErCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,iBAOvD2H,MAET,GACD,CAAC7B,KAKJ,MAkBM8B,IAAWC,EAAAA,EAAAA,MAqDXC,IAA8BC,EAAAA,EAAAA,cAClCtJ,iBAKM,IAJJuJ,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACdtG,EAAIsG,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,QACPG,EAASH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACZI,EAASJ,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,SAGRK,EAAeN,EAAWrB,UAAY,QAE1C,IACEpE,EAAoB+F,GACpBzF,GAAuB,GAEvB,IAAI0B,EAAY,GAEhB,MAAM5F,QAAiB2F,EAAqB,CAAE3C,KAAMA,EAAKnB,OAAQ+H,OAAQD,EAAa9H,OAAQe,KAAM6G,EAAU5H,SAM9G,GAJI7B,EAASiF,OACXW,EAAY5F,EAASiF,MAGnBW,EAAU2D,OAAQ,CAEpB,GAAkB,eAAdG,EAMF,OALAlG,GAAkBqG,IAAI,IACjBA,EACH,CAACF,GAAe/D,MAGXA,EAGT,MAAMkE,EAAmBlE,EACtBa,KAAKiB,IACJ,GAAG2B,EAAWzB,SAAS,CACrB,IAAImC,EAAQV,EAAWzB,SAASF,GAEhC,OAAGqC,GACGrC,EAAKsC,OAAStC,EAAKsC,MAAQ,IAC7BD,GAAS,KAAKrC,EAAKsC,UAGd,CAAED,QAAO9G,MAAOyE,EAAKiC,KAGzB,IACP,KACCM,OAAOC,SAOZ,OALA1G,GAAkBqG,IAAI,IACjBA,EACH,CAACR,EAAWvI,KAAKqJ,EAAAA,EAAAA,IAAYL,OAGxBA,CACT,CACF,CAAE,MAAO1K,GACPC,EAASD,EAAM4B,QACjB,CAAC,QACCkD,GAAuB,EACzB,CACF,GACA,IAGF,OACEhD,EAAAA,EAAAA,KAAA,WAASC,UAAU,gEAA+DE,UAChFC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,gBAAeE,SAAA,EAE5BC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,iGAAgGE,SAAA,EAC7GH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+BAA8BE,UAC3CH,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sBAAqBE,SAAEgC,OAEvC/B,EAAAA,EAAAA,MAAA,OAAKH,UAAU,0CAAyCE,SAAA,EAEtDH,EAAAA,EAAAA,KAACkJ,EAAAA,GAAa,CAAC/C,QAASA,GAASC,WAAYA,MAG1CnC,GAAcD,GAAamF,SAASnF,EAAU8E,OAAS,IACxD9I,EAAAA,EAAAA,KAAAoJ,EAAAA,SAAA,CAAAjJ,UACEC,EAAAA,EAAAA,MAAA,UACEH,UAAU,oZACVC,QAvIMtB,UACpB,IAEE,MAAM0C,QAAeyG,GACnBsB,EAAAA,IAAYC,UAAUC,gBAAgBC,SAAS,CAC7CpF,QAASb,EACTc,MAAOZ,EACPa,KAAMT,EACNU,UAAmB,OAATP,QAAS,IAATA,OAAS,EAATA,EAAW8E,QAAS,GAC9BtE,MAAO7B,KAET8G,SAEF,GAAW,OAANnI,QAAM,IAANA,IAAAA,EAAQwH,OAASxH,EAAOwH,MAAQ,EACnC,OAAO,EAGT,IAAIY,EAAK,EAET,IAAIC,EAAcrI,EAAOyC,KAAKwB,KAAKiB,IACjC,GAAIL,GAAQkC,OAAQ,CAClB,IAAIuB,EAAM,CAAC,EAMX,OALAzD,GAAQ0D,SAASnB,KACVA,EAAO7B,MAAQ6B,EAAOhC,WACzBkD,EAAIlB,EAAOrC,MAAwB,SAAhBqC,EAAOrC,KAAkBqD,IAAOhB,EAAOhC,SAASF,IAAS,GAC9E,IAEKoD,CACT,KAIF,MAAME,EAAYC,EAAAA,GAAWC,cAAcL,GACrCM,EAAWF,EAAAA,GAAWG,WAC5BH,EAAAA,GAAWI,kBAAkBF,EAAUH,EAAW,UAGlD,MAAMM,EAAcL,EAAAA,GAAWE,EAAU,CACvCI,SAAU,OACVvI,KAAM,UAEFwI,EAAO,IAAIC,KAAK,CAACH,GAAc,CAAEtI,KAAM,8BAC7C0I,EAAAA,EAAAA,QAAOF,EAAM,GAAGnI,EAAYsI,QAAQ,KAAK,QAAQd,EAAYtB,cAC/D,CAAE,MAAOnK,GACPwM,QAAQxM,MAAM,4BAA6BA,EAC7C,GA0FqCiC,SAAA,CAEtB8D,IACCjE,EAAAA,EAAAA,KAAAoJ,EAAAA,SAAA,CAAAjJ,UACEH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sDAAqDE,SAAC,yBAKxE8D,IACAjE,EAAAA,EAAAA,KAAA,QAAMC,UAAU,yCAAwCE,SAAC,gBAGzD,oBACgB6D,EAAU8E,MAAM,SAKvC7C,GAAgBQ,iBACfzG,EAAAA,EAAAA,KAAA,UACEC,UAAU,gYAEVC,QAASA,IAAMmD,GAAmB,GAAMlD,SACzC,mBAQPH,EAAAA,EAAAA,KAAC2K,EAAAA,GAAY,CACTxE,QAASA,GACT5D,sBAAuBA,EACvBC,yBAA0BA,EAC1ByF,4BAA6BA,GAC7B5F,cAAeA,EACfU,oBAAqBA,EACrBN,iBAAkBA,EAClBmI,UAlMQA,KAChB,GAAI1F,OAAO2F,KAAKtI,GAAuB8F,OAAQ,CAC7C,IAAIyC,EAAS,CAAC,EACd5F,OAAO2F,KAAKtI,GAAuBgD,KAAKD,IACI,kBAA/B/C,EAAsB+C,GAC/BwF,EAAOxF,GAAO,GAEdwF,EAAOxF,GAAO,EAChB,IAEF9C,EAAyB,IAAKsI,IAC9B/F,EAAiB,IAAK+F,GACxB,CACAhH,EAAe,EAAE,EAsLTA,eAAgBA,EAChBiB,iBAAkBA,IAIrBb,IAAclE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcE,SAAEjC,IAE7C+F,IAAcjE,EAAAA,EAAAA,KAAC+K,EAAAA,EAAO,KAKvB/K,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mDAAkDE,UAC/DH,EAAAA,EAAAA,KAACgL,EAAAA,GAAS,CACR7E,QAASA,GACTpC,MAAe,OAATC,QAAS,IAATA,OAAS,EAATA,EAAWD,OAAQ,GACzB9D,UAAU,8BACVgL,aAAW,EAEXC,kBAAgB,EAChBC,YAAU,EACVC,YAAU,EACVC,kBAAgB,EAChBC,kBAAmB3H,EACnB4H,qBAA8B,OAATvH,QAAS,IAATA,OAAS,EAATA,EAAW8E,QAAS,EACzC0C,aAAelH,IACTA,IAAST,GACXC,EAAeQ,EACjB,EAEFmH,oBAAsBC,IACjBA,IAAe/H,IAChBC,EAAW8H,GACX5H,EAAe,GACjB,EAEF6H,2BAA4B,CAC1BC,mBAAmB,EACnBC,sBAAuB,OAEzBC,YAAU,EACVC,OAAQ,SAACrD,GAAkC,IAA1BjF,EAAa2E,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAC,OAC1BlD,OAAO2F,KAAKnC,GAAQL,SACrB7E,EAAckF,EAAO5B,UAAY4B,EAAOrC,MAAQ,cAChD3C,EAAiBD,GAAiB,QAEtC,MAKHL,IACGpD,EAAAA,EAAAA,KAACgM,EAAAA,EAAW,CACRtO,UAAW0F,EACXzF,WAAY0F,IAKnBR,IACC7C,EAAAA,EAAAA,KAACiM,EAAY,CACXvO,UAAWmF,EACXlF,WAAYmF,EACZlF,YAAaA,IAIhBsF,IAEClD,EAAAA,EAAAA,KAACkM,EAAAA,GAAS,CAAC1F,KAAMtD,EAAUC,YAAaA,EAAagD,QAASA,GAASP,WAAYA,EAAYC,aAAcA,SAIzG,EChhBd,EATiBsG,KAEbnM,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCE,UACrDH,EAAAA,EAAAA,KAACoC,EAAgB,K", "sources": ["pages/location/EditLocation.jsx", "pages/location/LocationDataList.jsx", "dashboard/Location.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { alertMessage } from '../../common/coreui';\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst EditLocation = ({ isVisible, setVisible, dataItemsId }) => {\r\n    const [locationName, setLocationName] = useState('');\r\n    const [locations, setLocations] = useState([]);\r\n    const [error, setError] = useState('');\r\n    const [successMessage, setSuccessMessage] = useState('');\r\n    const [loggedInUser, setLoggedInUser] = useState(null);\r\n\r\n    // Fetch logged-in user data (user_id)\r\n    useEffect(() => {\r\n        const userId = localStorage.getItem('user_id');\r\n        if (userId) {\r\n            setLoggedInUser(userId);\r\n        }\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        const fetchLocationName = async () => {\r\n            if (!dataItemsId) return;\r\n    \r\n            const token = localStorage.getItem('token');\r\n            if (!token) {\r\n                setError('No authentication token found.');\r\n                return;\r\n            }\r\n    \r\n            try {\r\n                const response = await fetch(`${API_URL}/locations`, { // Update endpoint for locations\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n    \r\n                if (!response.ok) {\r\n                    throw new Error('Failed to fetch locations: ' + response.statusText);\r\n                }\r\n    \r\n                const data = await response.json();\r\n    \r\n                const locationArray = data['locations']; // Adjusted to use 'locations' data\r\n                if (!Array.isArray(locationArray)) {\r\n                    throw new Error('Expected locations to be an array.');\r\n                }\r\n    \r\n                // Find the location by ID\r\n                const locationData = locationArray.find(location => location.id === dataItemsId);\r\n                if (locationData) {\r\n                    setLocationName(locationData.locations_name); // Set the name from the matching location\r\n                } else {\r\n                    throw new Error('Location not found. Please check the ID.');\r\n                }\r\n            } catch (error) {\r\n                setError(error.message);\r\n            }\r\n        };\r\n    \r\n        fetchLocationName();\r\n    }, [dataItemsId]);\r\n    \r\n    // Update the Location\r\n    const handleSubmit = async (event) => {\r\n        event.preventDefault();\r\n        const trimmedLocationName = locationName.trim();\r\n\r\n        const updatedBy = loggedInUser;\r\n\r\n        if (!updatedBy) {\r\n            setError('User is not logged in.');\r\n            return;\r\n        }\r\n    \r\n        if (Array.isArray(locations)) {\r\n            const locationExists = locations.some(location => {\r\n                const locationNameLower = location.locations_name.toLowerCase().trim();\r\n                return locationNameLower === trimmedLocationName.toLowerCase();\r\n            });\r\n    \r\n            if (locationExists) {\r\n                setError('Location already exists. Please add a different location.');\r\n                const timeoutId = setTimeout(() => setError(''), 3000);\r\n                return () => clearTimeout(timeoutId);\r\n            }\r\n        }\r\n    \r\n        setError('');\r\n    \r\n        try {\r\n            const token = localStorage.getItem('token');\r\n    \r\n            const response = await fetch(`${API_URL}/locations`, { // Update endpoint for locations\r\n                method: 'POST',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n                body: JSON.stringify({\r\n                    locations_name: trimmedLocationName,\r\n                    updated_by: updatedBy,\r\n                }),\r\n            });\r\n    \r\n            if (!response.ok) {\r\n                throw new Error('Failed to save location: ' + response.statusText);\r\n            }\r\n    \r\n            const result = await response.json();\r\n    \r\n            const updatedLocationName = result.location.locations_name; // Adjusted to use the 'location' object\r\n    \r\n            //setSuccessMessage(`Location \"${updatedLocationName}\" updated successfully!`);\r\n\r\n            alertMessage({\r\n                icon: 'success',\r\n                title: 'Success!',\r\n                text: result?.message || 'Location updated successfully.',\r\n            });\r\n\r\n            setLocationName('');\r\n    \r\n            // Optionally, refetch locations\r\n            const newLocationsResponse = await fetch(`${API_URL}/locations`, { // Update endpoint for locations\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n    \r\n            if (!newLocationsResponse.ok) {\r\n                throw new Error('Failed to fetch locations: ' + newLocationsResponse.statusText);\r\n            }\r\n    \r\n            const newLocationsData = await newLocationsResponse.json();\r\n            setLocations(newLocationsData['locations'] || []);\r\n            // Close the modal after a short delay\r\n            setTimeout(() => {\r\n                setVisible(false);\r\n                setSuccessMessage(''); // Clear the success message\r\n            }, 1000);\r\n            \r\n        } catch (error) {\r\n            alertMessage('error');\r\n        }\r\n    };\r\n\r\n    if (!isVisible) return null;\r\n\r\n    return (\r\n        <div\r\n            className=\"fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50\"\r\n            onClick={() => setVisible(false)}\r\n        >\r\n            <div\r\n                className=\"relative bg-white rounded-lg shadow-lg max-w-md w-full p-5\"\r\n                onClick={(e) => e.stopPropagation()} // Prevent click from closing the modal\r\n            >\r\n                <div className=\"flex justify-between items-center mb-4\">\r\n                    <h3 className=\"text-lg font-semibold\">Update Location</h3>\r\n                    <button\r\n                        className=\"text-gray-500 hover:text-gray-800\"\r\n                        onClick={() => setVisible(false)}\r\n                    >\r\n                        &times;\r\n                    </button>\r\n                </div>\r\n                <form onSubmit={handleSubmit}>\r\n                    <div className=\"mb-4\">\r\n                        <label htmlFor=\"name\" className=\"block mb-2\">Location Name</label>\r\n                        <input\r\n                            type=\"text\"\r\n                            id=\"name\"\r\n                            value={locationName}\r\n                            onChange={(e) => setLocationName(e.target.value)}\r\n                            className=\"border rounded w-full p-2\"\r\n                            required\r\n                        />\r\n                    </div>\r\n                    <button\r\n                        type=\"submit\"\r\n                        className=\"bg-primary hover:bg-secondary text-white rounded-lg px-4 py-2\"\r\n                    >\r\n                        Update Location\r\n                    </button>\r\n                </form>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default EditLocation;\r\n", "import React, { useState, useCallback, useEffect } from \"react\";\r\n\r\n// DataTable component for rendering tabular data with features like pagination and sorting\r\nimport DataTable from \"react-data-table-component\";\r\n\r\n// Loading spinner component to show while data is loading\r\nimport Loading from \"./../../common/Loading\";\r\n\r\nimport {confirmation<PERSON><PERSON>t, ManageColumns, SearchFilter, TableView} from './../../common/coreui';\r\n\r\n\r\nimport { useDispatch } from \"react-redux\";\r\nimport { defaultDateTimeFormat, removeKeys, sortByLabel } from \"./../../utils\";\r\n\r\n// Libraries for exporting data to Excel\r\nimport { saveAs } from \"file-saver\";\r\nimport * as XLSX from \"xlsx\";\r\nimport { locationApi, useDeleteLocationMutation, useGetLocationDataQuery, useLazyFetchDataOptionsForLocationQuery } from \"./../../features/api\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport EditLocation from \"./EditLocation\";\r\nimport AddLocation from \"./AddLocation\";\r\nimport { useRoleBasedAccess } from \"./../../common/useRoleBasedAccess\";\r\nimport { DateTimeFormatDay, DateTimeFormatHour } from \"../../common/DateTimeFormatTable\";\r\n\r\n// API endpoint and configuration constants\r\nconst MODULE_NAME = \"Work Location\";\r\n\r\n// Main component for listing Product Type List\r\nconst LocationDataList = () => {\r\n  // State variables for data items, filters, search text, modals, and loading status\r\n  const [filterOptions, setFilterOptions] = useState({});\r\n  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});\r\n  const [showFilterOption, setShowFilterOption] = useState(\"\");\r\n  const [queryString, setQueryString] = useState(\"\");\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [filterOptionLoading, setFilterOptionLoading] = useState(false);\r\n  const [dataItemsId, setDataItemsId] = useState(null);\r\n  const [error, setError] = useState(null);\r\n  const [viewData, setViewData] = useState(null);\r\n  const navigate = useNavigate();\r\n  const [addModalVisible, setAddModalVisible] = useState(false);\r\n\r\n  \r\n  // Sorting and pagination state\r\n  const [sortColumn, setSortColumn] = useState(\"created_at\");\r\n  const [sortDirection, setSortDirection] = useState(\"desc\");\r\n  const [perPage, setPerPage] = useState(\"10\");\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n\r\n  \r\n  const { data: dataItems, isFetching, error: fetchError } = useGetLocationDataQuery({ sort_by: sortColumn, order: sortDirection, page: currentPage, per_page: perPage, query: queryString });\r\n\r\n  const [triggerFilterByFetch, { data: groupData, error: groupDataError }] = useLazyFetchDataOptionsForLocationQuery();\r\n       \r\n  const [deleteLocation] = useDeleteLocationMutation();\r\n\r\n  // Build query parameters from selected filters\r\n  const buildQueryParams = (selectedFilters) => {\r\n    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {\r\n      if (typeof value === \"string\") {\r\n        return acc + `&${key}=${value}`;\r\n      }\r\n      if (Array.isArray(value)) {\r\n        const vals = value.map((i) => i.value).join(\",\");\r\n        return acc + `&${key}=${vals}`;\r\n      }\r\n      return acc;\r\n    }, \"\")\r\n\r\n    setQueryString(q);\r\n  }\r\n\r\n  const handleCopy = (data) => {\r\n    const keysToRemove = [\"id\", \"team\", \"department\", \"updated_at\", \"updated_by\", \"updater\", \"created_at\", \"creator\", \"created_by\", \"updated_by\"];\r\n    const cleanedData = removeKeys(data, keysToRemove);\r\n    setViewData(null)\r\n    setModalVisible(true);\r\n  }\r\n\r\n  const handleEdit = (id) => {\r\n    setViewData(null)\r\n    setDataItemsId(id); \r\n    setModalVisible(true);\r\n  }\r\n\r\n  const handleDelete = (id) => {\r\n    confirmationAlert({onConfirm: () => \r\n      {        \r\n        deleteLocation(id);\r\n        setViewData(null);\r\n      }});  \r\n  }\r\n \r\n\r\n  let columnSerial = 1;\r\n\r\n  const { rolePermissions } = useRoleBasedAccess();\r\n\r\n  // Define columns dynamically based on rolePermissions\r\n  const [columns, setColumns] = useState(() => [\r\n    {\r\n        id: columnSerial++,\r\n      name: \"Action\",\r\n      width: \"180px\",\r\n      className: \"bg-red-300\",\r\n      cell: (item) => (\r\n        <div className=\"flex gap-1 mx-2 !min-w-[200px] pl-3\">\r\n          {/* View Button */}\r\n          <button\r\n            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n            onClick={() => setViewData(item)}\r\n          >\r\n            <span className=\"material-symbols-outlined text-lg\">visibility</span>\r\n          </button>\r\n  \r\n          {/* Conditionally render Edit Button based on rolePermissions */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleEdit(item.id)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\r\n            </button>\r\n          )}\r\n  \r\n          {/* Copy Button */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleCopy(item)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-lg\">content_copy</span>\r\n            </button>\r\n          )}\r\n  \r\n          {/* Delete Button */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleDelete(item.id)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-sm\">delete</span>\r\n            </button>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n      name: \"S.No\",\r\n      selector: (row, index) => (currentPage - 1) * perPage + index + 1,\r\n      width: \"80px\",\r\n      omit: false,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n      name: \"Location\",\r\n      db_field: \"name\",\r\n      selector: (row) => row.locations_name || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n        name: \"Created by\",\r\n        selector: (row) => `${row.creator?.fname || \"\"} ${row.creator?.lname || \"\"}`,\r\n        db_field: \"created_by\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Created Date\",\r\n      selector: (row) => DateTimeFormatDay(row.created_at),\r\n      db_field: \"created_at\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n        name: \"Created Time\",\r\n        selector: (row) => DateTimeFormatHour(row.created_at),\r\n        db_field: \"created_at\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: false,\r\n      },\r\n      {\r\n        id: columnSerial++,\r\n        name: \"Updated by\",\r\n        selector: (row) => `${row.updater?.fname || \"\"} ${row.updater?.lname || \"\"}`,\r\n        db_field: \"updated_by\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: true,\r\n      },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Updated Date\",\r\n      selector: (row) => DateTimeFormatDay(row.updated_at),\r\n      db_field: \"updated_at\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n        name: \"Updated Time\",\r\n        selector: (row) => DateTimeFormatHour(row.updated_at),\r\n        db_field: \"updated_at\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: false,\r\n    },\r\n  ]);\r\n  \r\n  useEffect(() => {\r\n    // Recalculate or update columns if rolePermissions change\r\n    setColumns((prevColumns) => [\r\n      ...prevColumns.map((col) => {\r\n        if (col.name === \"Action\") {\r\n          // Update the \"Action\" column dynamically\r\n          return {\r\n            ...col,\r\n            cell: (item) => (\r\n              <div className=\"flex gap-1 mx-2 !min-w-[200px] pl-3\">\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => setViewData(item)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-lg\">visibility</span>\r\n                </button>\r\n                {rolePermissions?.hasManagerRole && (\r\n                  <button\r\n                    className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                    onClick={() => handleEdit(item.id)}\r\n                  >\r\n                    <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\r\n                  </button>\r\n                )}\r\n\r\n                {rolePermissions?.hasManagerRole && (\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => handleCopy(item)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-lg\">content_copy</span>\r\n                </button>\r\n                )}\r\n\r\n                {rolePermissions?.hasManagerRole && (\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => handleDelete(item.id)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-sm\">delete</span>\r\n                </button>\r\n                )}\r\n              </div>\r\n            ),\r\n          };\r\n        }\r\n        return col;\r\n      }),\r\n    ]);\r\n  }, [rolePermissions]); // Dependency array ensures this is updated whenever rolePermissions changes\r\n  \r\n  \r\n\r\n  // Resets the pagination and clear-all filter state\r\n  const resetPage = () => {\r\n    if (Object.keys(selectedFilterOptions).length) {\r\n      let newObj = {};\r\n      Object.keys(selectedFilterOptions).map((key) => {\r\n        if (typeof selectedFilterOptions[key] === \"string\") {\r\n          newObj[key] = \"\";\r\n        } else {\r\n          newObj[key] = [];\r\n        }\r\n      });\r\n      setSelectedFilterOptions({ ...newObj });\r\n      buildQueryParams({ ...newObj })\r\n    }\r\n    setCurrentPage(1);\r\n  };\r\n\r\n\r\n  // Export the fetched data into an Excel file\r\n  const dispatch = useDispatch();\r\n  const exportToExcel = async () => {\r\n    try {\r\n      // Fetch all data items for Excel export\r\n      const result = await dispatch(\r\n        locationApi.endpoints.getLocationData.initiate({\r\n          sort_by: sortColumn,\r\n          order: sortDirection,\r\n          page: currentPage,\r\n          per_page: dataItems?.total || 10, // Fallback value to avoid undefined issues\r\n          query: queryString,\r\n        })\r\n      ).unwrap(); // Wait for the API response\r\n  \r\n      if (!result?.total || result.total < 1) {\r\n        return false;\r\n      }\r\n  \r\n      var sl = 1;\r\n  \r\n      let prepXlsData = result.data.map((item) => {\r\n        if (columns.length) {\r\n          let obj = {};\r\n          columns.forEach((column) => {\r\n            if (!column.omit && column.selector) {\r\n              obj[column.name] = column.name === \"S.No\" ? sl++ : column.selector(item) || \"\";\r\n            }\r\n          });\r\n          return obj;\r\n        }\r\n      });\r\n  \r\n      // Create a worksheet from the JSON data and append to a new workbook\r\n      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);\r\n      const workbook = XLSX.utils.book_new();\r\n      XLSX.utils.book_append_sheet(workbook, worksheet, \"Sheet1\");\r\n  \r\n      // Convert workbook to a buffer and create a Blob to trigger a file download\r\n      const excelBuffer = XLSX.write(workbook, {\r\n        bookType: \"xlsx\",\r\n        type: \"array\",\r\n      });\r\n      const blob = new Blob([excelBuffer], { type: \"application/octet-stream\" });\r\n      saveAs(blob, `${MODULE_NAME.replace(/ /g,\"_\")}_${prepXlsData.length}.xlsx`);\r\n    } catch (error) {\r\n      console.error(\"Error exporting to Excel:\", error);\r\n    }\r\n  };\r\n  \r\n\r\n  /**\r\n   * Fetch filter options from API for a specific field.\r\n   */\r\n  const fetchDataOptionsForFilterBy = useCallback(\r\n    async (\r\n      itemObject = {},\r\n      type = \"group\",\r\n      searching = \"\",\r\n      fieldType = \"select\"\r\n    ) => {\r\n\r\n      let groupByField = itemObject.db_field || \"title\";\r\n\r\n      try {\r\n        setShowFilterOption(groupByField);\r\n        setFilterOptionLoading(true);\r\n\r\n        var groupData = [];\r\n\r\n        const response = await triggerFilterByFetch({ type: type.trim(), column: groupByField.trim(), text: searching.trim() });\r\n        \r\n        if (response.data) {\r\n          groupData = response.data;\r\n        }\r\n\r\n        if (groupData.length) {\r\n\r\n          if (fieldType === \"searchable\") {\r\n            setFilterOptions((prev) => ({\r\n              ...prev,\r\n              [groupByField]: groupData,\r\n            }));\r\n\r\n            return groupData;\r\n          }\r\n\r\n          const optionsForFilter = groupData\r\n            .map((item) => {\r\n              if(itemObject.selector){\r\n                let label = itemObject.selector(item);\r\n\r\n                if(label){\r\n                  if (item.total && item.total > 1) {\r\n                    label += ` (${item.total})`;\r\n                  }\r\n\r\n                  return { label, value: item[groupByField] };\r\n                }\r\n\r\n              return null;\r\n              }\r\n            }).filter(Boolean);\r\n\r\n          setFilterOptions((prev) => ({\r\n            ...prev,\r\n            [itemObject.id]: sortByLabel(optionsForFilter),\r\n          }));\r\n\r\n          return optionsForFilter;\r\n        }\r\n      } catch (error) {\r\n        setError(error.message);\r\n      } finally {\r\n        setFilterOptionLoading(false);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  return (\r\n    <section className=\"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\">\r\n      <div className=\"mx-auto pb-6 \">\r\n        {/* Header section with title and action buttons */}\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4\">\r\n          <div className=\"w-4/12 md:w-10/12 text-start\">\r\n            <h2 className=\"text-2xl font-bold \">{MODULE_NAME}</h2>\r\n          </div>\r\n          <div className=\"w-8/12 flex items-end justify-end gap-1\">\r\n            {/* Manage Columns dropdown */}\r\n            <ManageColumns columns={columns} setColumns={setColumns} />\r\n            \r\n            {/* Export to Excel button, only shown if data exists */}\r\n            { !isFetching && dataItems && parseInt(dataItems.total) > 0 && (\r\n              <>\r\n                <button\r\n                  className=\"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\"\r\n                  onClick={exportToExcel}\r\n                >\r\n                  {isFetching && (\r\n                    <>\r\n                      <span className=\"material-symbols-outlined animate-spin text-sm me-2\">\r\n                        progress_activity\r\n                      </span>\r\n                    </>\r\n                  )}\r\n                  {!isFetching && (\r\n                    <span className=\"material-symbols-outlined text-sm me-2\">\r\n                    file_export\r\n                    </span>\r\n                  )}\r\n                  Export to Excel ({dataItems.total})\r\n                </button>\r\n              </>\r\n            )}\r\n            {/* Button to open modal for adding a new formation */}\r\n            {rolePermissions.hasManagerRole && (\r\n              <button\r\n                className=\" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\"\r\n\r\n                onClick={() => setAddModalVisible(true)}\r\n              >\r\n                Add New\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Filter fieldset for global search and field-specific filtering */}\r\n        <SearchFilter\r\n            columns={columns}\r\n            selectedFilterOptions={selectedFilterOptions}\r\n            setSelectedFilterOptions={setSelectedFilterOptions}\r\n            fetchDataOptionsForFilterBy={fetchDataOptionsForFilterBy}\r\n            filterOptions={filterOptions}\r\n            filterOptionLoading={filterOptionLoading}\r\n            showFilterOption={showFilterOption}\r\n            resetPage={resetPage}\r\n            setCurrentPage={setCurrentPage}\r\n            buildQueryParams={buildQueryParams}\r\n        />\r\n\r\n        {/* Display error message if any error occurs */}\r\n        {fetchError && <div className=\"text-red-500\">{error}</div>}\r\n        {/* Show loading spinner when data is being fetched */}\r\n        {isFetching && <Loading />}\r\n\r\n        {/* If no data is available, display an alert message */}\r\n        \r\n        {/* Render the DataTable with the fetched data */}\r\n        <div className=\"border border-gray-200 p-0 pb-1 rounded-lg my-5 \">\r\n          <DataTable\r\n            columns={columns}\r\n            data={dataItems?.data || []}\r\n            className=\"p-0 scrollbar-horizontal-10\"\r\n            fixedHeader\r\n            \r\n            highlightOnHover\r\n            responsive\r\n            pagination\r\n            paginationServer\r\n            paginationPerPage={perPage}\r\n            paginationTotalRows={dataItems?.total || 0}\r\n            onChangePage={(page) => {\r\n              if (page !== currentPage) {\r\n                setCurrentPage(page);\r\n              }\r\n            }}\r\n            onChangeRowsPerPage={(newPerPage) => {\r\n              if(newPerPage !== perPage){\r\n                setPerPage(newPerPage);\r\n                setCurrentPage(1);\r\n              }\r\n            }}\r\n            paginationComponentOptions={{\r\n              selectAllRowsItem: true,\r\n              selectAllRowsItemText: \"ALL\",\r\n            }}\r\n            sortServer\r\n            onSort={(column, sortDirection=\"desc\") => {\r\n              if(Object.keys(column).length){\r\n                setSortColumn(column.db_field || column.name || \"created_at\");\r\n                setSortDirection(sortDirection || \"desc\");\r\n              }\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        {/* Add Modal */}\r\n        {addModalVisible && (\r\n            <AddLocation\r\n                isVisible={addModalVisible}\r\n                setVisible={setAddModalVisible}\r\n            />\r\n        )}\r\n\r\n        {/* Conditionally render the Edit modal */}\r\n        {modalVisible && (\r\n          <EditLocation\r\n            isVisible={modalVisible}\r\n            setVisible={setModalVisible}\r\n            dataItemsId={dataItemsId}\r\n          />\r\n        )}\r\n\r\n        {viewData && (\r\n          // <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />\r\n          <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />\r\n        )}\r\n       \r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\n\r\nexport default LocationDataList;\r\n", "import React from 'react';\r\nimport LocationDataList from '../pages/location/LocationDataList';\r\n\r\n\r\nconst Location = () => {\r\n  return (\r\n    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>\r\n      <LocationDataList />\r\n    </div>\r\n\r\n  );\r\n};\r\n\r\nexport default Location;\r\n"], "names": ["API_URL", "process", "_ref", "isVisible", "setVisible", "dataItemsId", "locationName", "setLocationName", "useState", "locations", "setLocations", "error", "setError", "successMessage", "setSuccessMessage", "loggedInUser", "setLoggedInUser", "useEffect", "userId", "localStorage", "getItem", "async", "token", "response", "fetch", "method", "headers", "ok", "Error", "statusText", "locationArray", "json", "Array", "isArray", "locationData", "find", "location", "id", "locations_name", "message", "fetchLocationName", "_jsx", "className", "onClick", "children", "_jsxs", "e", "stopPropagation", "onSubmit", "event", "preventDefault", "trimmedLocationName", "trim", "updatedBy", "some", "toLowerCase", "timeoutId", "setTimeout", "clearTimeout", "body", "JSON", "stringify", "updated_by", "result", "alertMessage", "icon", "title", "text", "newLocationsResponse", "newLocationsData", "htmlFor", "type", "value", "onChange", "target", "required", "MODULE_NAME", "LocationDataList", "filterOptions", "setFilterOptions", "selectedFilterOptions", "setSelectedFilterOptions", "showFilterOption", "setShowFilterOption", "queryString", "setQueryString", "modalVisible", "setModalVisible", "filterOptionLoading", "setFilterOptionLoading", "setDataItemsId", "viewData", "setViewData", "addModalVisible", "setAddModalVisible", "useNavigate", "sortColumn", "setSortColumn", "sortDirection", "setSortDirection", "perPage", "setPerPage", "currentPage", "setCurrentPage", "data", "dataItems", "isFetching", "fetchError", "useGetLocationDataQuery", "sort_by", "order", "page", "per_page", "query", "triggerFilterByFetch", "groupData", "groupDataError", "useLazyFetchDataOptionsForLocationQuery", "deleteLocation", "useDeleteLocationMutation", "buildQueryParams", "selectedFilters", "q", "Object", "entries", "reduce", "acc", "key", "map", "i", "join", "handleCopy", "<PERSON><PERSON><PERSON><PERSON>", "handleEdit", "handleDelete", "<PERSON><PERSON><PERSON><PERSON>", "onConfirm", "columnSerial", "rolePermissions", "useRoleBasedAccess", "columns", "setColumns", "name", "width", "cell", "item", "hasManagerRole", "selector", "row", "index", "omit", "db_field", "sortable", "filterable", "_row$creator", "_row$creator2", "creator", "fname", "lname", "DateTimeFormatDay", "created_at", "DateTimeFormatHour", "_row$updater", "_row$updater2", "updater", "updated_at", "prevColumns", "col", "dispatch", "useDispatch", "fetchDataOptionsForFilterBy", "useCallback", "itemObject", "arguments", "length", "undefined", "searching", "fieldType", "groupByField", "column", "prev", "optionsForFilter", "label", "total", "filter", "Boolean", "sortByLabel", "ManageColumns", "parseInt", "_Fragment", "locationApi", "endpoints", "getLocationData", "initiate", "unwrap", "sl", "prepXlsData", "obj", "for<PERSON>ach", "worksheet", "XLSX", "json_to_sheet", "workbook", "book_new", "book_append_sheet", "excelBuffer", "bookType", "blob", "Blob", "saveAs", "replace", "console", "SearchFilter", "resetPage", "keys", "newObj", "Loading", "DataTable", "fixedHeader", "highlightOnHover", "responsive", "pagination", "paginationServer", "paginationPerPage", "paginationTotalRows", "onChangePage", "onChangeRowsPerPage", "newPerPage", "paginationComponentOptions", "selectAllRowsItem", "selectAllRowsItemText", "sortServer", "onSort", "AddLocation", "EditLocation", "TableView", "Location"], "sourceRoot": ""}