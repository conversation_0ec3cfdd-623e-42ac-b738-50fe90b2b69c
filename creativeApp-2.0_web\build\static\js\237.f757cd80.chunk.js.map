{"version": 3, "file": "static/js/237.f757cd80.chunk.js", "mappings": "6MAGA,MAAMA,EAAUC,gEAyQhB,EAvQ6BC,IAAoD,IAAnD,UAAEC,EAAS,WAAEC,EAAU,mBAAEC,GAAoBH,GACtDI,EAAAA,EAAAA,MAAjB,MACOC,EAAaC,IAAkBC,EAAAA,EAAAA,UAAS,KACxCC,EAAOC,IAAYF,EAAAA,EAAAA,UAAS,KAC5BG,EAAoBC,IAAyBJ,EAAAA,EAAAA,UAAS,KACtDK,EAAcC,IAAmBN,EAAAA,EAAAA,UAAS,KAC1CO,EAAcC,IAAmBR,EAAAA,EAAAA,UAAS,KAC1CS,EAAOC,IAAYV,EAAAA,EAAAA,UAAS,KAC5BW,EAAgBC,IAAqBZ,EAAAA,EAAAA,UAAS,KAGrDa,EAAAA,EAAAA,YAAU,KACN,IAAKnB,EAAW,OAESoB,WACrB,MAAMC,EAAQC,aAAaC,QAAQ,SACnC,GAAKF,EAKL,IACI,MAAMG,QAAiBC,MAAM,GAAG5B,eAAsB,CAClD6B,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUN,IAC3B,eAAgB,sBAIxB,IAAKG,EAASI,GACV,MAAM,IAAIC,MAAM,+BAGpB,MAAMC,QAAaN,EAASO,OAC5B1B,EAAeyB,EAAK1B,YACxB,CAAE,MAAOW,GACLC,EAASD,EAAMiB,QACnB,MArBIhB,EAAS,iCAqBb,EAGJiB,EAAkB,GACnB,CAACjC,KAGJmB,EAAAA,EAAAA,YAAU,KACN,IAAKjB,IAAuBE,EAAY8B,OAAQ,OAElBd,WAC1B,MAAMC,EAAQC,aAAaC,QAAQ,SACnC,GAAKF,EAKL,IACI,MAAMG,QAAiBC,MAAM,GAAG5B,sBAA4BK,IAAsB,CAC9EwB,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUN,IAC3B,eAAgB,sBAIxB,IAAKG,EAASI,GACV,MAAM,IAAIC,MAAM,6CAGpB,MACMM,SADaX,EAASO,QACEI,iBAG9BrB,EAAgBqB,EAAiBC,MACjC1B,EAAsByB,EAAiBE,YACvCzB,EAAgBuB,EAAiBG,MAGjC,MAAMD,EAAajC,EAAYmC,MAAKC,GAAOA,EAAIJ,OAASD,EAAiBE,aAErEA,GAAcA,EAAW9B,OACzBC,EAAS6B,EAAW9B,QACf4B,EAAiBG,MAAQD,EAAW9B,MAAM2B,OAAS,GACpDtB,EAAgByB,EAAW9B,MAAM,GAAG6B,OAGxC5B,EAAS,GAEjB,CAAE,MAAOO,GACLC,EAASD,EAAMiB,QACnB,MAtCIhB,EAAS,iCAsCb,EAGJyB,EAAuB,GACxB,CAACvC,EAAoBE,IAyExB,OACIsC,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SACK5C,IACG0C,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mHAAkHD,UAC7HE,EAAAA,EAAAA,MAAA,OAAKD,UAAU,4FAA2FD,SAAA,EACtGF,EAAAA,EAAAA,KAAA,UAAQK,QATRC,KAChB/C,GAAW,EAAM,EAQ6B4C,UAAU,2DAA0DD,SAAC,UAGnGF,EAAAA,EAAAA,KAAA,MAAIG,UAAU,kCAAiCD,SAAC,4BAChDE,EAAAA,EAAAA,MAAA,QAAMG,SA1DL7B,UAGjB,GAFA8B,EAAMC,iBAED1C,GAAuBE,GAAiBE,EAA7C,CAKAG,EAAS,IACT,IACI,MAAMK,EAAQC,aAAaC,QAAQ,SACnC,IAAKF,EAED,YADAL,EAAS,oCAIb,MAAMQ,QAAiBC,MAAM,GAAG5B,sBAA4BK,IAAsB,CAC9EwB,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUN,IAC3B,eAAgB,oBAEpB+B,KAAMC,KAAKC,UAAU,CACjBjB,WAAY5B,EACZ6B,KAAM3B,EACNyB,KAAMvB,MAId,IAAKW,EAASI,GACV,MAAM,IAAIC,MAAM,uCAGpB,MAAM0B,QAAe/B,EAASO,OAC9Bb,EAAkB,sBAAsBqC,EAAOC,kBAAkBpB,+BAEjEqB,YAAW,KACPxD,GAAW,GACXiB,EAAkB,GAAG,GACtB,IACP,CAAE,MAAOH,GACLC,EAASD,EAAMiB,QACnB,CApCA,MAFIhB,EAAS,0BAsCb,EAgB6C4B,SAAA,EACzBE,EAAAA,EAAAA,MAAA,OAAKD,UAAU,OAAMD,SAAA,EACjBF,EAAAA,EAAAA,KAAA,SAAOgB,QAAQ,aAAab,UAAU,+CAA8CD,SAAC,uBAGrFE,EAAAA,EAAAA,MAAA,UACIa,GAAG,aACHC,MAAOnD,EACPoD,SAvFAC,IAC5B,MAAMC,EAAiBD,EAAEE,OAAOJ,MAIhC,GAHAlD,EAAsBqD,GACtBnD,EAAgB,IAEZmD,EAAgB,CAChB,MAAM1B,EAAajC,EAAYmC,MAAKC,GAAOA,EAAIJ,OAAS2B,IAEpD1B,GAAcA,EAAW9B,OAAS8B,EAAW9B,MAAM2B,OAAS,GAC5D1B,EAAS6B,EAAW9B,OACpBK,EAAgByB,EAAW9B,MAAM,GAAG6B,QAEpC5B,EAAS,IACTI,EAAgB,IAExB,MACIJ,EAAS,IACTI,EAAgB,GACpB,EAsE4BiC,UAAU,+HACVoB,UAAQ,EAAArB,SAAA,EAERF,EAAAA,EAAAA,KAAA,UAAQkB,MAAM,GAAEhB,SAAC,wBACO,IAAvBxC,EAAY8B,QACTQ,EAAAA,EAAAA,KAAA,UAAQwB,UAAQ,EAAAtB,SAAC,6BAEjBxC,EAAY+D,KAAK9B,IACbK,EAAAA,EAAAA,KAAA,UAA4BkB,MAAOvB,EAAWD,KAAKQ,SAC9CP,EAAWD,MADHC,EAAWsB,aAQxCb,EAAAA,EAAAA,MAAA,OAAKD,UAAU,OAAMD,SAAA,EACjBF,EAAAA,EAAAA,KAAA,SAAOgB,QAAQ,OAAOb,UAAU,+CAA8CD,SAAC,iBAG/EE,EAAAA,EAAAA,MAAA,UACIa,GAAG,OACHC,MAAOjD,EACPkD,SAAWC,GAAMlD,EAAgBkD,EAAEE,OAAOJ,OAC1Cf,UAAU,+HACVoB,UAAQ,EAAArB,SAAA,EAERF,EAAAA,EAAAA,KAAA,UAAQkB,MAAM,GAAEhB,SAAC,kBACC,IAAjBrC,EAAM2B,QACHQ,EAAAA,EAAAA,KAAA,UAAQwB,UAAQ,EAAAtB,SAAC,uBAEjBrC,EAAM4D,KAAK7B,IACPI,EAAAA,EAAAA,KAAA,UAAsBkB,MAAOtB,EAAKF,KAAKQ,SAClCN,EAAKF,MADGE,EAAKqB,aAQlCb,EAAAA,EAAAA,MAAA,OAAKD,UAAU,OAAMD,SAAA,EACjBF,EAAAA,EAAAA,KAAA,SAAOgB,QAAQ,eAAeb,UAAU,+CAA8CD,SAAC,mBAGvFF,EAAAA,EAAAA,KAAA,SACIiB,GAAG,eACHS,KAAK,OACLR,MAAO/C,EACPgD,SAAWC,GAAMhD,EAAgBgD,EAAEE,OAAOJ,OAC1Cf,UAAU,+HACVoB,UAAQ,QAIhBvB,EAAAA,EAAAA,KAAA,OAAKG,UAAU,OAAMD,UACjBF,EAAAA,EAAAA,KAAA,UACI0B,KAAK,SACLvB,UAAU,kEAAiED,SAC9E,+BAKJ7B,IAAS2B,EAAAA,EAAAA,KAAA,KAAGG,UAAU,uBAAsBD,SAAE7B,IAE9CE,IACD6B,EAAAA,EAAAA,MAAA,OAAKD,UAAU,qFAAoFD,SAAA,EAC/FF,EAAAA,EAAAA,KAAA,QAAMG,UAAU,qEAAoED,SAAC,kBAErFF,EAAAA,EAAAA,KAAA,KAAGG,UAAU,0CAAyCD,SAAE3B,gBAM7E,E,eCpQX,MAAMoD,EAAeA,IAEA,OADH/C,aAAaC,QAAQ,SAIjC1B,EAAUC,+DAsHhB,EApH6BwE,KACzB,MAAOC,EAAoBC,IAAyBlE,EAAAA,EAAAA,UAAS,KACtDS,EAAOC,IAAYV,EAAAA,EAAAA,UAAS,OAC5BmE,EAAcC,IAAmBpE,EAAAA,EAAAA,WAAS,IAC1CqE,EAA4BC,IAAiCtE,EAAAA,EAAAA,UAAS,OAW7Ea,EAAAA,EAAAA,YAAU,KAC0BC,WAC5B,IAAKiD,IAED,YADArD,EAAS,kCAIb,MAAMK,EAAQC,aAAaC,QAAQ,SAEnC,IACI,MAAMC,QAAiBC,MAAM,GAAG5B,wBAA+B,CAC3D6B,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUN,IAC3B,eAAgB,sBAIxB,IAAKG,EAASI,GACV,MAAM,IAAIC,MAAM,gCAAkCL,EAASqD,YAG/D,MAAM/C,QAAaN,EAASO,OAC5B+C,QAAQC,IAAI,oBAAqBjD,GAEjC0C,EAAsB1C,EAAKyC,mBAAmBJ,KAAIhC,IAAgB,CAC9DwB,GAAIxB,EAAiBwB,GACrBvB,KAAMD,EAAiBC,KACvBC,WAAYF,EAAiBE,WAC7BC,KAAMH,EAAiBG,KACvB0C,WAAY7C,EAAiB6C,WAC7BC,WAAY9C,EAAiB8C,eAErC,CAAE,MAAOlE,GACLC,EAASD,EAAMiB,QACnB,GAGJkD,EAAyB,GAC1B,IAqCH,OAAInE,GACO2B,EAAAA,EAAAA,KAAA,OAAKG,UAAU,eAAcD,SAAE7B,KAItC+B,EAAAA,EAAAA,MAAA,OAAAF,SAAA,EACIF,EAAAA,EAAAA,KAACyC,EAAAA,EAAY,CACTC,aAAcb,EACdc,YA7FQ,CAChB,CAAEC,MAAO,KAAMC,IAAK,MACpB,CAAED,MAAO,oBAAqBC,IAAK,QACnC,CAAED,MAAO,aAAcC,IAAK,cAC5B,CAAED,MAAO,OAAQC,IAAK,QACtB,CAAED,MAAO,aAAcC,IAAK,cAC5B,CAAED,MAAO,aAAcC,IAAK,eAwFpBC,SA3CSpE,UACjB,IAAKiD,IAED,YADArD,EAAS,kCAIb,MAAMK,EAAQC,aAAaC,QAAQ,SAEnC,IACI,MAAMC,QAAiBC,MAAM,GAAG5B,uBAA6B8D,IAAM,CAC/DjC,OAAQ,SACRC,QAAS,CACL,cAAiB,UAAUN,IAC3B,eAAgB,sBAIxB,IAAKG,EAASI,GACV,MAAM,IAAIC,MAAM,uCAAyCL,EAASqD,YAItEL,GAAsBiB,GAA0BA,EAAuBC,QAAOvD,GAAoBA,EAAiBwB,KAAOA,KAC9H,CAAE,MAAO5C,GACLC,EAASD,EAAMiB,QACnB,GAmBQ2D,OAfQhC,IAChBiB,EAA8BjB,GAC9Be,GAAgB,EAAK,EAcbA,gBAAiBA,EACjBkB,qBAAsBhB,IAEzBH,IACG/B,EAAAA,EAAAA,KAACmD,EAAoB,CACjB7F,UAAWyE,EACXxE,WAAYyE,EACZxE,mBAAoByE,MAG1B,ECzGd,EAZyBmB,KAErBpD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,yCAAwCD,UACrDE,EAAAA,EAAAA,MAACiD,EAAAA,EAAmB,CAAAnD,SAAA,EAClBF,EAAAA,EAAAA,KAACsD,EAAAA,EAAW,CAACC,UAAU,yBAAyBC,WAAW,2BAC3DxD,EAAAA,EAAAA,KAAC4B,EAAoB,KACrB5B,EAAAA,EAAAA,KAACyD,EAAAA,EAAe,Q", "sources": ["pages/training/training-category/EditTrainingCategory.jsx", "pages/training/training-category/TrainingCategoryList.jsx", "dashboard/settings/TrainingCategory.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL+'/';\r\n\r\nconst EditTrainingCategory = ({ isVisible, setVisible, trainingCategoryId }) => {\r\n    const navigate = useNavigate();\r\n    const [departments, setDepartments] = useState([]);\r\n    const [teams, setTeams] = useState([]);\r\n    const [selectedDepartment, setSelectedDepartment] = useState('');\r\n    const [selectedTeam, setSelectedTeam] = useState('');\r\n    const [categoryName, setCategoryName] = useState('');\r\n    const [error, setError] = useState('');\r\n    const [successMessage, setSuccessMessage] = useState('');\r\n\r\n    // Fetch Departments\r\n    useEffect(() => {\r\n        if (!isVisible) return;\r\n\r\n        const fetchDepartments = async () => {\r\n            const token = localStorage.getItem('token');\r\n            if (!token) {\r\n                setError('No authentication token found.');\r\n                return;\r\n            }\r\n\r\n            try {\r\n                const response = await fetch(`${API_URL}departments`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n                if (!response.ok) {\r\n                    throw new Error('Failed to fetch departments');\r\n                }\r\n\r\n                const data = await response.json();\r\n                setDepartments(data.departments);\r\n            } catch (error) {\r\n                setError(error.message);\r\n            }\r\n        };\r\n\r\n        fetchDepartments();\r\n    }, [isVisible]);\r\n\r\n    // Fetch the Training Category Details to Edit\r\n    useEffect(() => {\r\n        if (!trainingCategoryId || !departments.length) return;\r\n\r\n        const fetchTrainingCategory = async () => {\r\n            const token = localStorage.getItem('token');\r\n            if (!token) {\r\n                setError('No authentication token found.');\r\n                return;\r\n            }\r\n\r\n            try {\r\n                const response = await fetch(`${API_URL}training-category/${trainingCategoryId}`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n                if (!response.ok) {\r\n                    throw new Error('Failed to fetch training category details');\r\n                }\r\n\r\n                const data = await response.json();\r\n                const trainingCategory = data.trainingCategory;\r\n\r\n                // Set the values from the fetched training category\r\n                setCategoryName(trainingCategory.name);\r\n                setSelectedDepartment(trainingCategory.department);\r\n                setSelectedTeam(trainingCategory.team);\r\n\r\n                // Fetch teams for the selected department\r\n                const department = departments.find(dep => dep.name === trainingCategory.department);\r\n\r\n                if (department && department.teams) {\r\n                    setTeams(department.teams);\r\n                    if (!trainingCategory.team && department.teams.length > 0) {\r\n                        setSelectedTeam(department.teams[0].name);\r\n                    }\r\n                } else {\r\n                    setTeams([]);\r\n                }\r\n            } catch (error) {\r\n                setError(error.message);\r\n            }\r\n        };\r\n\r\n        fetchTrainingCategory();\r\n    }, [trainingCategoryId, departments]);\r\n\r\n    // Handle Department Change and Fetch Teams\r\n    const handleDepartmentChange = (e) => {\r\n        const departmentName = e.target.value;\r\n        setSelectedDepartment(departmentName);\r\n        setSelectedTeam('');\r\n\r\n        if (departmentName) {\r\n            const department = departments.find(dep => dep.name === departmentName);\r\n\r\n            if (department && department.teams && department.teams.length > 0) {\r\n                setTeams(department.teams);\r\n                setSelectedTeam(department.teams[0].name);\r\n            } else {\r\n                setTeams([]);\r\n                setSelectedTeam('');\r\n            }\r\n        } else {\r\n            setTeams([]);\r\n            setSelectedTeam('');\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (event) => {\r\n        event.preventDefault();\r\n\r\n        if (!selectedDepartment || !selectedTeam || !categoryName) {\r\n            setError('Please fill all fields.');\r\n            return;\r\n        }\r\n\r\n        setError('');\r\n        try {\r\n            const token = localStorage.getItem('token');\r\n            if (!token) {\r\n                setError('Authentication token is missing.');\r\n                return;\r\n            }\r\n\r\n            const response = await fetch(`${API_URL}training-category/${trainingCategoryId}`, {\r\n                method: 'PUT',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n                body: JSON.stringify({\r\n                    department: selectedDepartment,\r\n                    team: selectedTeam,\r\n                    name: categoryName,\r\n                }),\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Failed to update training category.');\r\n            }\r\n\r\n            const result = await response.json();\r\n            setSuccessMessage(`Training Category \"${result.training_category.name}\" updated successfully!`);\r\n            // Close the modal after a short delay\r\n            setTimeout(() => {\r\n                setVisible(false);\r\n                setSuccessMessage(''); // Clear the success message\r\n            }, 2000);\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n\r\n    const handleClose = () => {\r\n        setVisible(false);\r\n    };\r\n\r\n    return (\r\n        <>\r\n            {isVisible && (\r\n                <div className=\"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\">\r\n                    <div className=\"bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative overflow-y-auto h-[80vh] mt-10\">\r\n                        <button onClick={handleClose} className=\"absolute top-2 right-2 text-gray-400 hover:text-gray-900\">\r\n                            &times;\r\n                        </button>\r\n                        <h4 className=\"text-xl font-semibold mb-4 py-4\">Edit Training Category</h4>\r\n                        <form onSubmit={handleSubmit}>\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"department\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Select Department\r\n                                </label>\r\n                                <select\r\n                                    id=\"department\"\r\n                                    value={selectedDepartment}\r\n                                    onChange={handleDepartmentChange}\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                    required\r\n                                >\r\n                                    <option value=\"\">Select a Department</option>\r\n                                    {departments.length === 0 ? (\r\n                                        <option disabled>No departments available</option>\r\n                                    ) : (\r\n                                        departments.map((department) => (\r\n                                            <option key={department.id} value={department.name}>\r\n                                                {department.name}\r\n                                            </option>\r\n                                        ))\r\n                                    )}\r\n                                </select>\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"team\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Select Team\r\n                                </label>\r\n                                <select\r\n                                    id=\"team\"\r\n                                    value={selectedTeam}\r\n                                    onChange={(e) => setSelectedTeam(e.target.value)}\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                    required\r\n                                >\r\n                                    <option value=\"\">Select a Team</option>\r\n                                    {teams.length === 0 ? (\r\n                                        <option disabled>No teams available</option>\r\n                                    ) : (\r\n                                        teams.map((team) => (\r\n                                            <option key={team.id} value={team.name}>\r\n                                                {team.name}\r\n                                            </option>\r\n                                        ))\r\n                                    )}\r\n                                </select>\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"categoryName\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Category Name\r\n                                </label>\r\n                                <input\r\n                                    id=\"categoryName\"\r\n                                    type=\"text\"\r\n                                    value={categoryName}\r\n                                    onChange={(e) => setCategoryName(e.target.value)}\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                    required\r\n                                />\r\n                            </div>\r\n\r\n                            <div className=\"py-4\">\r\n                                <button\r\n                                    type=\"submit\"\r\n                                    className=\"w-full bg-primary hover:bg-secondary text-white rounded-md py-3\"\r\n                                >\r\n                                    Update Training Category\r\n                                </button>\r\n                            </div>\r\n\r\n                            {error && <p className=\"text-red-500 text-sm\">{error}</p>}\r\n\r\n                            {successMessage && \r\n                            <div className='bg-[#DAF8E6] p-6 rounded-lg border-l-4 border-green-500 flex flex-row items-center'>\r\n                                <span className=\"material-symbols-rounded bg-green-500 text-gray-700 p-1 rounded-md\">check_circle</span>\r\n                                {/* Success message display */}\r\n                                <p className=\"text-green-500 text-xl font-medium pl-6\">{successMessage}</p>\r\n                            </div>}\r\n                        </form>\r\n                    </div>\r\n                </div>\r\n            )}\r\n        </>\r\n    );\r\n};\r\n\r\nexport default EditTrainingCategory;\r\n", "import React, { useEffect, useState } from 'react';\r\nimport EditTrainingCategory from './EditTrainingCategory';\r\nimport TableContent from '../../../common/table/TableContent';\r\n\r\nconst isTokenValid = () => {\r\n    const token = localStorage.getItem('token');\r\n    return token !== null;\r\n};\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst TrainingCategoryList = () => {\r\n    const [trainingCategories, setTrainingCategories] = useState([]);\r\n    const [error, setError] = useState(null);\r\n    const [modalVisible, setModalVisible] = useState(false);\r\n    const [selectedTrainingCategoryId, setSelectedTrainingCategoryId] = useState(null);\r\n\r\n    const columnNames = [\r\n        { label: \"SL\", key: \"id\" },\r\n        { label: \"Product Type Name\", key: \"name\" },\r\n        { label: \"Department\", key: \"department\" },\r\n        { label: \"Team\", key: \"team\" },\r\n        { label: \"Created By\", key: \"created_by\" },\r\n        { label: \"Updated By\", key: \"updated_by\" },\r\n    ];\r\n\r\n    useEffect(() => {\r\n        const fetchTrainingCategories = async () => {\r\n            if (!isTokenValid()) {\r\n                setError('No authentication token found.');\r\n                return;\r\n            }\r\n\r\n            const token = localStorage.getItem('token');\r\n\r\n            try {\r\n                const response = await fetch(`${API_URL}/training-categories`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n                if (!response.ok) {\r\n                    throw new Error('Network response was not ok: ' + response.statusText);\r\n                }\r\n\r\n                const data = await response.json();\r\n                console.log('Training Category', data);\r\n\r\n                setTrainingCategories(data.trainingCategories.map(trainingCategory => ({\r\n                    id: trainingCategory.id,\r\n                    name: trainingCategory.name,\r\n                    department: trainingCategory.department,\r\n                    team: trainingCategory.team,\r\n                    created_by: trainingCategory.created_by,\r\n                    updated_by: trainingCategory.updated_by,\r\n                })));\r\n            } catch (error) {\r\n                setError(error.message);\r\n            }\r\n        };\r\n\r\n        fetchTrainingCategories();\r\n    }, []);\r\n\r\n    // Handle Delete\r\n    const handleDelete = async (id) => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n\r\n        const token = localStorage.getItem('token');\r\n\r\n        try {\r\n            const response = await fetch(`${API_URL}/training-category/${id}`, {\r\n                method: 'DELETE',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Failed to delete training category: ' + response.statusText);\r\n            }\r\n\r\n            // Update the training categories list after deletion\r\n            setTrainingCategories(prevTrainingCategories => prevTrainingCategories.filter(trainingCategory => trainingCategory.id !== id));\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n\r\n    // Handle Edit\r\n    const handleEdit = (id) => {\r\n        setSelectedTrainingCategoryId(id);\r\n        setModalVisible(true);\r\n    };\r\n\r\n    if (error) {\r\n        return <div className=\"text-red-500\">{error}</div>;\r\n    }\r\n\r\n    return (\r\n        <div>\r\n            <TableContent\r\n                tableContent={trainingCategories}\r\n                columnNames={columnNames}\r\n                onDelete={handleDelete}\r\n                onEdit={handleEdit}\r\n                setModalVisible={setModalVisible}\r\n                setSelectedServiceId={setSelectedTrainingCategoryId}\r\n            />\r\n            {modalVisible && (\r\n                <EditTrainingCategory\r\n                    isVisible={modalVisible}\r\n                    setVisible={setModalVisible}\r\n                    trainingCategoryId={selectedTrainingCategoryId}\r\n                />\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default TrainingCategoryList;\r\n", "import React from 'react';\r\nimport TableLayoutWrapper2 from '../../common/table/TableLayoutWrapper2';\r\nimport TableHeader from '../../common/table/TableHeader';\r\nimport TablePagination from '../../common/table/TablePagination';\r\nimport TrainingCategoryList from '../../pages/training/training-category/TrainingCategoryList';\r\n\r\nconst TrainingCategory = () => {\r\n  return (\r\n    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>\r\n      <TableLayoutWrapper2>\r\n        <TableHeader routeName=\"/add-training-category\" buttonName=\"Add Training Category\" />\r\n        <TrainingCategoryList />\r\n        <TablePagination />\r\n      </TableLayoutWrapper2>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TrainingCategory;\r\n"], "names": ["API_URL", "process", "_ref", "isVisible", "setVisible", "trainingCategoryId", "useNavigate", "departments", "setDepartments", "useState", "teams", "setTeams", "selectedDepartment", "setSelectedDepartment", "selectedTeam", "setSelectedTeam", "categoryName", "setCategoryName", "error", "setError", "successMessage", "setSuccessMessage", "useEffect", "async", "token", "localStorage", "getItem", "response", "fetch", "method", "headers", "ok", "Error", "data", "json", "message", "fetchDepartments", "length", "trainingCategory", "name", "department", "team", "find", "dep", "fetchTrainingCategory", "_jsx", "_Fragment", "children", "className", "_jsxs", "onClick", "handleClose", "onSubmit", "event", "preventDefault", "body", "JSON", "stringify", "result", "training_category", "setTimeout", "htmlFor", "id", "value", "onChange", "e", "departmentName", "target", "required", "disabled", "map", "type", "isTokenValid", "TrainingCategoryList", "trainingCategories", "setTrainingCategories", "modalVisible", "setModalVisible", "selectedTrainingCategoryId", "setSelectedTrainingCategoryId", "statusText", "console", "log", "created_by", "updated_by", "fetchTrainingCategories", "TableContent", "tableContent", "columnNames", "label", "key", "onDelete", "prevTrainingCategories", "filter", "onEdit", "setSelectedServiceId", "EditTrainingCategory", "TrainingCategory", "TableLayoutWrapper2", "TableHeader", "routeName", "buttonName", "TablePagination"], "sourceRoot": ""}