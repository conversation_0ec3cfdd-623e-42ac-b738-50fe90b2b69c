{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\routes.js\";\nimport React from 'react';\nimport { createBrowserRouter, Navigate } from 'react-router-dom';\nimport MainLayout from './dashboard/MainLayout';\nimport Login from './common/Login';\nimport Dashboard from './dashboard/Dashboard';\nimport UserList from './pages/team-member/TeamMemberList';\nimport Teams from './dashboard/settings/Teams';\nimport AddLocation from './pages/location/AddLocation';\nimport AddBranch from './pages/branch/AddBranch';\nimport Department from './dashboard/settings/Department';\nimport AddTeam from './pages/team/AddTeam';\nimport AddMember from './pages/team-member/AddMember';\nimport Settings from './dashboard/Settings';\nimport AddRole from './pages/role/AddRole';\nimport AddDepartment from './pages/department/AddDepartment';\nimport AddBillingStatus from './pages/billing-status/AddBillingStatus';\nimport AddResourceStatus from './pages/resource-status/AddResourceStatus';\nimport AddResourceType from './pages/resource-type/AddResourceType';\nimport AddDesignation from './pages/designation/AddDesignation';\nimport MemberIndex from './dashboard/MemberIndex';\nimport AddBlood from './pages/blood/AddBlood';\nimport AddAvailableStatus from './pages/available-status/AddAvailableStatus';\nimport AddContactType from './pages/contact-type/AddContactType';\nimport AddMemberStatus from './pages/member-status/AddMemberStatus';\nimport AddOnsiteStatus from './pages/onsite-status/AddOnsiteStatus';\nimport MemberOnboard from './dashboard/MemberOnboard';\nimport AddSchedule from './pages/schedule/AddSchedule';\nimport ProtectedRoute from './route/ProtectedRoute';\nimport TeamContacts from './dashboard/TeamContacts';\n\n// Abdur Rahman\nimport Holiday from './dashboard/Holiday';\nimport QuickAccess from './dashboard/QuickAccessHubs';\nimport Training from './dashboard/Training';\nimport AddHolidayCalender from './pages/holiday-calender/AddHolidayCalender';\nimport AddTrainingCategory from './pages/training/training-category/AddTrainingCategory';\nimport AddTrainingTopic from './pages/training/training-topic/AddTrainingTopic';\nimport AddQuickAccessHub from './pages/quickaccesshub/AddQuickAccessHub';\nimport AddTraining from './pages/training/AddTraining';\nimport TaskDetails from './dashboard/task-details/TaskDetails';\nimport Formation from './dashboard/task-details/Formation';\nimport AddTaskRecord from './pages/task-details/task-record/AddTaskRecord';\nimport AddTimeCard from './pages/time-card/AddTimeCard';\nimport TimeCard from './dashboard/time-card/TimeCard';\nimport AddTeamShiftPlan from './pages/team-shift-plan/AddTeamShiftPlan';\nimport TeamShiftPlan from './dashboard/TeamShiftPlan';\nimport Profile from './dashboard/Profile';\nimport WorldTime from './pages/world-time/WorldTime';\nimport TimeZoneConvert from './pages/world-time/TimeZoneConvert';\nimport AttendanceFormation from './pages/attendance/AttendanceFormation/AttendanceFormationList';\nimport Attendance from './pages/attendance/Attendance/Attendance';\nimport SchedulePlaners from './pages/schedule-planers/SchedulePlaners';\n\n// Imran Ahmed\nimport Todo from './dashboard/Todo';\nimport AddTodo from './pages/todo/commonTodo/AddTodo';\nimport Abouttheapp from './dashboard/Abouttheapp';\nimport AddAboutTheApp from './pages/about-the-app/AddAboutTheApp';\nimport AddChangeLog from './pages/change-log/AddChangeLog';\nimport Changelog from './dashboard/Changelog';\nimport AddReporter from './pages/time-card/reporter/AddReporter';\nimport Reporter from './dashboard/time-card/Reporter';\nimport Appsupport from './dashboard/Appsupport';\nimport AddAppsupport from './pages/app-support/AddAppsupport';\nimport Givefeedback from './dashboard/Givefeedback';\nimport Reportproblem from './dashboard/Reportproblem';\nimport AddGiveFeedback from './pages/give-feedback/AddGiveFeedback';\nimport AddReportProblem from './pages/report-problem/AddReportProblem';\nimport Teamsnapshot from './dashboard/Teamsnapshot';\nimport Notice from './dashboard/NoticeBoard';\nimport NoticeBoard from './dashboard/NoticeBoard';\nimport AddNoticeBoardCategory from './pages/settings/noticeboardcategory/AddNoticeBoardCategory';\nimport AddNotice from './pages/notice/AddNotice';\nimport HolidayCalendarGoogleList from './pages/holiday-calender/HolidayCalenderGoogleList';\n//import OfficeSeatPlan from './pages/holiday-calender/OfficeSeatPlan';\nimport OfficeSeatPlan from './pages/seat-plan/OfficeSeatPlan';\nimport NotFound from './common/utility/NotFound';\nimport Unauthorized from './common/utility/UnAuthorized';\nimport ResetPassword from './common/login/ResetPassword';\nimport UpdatePassword from './common/login/UpdatePassword';\nimport Welcome from './dashboard/Welcome';\nimport Creativetools from './dashboard/Creativetools';\n\n// Define your routes\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreativeRoutes = [{\n  path: '/login',\n  element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 14\n  }, this)\n},\n// {\n//   path: '*',\n//   element: <NotFound />,\n// },\n{\n  path: 'reset-password',\n  element: /*#__PURE__*/_jsxDEV(ResetPassword, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 14\n  }, this)\n}, {\n  path: '/password/reset/:token',\n  element: /*#__PURE__*/_jsxDEV(UpdatePassword, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 14\n  }, this)\n}, {\n  path: 'world-time-share',\n  element: /*#__PURE__*/_jsxDEV(WorldTime, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 14\n  }, this)\n}, {\n  path: 'time-zone-convert-share',\n  element: /*#__PURE__*/_jsxDEV(TimeZoneConvert, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 14\n  }, this)\n}, {\n  path: '/',\n  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n    requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],\n    children: /*#__PURE__*/_jsxDEV(MainLayout, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 146\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 14\n  }, this),\n  children: [{\n    path: '/',\n    element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 18\n    }, this) // Default to Dashboard\n  }, {\n    path: 'unauthorized',\n    element: /*#__PURE__*/_jsxDEV(Unauthorized, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'member-onboard',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin'],\n      children: /*#__PURE__*/_jsxDEV(MemberOnboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 75\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'world-time',\n    element: /*#__PURE__*/_jsxDEV(WorldTime, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'time-zone-convert',\n    element: /*#__PURE__*/_jsxDEV(TimeZoneConvert, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'time-zone-convert',\n    element: /*#__PURE__*/_jsxDEV(TimeZoneConvert, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'attendance',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],\n      children: /*#__PURE__*/_jsxDEV(Attendance, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 150\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'attendance-formation',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AttendanceFormation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'team-members',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],\n      children: /*#__PURE__*/_jsxDEV(TeamContacts, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 150\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'member-index',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin'],\n      children: /*#__PURE__*/_jsxDEV(MemberIndex, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 75\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'profile',\n    element: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-member',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin'],\n      children: /*#__PURE__*/_jsxDEV(AddMember, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 75\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'teams',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(Teams, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-team',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddTeam, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-role',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],\n      children: /*#__PURE__*/_jsxDEV(AddRole, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 150\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'departments',\n    element: /*#__PURE__*/_jsxDEV(Department, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 18\n    }, this),\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(Department, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-department',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddDepartment, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-billing-status',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddBillingStatus, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-resource-status',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddResourceStatus, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-resource-type',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddResourceType, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-designation',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddDesignation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-location',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddLocation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-branch',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddBranch, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-schedule',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddSchedule, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-available-status',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddAvailableStatus, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-contact-type',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddContactType, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-blood',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddBlood, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-member-status',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddMemberStatus, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-onsite-status',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddOnsiteStatus, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'settings',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin'],\n      children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 75\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 18\n    }, this)\n  },\n  // Task Details\n  {\n    path: 'add-task',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddTaskRecord, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 135\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'task-records',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead'],\n      children: /*#__PURE__*/_jsxDEV(TaskDetails, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 135\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'formation',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(Formation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-reporter',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddReporter, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 135\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'reporters',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],\n      children: /*#__PURE__*/_jsxDEV(Reporter, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 150\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 18\n    }, this)\n  },\n  // Routes for All Users\n  {\n    path: 'add-time',\n    element: /*#__PURE__*/_jsxDEV(AddTimeCard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'time-cards',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],\n      children: /*#__PURE__*/_jsxDEV(TimeCard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 150\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 18\n    }, this)\n  },\n  // Abdur Rahman\n  {\n    path: 'holidaycalenders',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],\n      children: /*#__PURE__*/_jsxDEV(Holiday, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 150\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-holiday',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddHolidayCalender, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'quickaccesshub',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],\n      children: /*#__PURE__*/_jsxDEV(QuickAccess, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 150\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-hub',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddQuickAccessHub, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'training',\n    element: /*#__PURE__*/_jsxDEV(Training, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-training',\n    element: /*#__PURE__*/_jsxDEV(AddTraining, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-training-category',\n    element: /*#__PURE__*/_jsxDEV(AddTrainingCategory, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-trainingtopic',\n    element: /*#__PURE__*/_jsxDEV(AddTrainingTopic, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-team-shift-plan',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddTeamShiftPlan, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'shift-plan',\n    element: /*#__PURE__*/_jsxDEV(TeamShiftPlan, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'schedule-planners',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'shift-lead'],\n      children: /*#__PURE__*/_jsxDEV(SchedulePlaners, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 120\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'achieve',\n    element: /*#__PURE__*/_jsxDEV(Todo, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'achieve/add-todo',\n    element: /*#__PURE__*/_jsxDEV(AddTodo, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'about-the-app',\n    element: /*#__PURE__*/_jsxDEV(Abouttheapp, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-about-the-app',\n    element: /*#__PURE__*/_jsxDEV(AddAboutTheApp, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'change-log',\n    element: /*#__PURE__*/_jsxDEV(Changelog, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-change-log',\n    element: /*#__PURE__*/_jsxDEV(AddChangeLog, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'app-support',\n    element: /*#__PURE__*/_jsxDEV(Appsupport, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-app-support',\n    element: /*#__PURE__*/_jsxDEV(AddAppsupport, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'give-feedback',\n    element: /*#__PURE__*/_jsxDEV(Givefeedback, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-feedback',\n    element: /*#__PURE__*/_jsxDEV(AddGiveFeedback, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'report-problem',\n    element: /*#__PURE__*/_jsxDEV(Reportproblem, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-report-problem',\n    element: /*#__PURE__*/_jsxDEV(AddReportProblem, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'team-snapshot',\n    element: /*#__PURE__*/_jsxDEV(Teamsnapshot, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'noticeboard',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead'],\n      children: /*#__PURE__*/_jsxDEV(NoticeBoard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 135\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-notice-category',\n    element: /*#__PURE__*/_jsxDEV(AddNoticeBoardCategory, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-notice',\n    element: /*#__PURE__*/_jsxDEV(AddNotice, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'events',\n    element: /*#__PURE__*/_jsxDEV(HolidayCalendarGoogleList, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'seat-plan',\n    element: /*#__PURE__*/_jsxDEV(OfficeSeatPlan, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'welcome',\n    element: /*#__PURE__*/_jsxDEV(Welcome, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'passwordmanager',\n    element: /*#__PURE__*/_jsxDEV(Welcome, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'creative-tools',\n    element: /*#__PURE__*/_jsxDEV(Creativetools, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 18\n    }, this)\n  }]\n}, {\n  path: 'users',\n  element: /*#__PURE__*/_jsxDEV(UserList, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 421,\n    columnNumber: 14\n  }, this)\n}, {\n  path: '*',\n  element: /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/login\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 425,\n    columnNumber: 14\n  }, this)\n}];\n\n// Create the router\nconst router = createBrowserRouter(CreativeRoutes);\nexport default router;", "map": {"version": 3, "names": ["React", "createBrowserRouter", "Navigate", "MainLayout", "<PERSON><PERSON>", "Dashboard", "UserList", "Teams", "AddLocation", "AddBranch", "Department", "AddTeam", "AddMember", "Settings", "AddRole", "AddDepartment", "AddBillingStatus", "AddResourceStatus", "AddResourceType", "AddDesignation", "MemberIndex", "AddBlood", "AddAvailableStatus", "AddContactType", "AddMemberStatus", "AddOnsiteStatus", "MemberOnboard", "AddSchedule", "ProtectedRoute", "TeamContacts", "Holiday", "QuickAccess", "Training", "AddHolidayCalender", "AddTrainingCategory", "AddTrainingTopic", "AddQuickAccessHub", "AddTraining", "TaskDetails", "Formation", "AddTaskRecord", "AddTimeCard", "TimeCard", "AddTeamShiftPlan", "TeamShiftPlan", "Profile", "WorldTime", "TimeZoneConvert", "AttendanceFormation", "Attendance", "SchedulePlaners", "Todo", "AddTodo", "Abouttheapp", "AddAboutTheApp", "AddChangeLog", "Changelog", "AddReport<PERSON>", "Reporter", "Appsupport", "AddAppsupport", "Givefeedback", "Reportproblem", "AddGiveFeedback", "AddReportProblem", "Teamsnapshot", "Notice", "NoticeBoard", "AddNoticeBoardCategory", "AddNotice", "HolidayCalendarGoogleList", "OfficeSeatPlan", "NotFound", "Unauthorized", "ResetPassword", "UpdatePassword", "Welcome", "Creativetools", "jsxDEV", "_jsxDEV", "CreativeRoutes", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "requiredRoles", "children", "to", "router"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/routes.js"], "sourcesContent": ["import React from 'react';\r\nimport { createBrowserRouter, Navigate } from 'react-router-dom';\r\nimport MainLayout from './dashboard/MainLayout';\r\n\r\nimport Login from './common/Login';\r\nimport Dashboard from './dashboard/Dashboard';\r\nimport UserList from './pages/team-member/TeamMemberList';\r\nimport Teams from './dashboard/settings/Teams';\r\nimport AddLocation from './pages/location/AddLocation';\r\nimport AddBranch from './pages/branch/AddBranch';\r\nimport Department from './dashboard/settings/Department';\r\nimport AddTeam from './pages/team/AddTeam';\r\nimport AddMember from './pages/team-member/AddMember';\r\nimport Settings from './dashboard/Settings';\r\nimport AddRole from './pages/role/AddRole';\r\nimport AddDepartment from './pages/department/AddDepartment';\r\nimport AddBillingStatus from './pages/billing-status/AddBillingStatus';\r\nimport AddResourceStatus from './pages/resource-status/AddResourceStatus';\r\nimport AddResourceType from './pages/resource-type/AddResourceType';\r\nimport AddDesignation from './pages/designation/AddDesignation';\r\nimport MemberIndex from './dashboard/MemberIndex';\r\nimport AddBlood from './pages/blood/AddBlood';\r\nimport AddAvailableStatus from './pages/available-status/AddAvailableStatus';\r\nimport AddContactType from './pages/contact-type/AddContactType';\r\nimport AddMemberStatus from './pages/member-status/AddMemberStatus';\r\nimport AddOnsiteStatus from './pages/onsite-status/AddOnsiteStatus';\r\nimport MemberOnboard from './dashboard/MemberOnboard';\r\nimport AddSchedule from './pages/schedule/AddSchedule';\r\nimport ProtectedRoute from './route/ProtectedRoute';\r\nimport TeamContacts from './dashboard/TeamContacts';\r\n\r\n// Abdur Rahman\r\nimport Holiday from './dashboard/Holiday';\r\nimport QuickAccess from './dashboard/QuickAccessHubs';\r\nimport Training from './dashboard/Training'\r\nimport AddHolidayCalender from './pages/holiday-calender/AddHolidayCalender';\r\nimport AddTrainingCategory from './pages/training/training-category/AddTrainingCategory';\r\nimport AddTrainingTopic from './pages/training/training-topic/AddTrainingTopic';\r\nimport AddQuickAccessHub from './pages/quickaccesshub/AddQuickAccessHub';\r\nimport AddTraining from './pages/training/AddTraining';\r\nimport TaskDetails from './dashboard/task-details/TaskDetails';\r\nimport Formation from './dashboard/task-details/Formation';\r\nimport AddTaskRecord from './pages/task-details/task-record/AddTaskRecord';\r\nimport AddTimeCard from './pages/time-card/AddTimeCard';\r\nimport TimeCard from './dashboard/time-card/TimeCard';\r\nimport AddTeamShiftPlan from './pages/team-shift-plan/AddTeamShiftPlan';\r\nimport TeamShiftPlan from './dashboard/TeamShiftPlan';\r\nimport Profile from './dashboard/Profile';\r\nimport WorldTime from './pages/world-time/WorldTime';\r\nimport TimeZoneConvert from './pages/world-time/TimeZoneConvert';\r\nimport AttendanceFormation from './pages/attendance/AttendanceFormation/AttendanceFormationList';\r\nimport Attendance from './pages/attendance/Attendance/Attendance';\r\nimport SchedulePlaners from './pages/schedule-planers/SchedulePlaners';\r\n\r\n// Imran Ahmed\r\nimport Todo from './dashboard/Todo';\r\nimport AddTodo from './pages/todo/commonTodo/AddTodo';\r\nimport Abouttheapp from './dashboard/Abouttheapp';\r\nimport AddAboutTheApp from './pages/about-the-app/AddAboutTheApp';\r\nimport AddChangeLog from './pages/change-log/AddChangeLog';\r\nimport Changelog from './dashboard/Changelog';\r\nimport AddReporter from './pages/time-card/reporter/AddReporter';\r\nimport Reporter from './dashboard/time-card/Reporter';\r\nimport Appsupport from './dashboard/Appsupport';\r\nimport AddAppsupport from './pages/app-support/AddAppsupport';\r\nimport Givefeedback from './dashboard/Givefeedback';\r\nimport Reportproblem from './dashboard/Reportproblem';\r\nimport AddGiveFeedback from './pages/give-feedback/AddGiveFeedback';\r\nimport AddReportProblem from './pages/report-problem/AddReportProblem';\r\nimport Teamsnapshot from './dashboard/Teamsnapshot';\r\nimport Notice from './dashboard/NoticeBoard';\r\nimport NoticeBoard from './dashboard/NoticeBoard';\r\nimport AddNoticeBoardCategory from './pages/settings/noticeboardcategory/AddNoticeBoardCategory';\r\nimport AddNotice from './pages/notice/AddNotice';\r\nimport HolidayCalendarGoogleList from './pages/holiday-calender/HolidayCalenderGoogleList';\r\n//import OfficeSeatPlan from './pages/holiday-calender/OfficeSeatPlan';\r\nimport OfficeSeatPlan from './pages/seat-plan/OfficeSeatPlan';\r\nimport NotFound from './common/utility/NotFound';\r\nimport Unauthorized from './common/utility/UnAuthorized';\r\nimport ResetPassword from './common/login/ResetPassword';\r\nimport UpdatePassword from './common/login/UpdatePassword';\r\nimport Welcome from './dashboard/Welcome';\r\nimport Creativetools from './dashboard/Creativetools';\r\n\r\n\r\n// Define your routes\r\nconst CreativeRoutes = [\r\n  {\r\n    path: '/login',\r\n    element: <Login />,\r\n  },\r\n  // {\r\n  //   path: '*',\r\n  //   element: <NotFound />,\r\n  // },\r\n  {\r\n    path: 'reset-password',\r\n    element: <ResetPassword />,\r\n  },\r\n  {\r\n    path: '/password/reset/:token',\r\n    element: <UpdatePassword />,\r\n  },\r\n  {\r\n    path: 'world-time-share',\r\n    element: <WorldTime />,\r\n  },\r\n  {\r\n    path: 'time-zone-convert-share',\r\n    element: <TimeZoneConvert />,\r\n  },\r\n  {\r\n    path: '/',\r\n    element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><MainLayout /></ProtectedRoute>,\r\n    children: [\r\n      {\r\n        path: '/',\r\n        element: <Dashboard />, // Default to Dashboard\r\n      },\r\n      {\r\n        path: 'unauthorized',\r\n        element: <Unauthorized />,\r\n      },\r\n      {\r\n        path: 'member-onboard',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin']}><MemberOnboard /></ProtectedRoute>,\r\n        \r\n      },\r\n      \r\n      {\r\n        path: 'world-time',\r\n        element: <WorldTime />,\r\n      },\r\n\r\n      {\r\n        path: 'time-zone-convert',\r\n        element: <TimeZoneConvert />,\r\n      },\r\n      {\r\n        path: 'time-zone-convert',\r\n        element: <TimeZoneConvert />,\r\n      },\r\n      {\r\n        path: 'attendance',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><Attendance/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'attendance-formation',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AttendanceFormation/></ProtectedRoute>,\r\n      },\r\n\r\n      {\r\n        path: 'team-members',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><TeamContacts/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'member-index',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin']}><MemberIndex /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'profile',\r\n        element: <Profile />,\r\n      },\r\n      {\r\n        path: 'add-member',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin']}><AddMember/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'teams',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><Teams/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-team',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddTeam/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-role',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><AddRole/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'departments',\r\n        element: <Department />,\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><Department/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-department',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddDepartment/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-billing-status',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddBillingStatus/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-resource-status',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddResourceStatus/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-resource-type',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddResourceType/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-designation',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddDesignation/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-location',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddLocation/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-branch',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddBranch/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-schedule',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddSchedule/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-available-status',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddAvailableStatus/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-contact-type',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddContactType/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-blood',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddBlood/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-member-status',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddMemberStatus/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-onsite-status',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddOnsiteStatus/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'settings',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin']}><Settings /></ProtectedRoute>,\r\n      },\r\n\r\n      // Task Details\r\n      {\r\n        path: 'add-task',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead']}><AddTaskRecord /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'task-records',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead']}><TaskDetails /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'formation',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><Formation /></ProtectedRoute>,\r\n      },\r\n\r\n      {\r\n        path: 'add-reporter',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead']}><AddReporter /></ProtectedRoute>,\r\n      },\r\n\r\n      {\r\n        path: 'reporters',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><Reporter /></ProtectedRoute>,\r\n      },\r\n\r\n      // Routes for All Users\r\n      {\r\n        path: 'add-time',\r\n        element: <AddTimeCard />,\r\n      },\r\n\r\n      {\r\n        path: 'time-cards',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><TimeCard /></ProtectedRoute>,\r\n      },\r\n\r\n      // Abdur Rahman\r\n      {\r\n        path: 'holidaycalenders',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><Holiday /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-holiday',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddHolidayCalender /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'quickaccesshub',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><QuickAccess/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-hub',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddQuickAccessHub /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'training',\r\n        element: <Training />,\r\n      },\r\n      {\r\n        path: 'add-training',\r\n        element: <AddTraining/>,\r\n      },\r\n      {\r\n        path: 'add-training-category',\r\n        element: <AddTrainingCategory />,\r\n      },\r\n\r\n      {\r\n        path: 'add-trainingtopic',\r\n        element: <AddTrainingTopic />,\r\n      },\r\n\r\n      {\r\n        path: 'add-team-shift-plan',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddTeamShiftPlan /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'shift-plan',\r\n        element: <TeamShiftPlan/>,\r\n      },\r\n      \r\n      {\r\n        path: 'schedule-planners',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'shift-lead']}><SchedulePlaners /></ProtectedRoute>,\r\n      },\r\n\r\n      {\r\n        path: 'achieve',\r\n        element: <Todo />\r\n      },\r\n      {\r\n        path: 'achieve/add-todo',\r\n        element: <AddTodo />\r\n      },\r\n      {\r\n        path: 'about-the-app',\r\n        element: <Abouttheapp/>\r\n      },\r\n      {\r\n        path: 'add-about-the-app',\r\n        element: <AddAboutTheApp/>\r\n      },\r\n      {\r\n        path: 'change-log',\r\n        element: <Changelog/>\r\n      },\r\n      {\r\n        path: 'add-change-log',\r\n        element: <AddChangeLog/>\r\n      },\r\n      {\r\n        path: 'app-support',\r\n        element: <Appsupport/>\r\n      },\r\n      {\r\n        path: 'add-app-support',\r\n        element: <AddAppsupport/>\r\n      },\r\n      {\r\n        path: 'give-feedback',\r\n        element: <Givefeedback/>\r\n      },\r\n      {\r\n        path: 'add-feedback',\r\n        element: <AddGiveFeedback/>\r\n      },\r\n      \r\n      {\r\n        path: 'report-problem',\r\n        element: <Reportproblem/>\r\n      },\r\n      {\r\n        path: 'add-report-problem',\r\n        element: <AddReportProblem/>\r\n      },\r\n      {\r\n        path: 'team-snapshot',\r\n        element: <Teamsnapshot/>\r\n      },\r\n      {\r\n        path: 'noticeboard',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead']}><NoticeBoard/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-notice-category',\r\n        element: <AddNoticeBoardCategory/>\r\n      },\r\n      {\r\n        path: 'add-notice',\r\n        element: <AddNotice/>\r\n      },\r\n     \r\n      {\r\n        path: 'events',\r\n        element: <HolidayCalendarGoogleList/>\r\n      },\r\n      {\r\n        path: 'seat-plan',\r\n        element: <OfficeSeatPlan/>\r\n      },\r\n\r\n      {\r\n        path: 'welcome',\r\n        element: <Welcome/>\r\n      },\r\n      {\r\n        path: 'passwordmanager',\r\n        element: <Welcome/>\r\n      },\r\n      {\r\n        path: 'creative-tools',\r\n        element: <Creativetools/>\r\n      }\r\n     \r\n      \r\n\r\n      \r\n    ],\r\n  },\r\n  {\r\n    path: 'users',\r\n    element: <UserList />,\r\n  },\r\n  {\r\n    path: '*',\r\n    element: <Navigate to=\"/login\" />,\r\n  }, \r\n];\r\n\r\n// Create the router\r\nconst router = createBrowserRouter(CreativeRoutes);\r\n\r\nexport default router;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,mBAAmB,EAAEC,QAAQ,QAAQ,kBAAkB;AAChE,OAAOC,UAAU,MAAM,wBAAwB;AAE/C,OAAOC,KAAK,MAAM,gBAAgB;AAClC,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,UAAU,MAAM,iCAAiC;AACxD,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,SAAS,MAAM,+BAA+B;AACrD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE,OAAOC,iBAAiB,MAAM,2CAA2C;AACzE,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,kBAAkB,MAAM,6CAA6C;AAC5E,OAAOC,cAAc,MAAM,qCAAqC;AAChE,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,YAAY,MAAM,0BAA0B;;AAEnD;AACA,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,kBAAkB,MAAM,6CAA6C;AAC5E,OAAOC,mBAAmB,MAAM,wDAAwD;AACxF,OAAOC,gBAAgB,MAAM,kDAAkD;AAC/E,OAAOC,iBAAiB,MAAM,0CAA0C;AACxE,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,WAAW,MAAM,sCAAsC;AAC9D,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,OAAOC,aAAa,MAAM,gDAAgD;AAC1E,OAAOC,WAAW,MAAM,+BAA+B;AACvD,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,gBAAgB,MAAM,0CAA0C;AACvE,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,SAAS,MAAM,8BAA8B;AACpD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,mBAAmB,MAAM,gEAAgE;AAChG,OAAOC,UAAU,MAAM,0CAA0C;AACjE,OAAOC,eAAe,MAAM,0CAA0C;;AAEtE;AACA,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,OAAO,MAAM,iCAAiC;AACrD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,cAAc,MAAM,sCAAsC;AACjE,OAAOC,YAAY,MAAM,iCAAiC;AAC1D,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,WAAW,MAAM,wCAAwC;AAChE,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,sBAAsB,MAAM,6DAA6D;AAChG,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,yBAAyB,MAAM,oDAAoD;AAC1F;AACA,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,aAAa,MAAM,2BAA2B;;AAGrD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAG,CACrB;EACEC,IAAI,EAAE,QAAQ;EACdC,OAAO,eAAEH,OAAA,CAAC3E,KAAK;IAAA+E,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACnB,CAAC;AACD;AACA;AACA;AACA;AACA;EACEL,IAAI,EAAE,gBAAgB;EACtBC,OAAO,eAAEH,OAAA,CAACL,aAAa;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC3B,CAAC,EACD;EACEL,IAAI,EAAE,wBAAwB;EAC9BC,OAAO,eAAEH,OAAA,CAACJ,cAAc;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC5B,CAAC,EACD;EACEL,IAAI,EAAE,kBAAkB;EACxBC,OAAO,eAAEH,OAAA,CAACjC,SAAS;IAAAqC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACvB,CAAC,EACD;EACEL,IAAI,EAAE,yBAAyB;EAC/BC,OAAO,eAAEH,OAAA,CAAChC,eAAe;IAAAoC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,GAAG;EACTC,OAAO,eAAEH,OAAA,CAACnD,cAAc;IAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAE;IAAAC,QAAA,eAACT,OAAA,CAAC5E,UAAU;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAgB,CAAC;EAC5KE,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,GAAG;IACTC,OAAO,eAAEH,OAAA,CAAC1E,SAAS;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,CAAE;EAC1B,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAACN,YAAY;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC1B,CAAC,EACD;IACEL,IAAI,EAAE,gBAAgB;IACtBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACrD,aAAa;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAErG,CAAC,EAED;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAACjC,SAAS;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACvB,CAAC,EAED;IACEL,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEH,OAAA,CAAChC,eAAe;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC7B,CAAC,EACD;IACEL,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEH,OAAA,CAAChC,eAAe;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC7B,CAAC,EACD;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC9B,UAAU;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC5K,CAAC,EACD;IACEL,IAAI,EAAE,sBAAsB;IAC5BC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC/B,mBAAmB;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACzI,CAAC,EAED;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAClD,YAAY;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC9K,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC3D,WAAW;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACnG,CAAC,EACD;IACEL,IAAI,EAAE,SAAS;IACfC,OAAO,eAAEH,OAAA,CAAClC,OAAO;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACrB,CAAC,EACD;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACnE,SAAS;QAAAuE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAChG,CAAC,EACD;IACEL,IAAI,EAAE,OAAO;IACbC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACxE,KAAK;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC3H,CAAC,EACD;IACEL,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACpE,OAAO;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC7H,CAAC,EACD;IACEL,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACjE,OAAO;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACzK,CAAC,EACD;IACEL,IAAI,EAAE,aAAa;IACnBC,OAAO,eAAEH,OAAA,CAACrE,UAAU;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBJ,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACrE,UAAU;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAChI,CAAC,EACD;IACEL,IAAI,EAAE,gBAAgB;IACtBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAChE,aAAa;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACnI,CAAC,EACD;IACEL,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC/D,gBAAgB;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACtI,CAAC,EACD;IACEL,IAAI,EAAE,qBAAqB;IAC3BC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC9D,iBAAiB;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACvI,CAAC,EACD;IACEL,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC7D,eAAe;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACrI,CAAC,EACD;IACEL,IAAI,EAAE,iBAAiB;IACvBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC5D,cAAc;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACpI,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACvE,WAAW;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACjI,CAAC,EACD;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACtE,SAAS;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC/H,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACpD,WAAW;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACjI,CAAC,EACD;IACEL,IAAI,EAAE,sBAAsB;IAC5BC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACzD,kBAAkB;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACxI,CAAC,EACD;IACEL,IAAI,EAAE,kBAAkB;IACxBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACxD,cAAc;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACpI,CAAC,EACD;IACEL,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC1D,QAAQ;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC9H,CAAC,EACD;IACEL,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACvD,eAAe;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACrI,CAAC,EACD;IACEL,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACtD,eAAe;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACrI,CAAC,EACD;IACEL,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAClE,QAAQ;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAChG,CAAC;EAED;EACA;IACEL,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACvC,aAAa;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACjK,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACzC,WAAW;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC/J,CAAC,EACD;IACEL,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACxC,SAAS;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAChI,CAAC,EAED;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACtB,WAAW;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC/J,CAAC,EAED;IACEL,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACrB,QAAQ;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC3K,CAAC;EAED;EACA;IACEL,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEH,OAAA,CAACtC,WAAW;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACzB,CAAC,EAED;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACrC,QAAQ;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC3K,CAAC;EAED;EACA;IACEL,IAAI,EAAE,kBAAkB;IACxBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACjD,OAAO;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC1K,CAAC,EACD;IACEL,IAAI,EAAE,aAAa;IACnBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC9C,kBAAkB;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACzI,CAAC,EACD;IACEL,IAAI,EAAE,gBAAgB;IACtBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAChD,WAAW;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC7K,CAAC,EACD;IACEL,IAAI,EAAE,SAAS;IACfC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC3C,iBAAiB;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACxI,CAAC,EACD;IACEL,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEH,OAAA,CAAC/C,QAAQ;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACtB,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAAC1C,WAAW;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACxB,CAAC,EACD;IACEL,IAAI,EAAE,uBAAuB;IAC7BC,OAAO,eAAEH,OAAA,CAAC7C,mBAAmB;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACjC,CAAC,EAED;IACEL,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEH,OAAA,CAAC5C,gBAAgB;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC9B,CAAC,EAED;IACEL,IAAI,EAAE,qBAAqB;IAC3BC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACpC,gBAAgB;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACvI,CAAC,EACD;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAACnC,aAAa;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAC1B,CAAC,EAED;IACEL,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC7B,eAAe;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACpJ,CAAC,EAED;IACEL,IAAI,EAAE,SAAS;IACfC,OAAO,eAAEH,OAAA,CAAC5B,IAAI;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAClB,CAAC,EACD;IACEL,IAAI,EAAE,kBAAkB;IACxBC,OAAO,eAAEH,OAAA,CAAC3B,OAAO;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACrB,CAAC,EACD;IACEL,IAAI,EAAE,eAAe;IACrBC,OAAO,eAAEH,OAAA,CAAC1B,WAAW;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACxB,CAAC,EACD;IACEL,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEH,OAAA,CAACzB,cAAc;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAC3B,CAAC,EACD;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAACvB,SAAS;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACtB,CAAC,EACD;IACEL,IAAI,EAAE,gBAAgB;IACtBC,OAAO,eAAEH,OAAA,CAACxB,YAAY;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACzB,CAAC,EACD;IACEL,IAAI,EAAE,aAAa;IACnBC,OAAO,eAAEH,OAAA,CAACpB,UAAU;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACvB,CAAC,EACD;IACEL,IAAI,EAAE,iBAAiB;IACvBC,OAAO,eAAEH,OAAA,CAACnB,aAAa;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAC1B,CAAC,EACD;IACEL,IAAI,EAAE,eAAe;IACrBC,OAAO,eAAEH,OAAA,CAAClB,YAAY;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACzB,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAAChB,eAAe;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAC5B,CAAC,EAED;IACEL,IAAI,EAAE,gBAAgB;IACtBC,OAAO,eAAEH,OAAA,CAACjB,aAAa;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAC1B,CAAC,EACD;IACEL,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,eAAEH,OAAA,CAACf,gBAAgB;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAC7B,CAAC,EACD;IACEL,IAAI,EAAE,eAAe;IACrBC,OAAO,eAAEH,OAAA,CAACd,YAAY;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACzB,CAAC,EACD;IACEL,IAAI,EAAE,aAAa;IACnBC,OAAO,eAAEH,OAAA,CAACnD,cAAc;MAAC2D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACZ,WAAW;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC9J,CAAC,EACD;IACEL,IAAI,EAAE,qBAAqB;IAC3BC,OAAO,eAAEH,OAAA,CAACX,sBAAsB;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACnC,CAAC,EACD;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAACV,SAAS;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACtB,CAAC,EAED;IACEL,IAAI,EAAE,QAAQ;IACdC,OAAO,eAAEH,OAAA,CAACT,yBAAyB;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACtC,CAAC,EACD;IACEL,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEH,OAAA,CAACR,cAAc;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAC3B,CAAC,EAED;IACEL,IAAI,EAAE,SAAS;IACfC,OAAO,eAAEH,OAAA,CAACH,OAAO;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACpB,CAAC,EACD;IACEL,IAAI,EAAE,iBAAiB;IACvBC,OAAO,eAAEH,OAAA,CAACH,OAAO;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACpB,CAAC,EACD;IACEL,IAAI,EAAE,gBAAgB;IACtBC,OAAO,eAAEH,OAAA,CAACF,aAAa;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAC1B,CAAC;AAML,CAAC,EACD;EACEL,IAAI,EAAE,OAAO;EACbC,OAAO,eAAEH,OAAA,CAACzE,QAAQ;IAAA6E,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACtB,CAAC,EACD;EACEL,IAAI,EAAE,GAAG;EACTC,OAAO,eAAEH,OAAA,CAAC7E,QAAQ;IAACuF,EAAE,EAAC;EAAQ;IAAAN,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAClC,CAAC,CACF;;AAED;AACA,MAAMI,MAAM,GAAGzF,mBAAmB,CAAC+E,cAAc,CAAC;AAElD,eAAeU,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}