{"version": 3, "file": "static/js/735.c09108ea.chunk.js", "mappings": "wLAIA,MAkGMA,EAAcC,IAAA,IAAC,MAAEC,EAAK,SAAEC,EAAQ,QAAEC,EAAO,YAAEC,EAAW,aAAEC,EAAY,eAAEC,EAAc,OAAEC,EAAM,KAAEC,GAAMR,EAAA,OACxGS,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sBAAqBC,SAAA,EAClCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4CAA2CC,SAAA,EACxDF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,sBAAqBC,SAAA,CAAC,UAAMC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAEV,QACzEW,EAAAA,EAAAA,KAAA,QAAMF,UAAU,sBAAqBC,SAAC,OACtCF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,sBAAqBC,SAAA,CAAC,eAAWC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAET,QAC9EU,EAAAA,EAAAA,KAAA,QAAMF,UAAU,sBAAqBC,SAAC,OACtCF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,sBAAqBC,SAAA,CAAC,mBAAeC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAER,QAClFM,EAAAA,EAAAA,MAAA,KAAGC,UAAU,sBAAqBC,SAAA,CAAC,kBAAcC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAEP,QACjFK,EAAAA,EAAAA,MAAA,KAAGC,UAAU,sBAAqBC,SAAA,CAAC,mBAAeC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAEN,QAClFI,EAAAA,EAAAA,MAAA,KAAGC,UAAU,sBAAqBC,SAAA,CAAC,oBAAgBC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAEL,EAAyB,UAAK,QACjHG,EAAAA,EAAAA,MAAA,KAAGC,UAAU,sBAAqBC,SAAA,CAAC,qBAAiBC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAEL,EAA0B,WAAK,QACnHG,EAAAA,EAAAA,MAAA,KAAGC,UAAU,sBAAqBC,SAAA,CAAC,cAAUC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAEL,EAAmB,IAAK,WAEvGG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2BAA0BC,SAAA,EAC3CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0HAAyHC,SAAA,EACtIC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,UAASC,SAAEV,KACzBW,EAAAA,EAAAA,KAAA,OAAKC,IAAKL,EAAMM,IAAKb,EAAOS,UAAU,iCACtCD,EAAAA,EAAAA,MAAA,KAAGC,UAAU,sBAAqBC,SAAA,CAAC,mBAClBC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAER,WAGnDS,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kCAAiCC,SAC7CI,OAAOC,KAAKT,GAAQU,KAAI,CAACC,EAAUC,KAClCP,EAAAA,EAAAA,KAACQ,EAAe,CAAanB,MAAOiB,KAAcX,EAAOW,IAAnCC,YAKpB,EAGFC,EAAkBC,IAAA,IAAC,MAAEpB,EAAK,KAAEqB,EAAI,eAAEhB,GAAgBe,EAAA,OACtDT,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,UAClBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBC,SAAA,EACtCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yFAAwFC,SAAA,EACrGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,UAASC,SAAEV,KACzBQ,EAAAA,EAAAA,MAAA,KAAGC,UAAU,oCAAmCC,SAAA,CAAC,UAAGW,SAEtDV,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gCAA+BC,SAC3CI,OAAOQ,QAAQjB,GAAkB,CAAC,GAAGW,KAAIO,IAAA,IAAEC,EAAMC,GAAMF,EAAA,OACtDZ,EAAAA,EAAAA,KAACe,EAAI,CAAY1B,MAAO,SAASwB,IAAQC,MAAOA,GAArCD,EAA8C,UAI3D,EAGFE,EAAOC,IAAA,IAAC,MAAE3B,EAAK,MAAEyB,GAAOE,EAAA,OAC5BnB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASC,SAAEV,KAC3BQ,EAAAA,EAAAA,MAAA,KAAGC,UAAU,sBAAqBC,SAAA,CAAC,iBAAKe,OACpC,EAGR,EAzJiBG,KACf,MAAMC,EAAQC,aAAaC,QAAQ,UAC3BC,KAAMC,EAAS,MAAEC,IAAUC,EAAAA,EAAAA,GAAgB,GAAGC,EAAAA,SAAgBP,IAEtEQ,EAAAA,EAAAA,YAAU,KACJH,GACFI,QAAQJ,MAAM,aAAcA,EAC9B,GACC,CAACA,IAGJ,MAAMK,EAAoBC,IACxB,IAAKA,EAAY,MAAO,GACxB,MAAOC,EAAOC,GAAWF,EAAWG,MAAM,KACpCC,EAAO,IAAIC,KAEjB,OADAD,EAAKE,SAASL,EAAOC,GACdE,EAAKG,mBAAmB,QAAS,CAAEC,KAAM,UAAWC,OAAQ,UAAWC,QAAQ,GAAO,EAIzFC,GAAWC,EAAAA,EAAAA,UAAQ,KACvB,IAAKC,MAAMC,QAAQrB,IAAmC,IAArBA,EAAUsB,OAAc,MAAO,CAAC,EAEjE,MAAMC,EAAa,CAAC,EA2DpB,OAzDAvB,EAAUwB,SAASC,IACZL,MAAMC,QAAQI,EAAKC,QAExBD,EAAKC,MAAMF,SAASG,IAAU,IAADC,EAC3B,MAAMC,EAAUF,EAAKG,KAEhBP,EAAWM,KACdN,EAAWM,GAAW,CACpBvD,KAAMqD,EAAKrD,KAAO,GAAG6B,EAAAA,IAAUwB,EAAKrD,OAAS,kCAC7CN,SAAU2D,EAAKI,KAAO,eACtB9D,QAAS,EACTC,YAAa,EACbC,aAAc,EACdC,eAAgB,CAAC,EACjBC,OAAQ,CAAC,IAIbkD,EAAWM,GAAS5D,WAGiB,QAAvB2D,EAAGH,EAAKO,uBAAe,IAAAJ,OAAA,EAApBA,EAAsBK,MAAKC,GAA0B,SAAhBA,EAAOJ,QAG7DP,EAAWM,GAAS3D,cAEpBqD,EAAWM,GAAS1D,eAIfiD,MAAMC,QAAQI,EAAKU,YAExBV,EAAKU,UAAUX,SAASY,IACtB,MAAMpD,EAAWoD,EAASC,WAErBd,EAAWM,GAASxD,OAAOW,KAC9BuC,EAAWM,GAASxD,OAAOW,GAAY,CACrCI,KAAMgD,EAASE,aAAeF,EAASG,UACnC,GAAGjC,EAAiB8B,EAASE,kBAAkBhC,EAAiB8B,EAASG,aACzE,2BACJnE,eAAgB,CAAC,IAIhBgD,MAAMC,QAAQI,EAAKe,iBAExBf,EAAKe,eAAehB,SAASiB,IAC3B,MAAMlD,EAAOkD,EAAaX,MAAQ,QAClCP,EAAWM,GAASxD,OAAOW,GAAUZ,eAAemB,IACjDgC,EAAWM,GAASxD,OAAOW,GAAUZ,eAAemB,IAAS,GAAK,EACrEgC,EAAWM,GAASzD,eAAemB,IAChCgC,EAAWM,GAASzD,eAAemB,IAAS,GAAK,CAAC,GACrD,GACF,GACF,IAGGgC,CAAU,GAChB,CAACvB,IAEJ,OACEtB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,SACgB,IAAjCI,OAAOC,KAAKoC,GAAUI,QACrB5C,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAC,uBAEzCI,OAAOC,KAAKoC,GAAUnC,KAAI,CAAC2D,EAAUzD,KACnCP,EAAAA,EAAAA,KAACb,EAAW,CAAaE,MAAO2E,KAAcxB,EAASwB,IAArCzD,MAGlB,C", "sources": ["pages/teamsnapshot/TeamArea.jsx"], "sourcesContent": ["import React, { useState, useEffect, useMemo } from \"react\";\r\nimport useFetchApiData from \"../../common/fetchData/useFetchApiData.jsx\";\r\nimport { API_URL } from \"../../common/fetchData/apiConfig.js\";\r\n\r\nconst TeamArea = () => {\r\n  const token = localStorage.getItem(\"token\");\r\n  const { data: usersData, error } = useFetchApiData(`${API_URL}users`, token);\r\n\r\n  useEffect(() => {\r\n    if (error) {\r\n      console.error(\"API Error:\", error);\r\n    }\r\n  }, [error]);\r\n\r\n  // Utility function to format time into 12-hour format\r\n  const formatTime12Hour = (timeString) => {\r\n    if (!timeString) return \"\";\r\n    const [hours, minutes] = timeString.split(\":\");\r\n    const date = new Date();\r\n    date.setHours(hours, minutes);\r\n    return date.toLocaleTimeString(\"en-US\", { hour: \"2-digit\", minute: \"2-digit\", hour12: true });\r\n  };\r\n\r\n  // Memoized Team Data Calculation\r\n  const teamData = useMemo(() => {\r\n    if (!Array.isArray(usersData) || usersData.length === 0) return {};\r\n\r\n    const teamCounts = {};\r\n\r\n    usersData.forEach((user) => {\r\n      if (!Array.isArray(user.teams)) return;\r\n\r\n      user.teams.forEach((team) => {\r\n        const teamKey = team.name;\r\n\r\n        if (!teamCounts[teamKey]) {\r\n          teamCounts[teamKey] = {\r\n            logo: team.logo ? `${API_URL}${team.logo}` : \"/assets/images/default-logo.png\", \r\n            teamLead: team.poc || \"Not Assigned\",\r\n            members: 0,\r\n            liveMembers: 0,\r\n            benchMembers: 0,\r\n            resourceCounts: {},\r\n            shifts: {},\r\n          };\r\n        }\r\n\r\n        teamCounts[teamKey].members++;\r\n\r\n    // Ensure member_statuses is an array and check if it contains \"Live\"\r\n    const isLiveMember = user.member_statuses?.some(status => status.name === \"Live\");\r\n\r\n    if (isLiveMember) {\r\n        teamCounts[teamKey].liveMembers++;\r\n    } else {\r\n        teamCounts[teamKey].benchMembers++;\r\n    }\r\n\r\n\r\n        if (!Array.isArray(user.schedules)) return;\r\n\r\n        user.schedules.forEach((schedule) => {\r\n          const shiftKey = schedule.shift_name;\r\n\r\n          if (!teamCounts[teamKey].shifts[shiftKey]) {\r\n            teamCounts[teamKey].shifts[shiftKey] = {\r\n              time: schedule.shift_start && schedule.shift_end\r\n                ? `${formatTime12Hour(schedule.shift_start)} - ${formatTime12Hour(schedule.shift_end)}`\r\n                : \"Shift time not available\",\r\n              resourceCounts: {},\r\n            };\r\n          }\r\n\r\n          if (!Array.isArray(user.resource_types)) return;\r\n\r\n          user.resource_types.forEach((resourceType) => {\r\n            const role = resourceType.name || \"Other\";\r\n            teamCounts[teamKey].shifts[shiftKey].resourceCounts[role] =\r\n              (teamCounts[teamKey].shifts[shiftKey].resourceCounts[role] || 0) + 1;\r\n            teamCounts[teamKey].resourceCounts[role] =\r\n              (teamCounts[teamKey].resourceCounts[role] || 0) + 1;\r\n          });\r\n        });\r\n      });\r\n    });\r\n\r\n    return teamCounts;\r\n  }, [usersData]);\r\n\r\n  return (\r\n    <div className=\"p-6\">\r\n      {Object.keys(teamData).length === 0 ? (\r\n        <p className=\"text-center text-gray-500\">No teams available</p>\r\n      ) : (\r\n        Object.keys(teamData).map((teamName, index) => (\r\n          <TeamSection key={index} title={teamName} {...teamData[teamName]} />\r\n        ))\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst TeamSection = ({ title, teamLead, members, liveMembers, benchMembers, resourceCounts, shifts, logo }) => (\r\n  <div className=\"p-6 rounded-lg mb-8\">\r\n    <div className=\"flex items-center gap-4 whitespace-nowrap\">\r\n      <p className=\"font-normal text-lg\">Team: <span className=\"text-primary\">{title}</span></p>\r\n      <span className=\"font-normal text-lg\">|</span>\r\n      <p className=\"font-normal text-lg\">Team Lead: <span className=\"text-primary\">{teamLead}</span></p>\r\n      <span className=\"font-normal text-lg\">|</span>\r\n      <p className=\"font-normal text-lg\">Total Members: <span className=\"text-primary\">{members}</span></p>\r\n      <p className=\"font-normal text-lg\">Live Members: <span className=\"text-primary\">{liveMembers}</span></p>\r\n      <p className=\"font-normal text-lg\">Bench Members: <span className=\"text-primary\">{benchMembers}</span></p>\r\n      <p className=\"font-normal text-lg\">Total Designer: <span className=\"text-primary\">{resourceCounts[\"Designer\"] || 0}</span></p>\r\n      <p className=\"font-normal text-lg\">Total Developer: <span className=\"text-primary\">{resourceCounts[\"Developer\"] || 0}</span></p>\r\n      <p className=\"font-normal text-lg\">Total QA: <span className=\"text-primary\">{resourceCounts[\"QA\"] || 0}</span></p>\r\n    </div>\r\n    <div className=\"flex flex-row gap-2 mt-2\">\r\n  <div className=\"w-[220px] p-4 rounded-lg text-center font-bold text-gray-700 shadow-md flex flex-col items-center justify-center border\">\r\n    <h5 className=\"text-lg\">{title}</h5>\r\n    <img src={logo} alt={title} className=\"w-16 h-16 rounded-full my-2\" />\r\n    <p className=\"font-normal text-lg\">\r\n      Total Members: <span className=\"text-primary\">{members}</span>\r\n    </p>\r\n  </div>\r\n  <div className=\"flex flex-col w-full gap-2 mt-2\">\r\n    {Object.keys(shifts).map((shiftKey, index) => (\r\n      <ShiftDetailCard key={index} title={shiftKey} {...shifts[shiftKey]} />\r\n    ))}\r\n  </div>\r\n</div>\r\n\r\n  </div>\r\n);\r\n\r\nconst ShiftDetailCard = ({ title, time, resourceCounts }) => (\r\n  <div className=\"p-4\">\r\n    <div className=\"flex items-center gap-4\">\r\n      <div className=\"w-[209px] h-[77px] p-4 rounded-lg text-center font-bold text-gray-700 shadow-md border\">\r\n        <h5 className=\"text-sm\">{title}</h5>\r\n        <p className=\"text-xs font-normal text-gray-600\">⏰ {time}</p>\r\n      </div>\r\n      <div className=\"grid grid-cols-4 gap-4 w-full\">\r\n        {Object.entries(resourceCounts || {}).map(([role, count]) => (\r\n          <Card key={role} title={`Total ${role}`} count={count} />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst Card = ({ title, count }) => (\r\n  <div className=\"h-[78px] p-4 rounded-lg text-center shadow-md border\">\r\n    <span className=\"text-sm\">{title}</span>\r\n    <p className=\"text-base font-bold\"> 👥 {count}</p>\r\n  </div>\r\n);\r\n\r\nexport default TeamArea;\r\n"], "names": ["TeamSection", "_ref", "title", "teamLead", "members", "liveMembers", "benchMembers", "resourceCounts", "shifts", "logo", "_jsxs", "className", "children", "_jsx", "src", "alt", "Object", "keys", "map", "shift<PERSON>ey", "index", "ShiftDetailCard", "_ref2", "time", "entries", "_ref3", "role", "count", "Card", "_ref4", "TeamArea", "token", "localStorage", "getItem", "data", "usersData", "error", "useFetchApiData", "API_URL", "useEffect", "console", "formatTime12Hour", "timeString", "hours", "minutes", "split", "date", "Date", "setHours", "toLocaleTimeString", "hour", "minute", "hour12", "teamData", "useMemo", "Array", "isArray", "length", "teamCounts", "for<PERSON>ach", "user", "teams", "team", "_user$member_statuses", "teamKey", "name", "poc", "member_statuses", "some", "status", "schedules", "schedule", "shift_name", "shift_start", "shift_end", "resource_types", "resourceType", "teamName"], "sourceRoot": ""}