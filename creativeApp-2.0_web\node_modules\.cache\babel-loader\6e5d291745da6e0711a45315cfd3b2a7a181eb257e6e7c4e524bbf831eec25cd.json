{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\components\\\\password-manager\\\\PasswordCardsTable.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport AddPasswordCardForm from \"./AddPasswordCardForm\";\nimport { confirmationAlert } from \"../../common/coreui\";\nimport { useDeleteUserDataMutation } from \"../../features/api/userDataApi\";\nimport FetchLoggedInRole from \"../../common/fetchData/FetchLoggedInRole\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PasswordCardsTable = ({\n  generatedPassword,\n  passwordStrength\n}) => {\n  _s();\n  // Get current user data\n  const {\n    userData\n  } = FetchLoggedInRole();\n  const currentUserId = userData === null || userData === void 0 ? void 0 : userData.id;\n\n  // Sample data - in real app this would come from API/state management\n  const [passwordCards, setPasswordCards] = useState([{\n    id: 1,\n    title: \"Gmail Account\",\n    platform: \"Gmail\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn\",\n    team: \"Team Name\",\n    department: \"IT\",\n    strength: \"Weak Password\",\n    strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n    authorId: currentUserId // Add author ID\n  }, {\n    id: 2,\n    title: \"Slack Workspace\",\n    platform: \"Slack\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"StrongPass123!@#\",\n    team: \"Team Name\",\n    department: \"IT\",\n    strength: \"Strong Password\",\n    strengthColor: \"bg-green-100 text-green-600 border-green-300\",\n    authorId: currentUserId\n  }, {\n    id: 3,\n    title: \"GitHub Repository\",\n    platform: \"GitHub\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"ModeratePass456\",\n    team: \"Team Name\",\n    department: \"Development\",\n    strength: \"Moderate Password\",\n    strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\",\n    authorId: 999 // Different author ID to test permissions\n  }, {\n    id: 4,\n    title: \"AWS Console\",\n    platform: \"AWS\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"WeakPass\",\n    team: \"Team Name\",\n    department: \"DevOps\",\n    strength: \"Weak Password\",\n    strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n    authorId: currentUserId\n  }, {\n    id: 5,\n    title: \"Jira Project\",\n    platform: \"Jira\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"AnotherStrongPass789!\",\n    team: \"Team Name\",\n    department: \"Project Management\",\n    strength: \"Strong Password\",\n    strengthColor: \"bg-green-100 text-green-600 border-green-300\",\n    authorId: 998 // Different author ID to test permissions\n  }, {\n    id: 6,\n    title: \"Office 365\",\n    platform: \"Microsoft 365\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"ModerateSecure123\",\n    team: \"Team Name\",\n    department: \"HR\",\n    strength: \"Moderate Password\",\n    strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\",\n    authorId: currentUserId\n  }, {\n    id: 7,\n    title: \"Database Admin\",\n    platform: \"MySQL\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"VeryWeakPass\",\n    team: \"Team Name\",\n    department: \"Database\",\n    strength: \"Weak Password\",\n    strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n    authorId: currentUserId\n  }]);\n  const [visiblePasswords, setVisiblePasswords] = useState({});\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [showNewTable, setShowNewTable] = useState(false);\n  const [showTeamTable, setShowTeamTable] = useState(true);\n  const [newPasswordCards, setNewPasswordCards] = useState([]);\n  const [editingRowId, setEditingRowId] = useState(null);\n  const [shareableCards, setShareableCards] = useState([]);\n  const [sortConfig, setSortConfig] = useState({\n    key: null,\n    direction: \"asc\"\n  });\n  const [deleteUserData] = useDeleteUserDataMutation();\n  const [viewData, setViewData] = useState(null);\n  const togglePasswordVisibility = id => {\n    setVisiblePasswords(prev => ({\n      ...prev,\n      [id]: !prev[id]\n    }));\n  };\n  const copyToClipboard = text => {\n    navigator.clipboard.writeText(text);\n    // You could add a toast notification here\n  };\n\n  // Handle individual row editing\n  const handleEdit = id => {\n    setEditingRowId(editingRowId === id ? null : id);\n  };\n\n  // Handle creating new table\n  const handleCreateNewTable = () => {\n    setShowNewTable(true);\n  };\n\n  // Handle delete entire team table - only author can delete\n  const handleDeleteTeamTable = () => {\n    // Check if current user is author of any cards\n    const isAuthor = passwordCards.some(card => card.authorId === currentUserId);\n    if (!isAuthor) {\n      alert(\"Only the author can delete the entire table.\");\n      return;\n    }\n\n    // Only proceed if there are selected cards or if user is author\n    if (shareableCards.length === 0) {\n      alert(\"Please select at least one row to delete the table.\");\n      return;\n    }\n    confirmationAlert({\n      onConfirm: () => {\n        setPasswordCards([]);\n      }\n    });\n  };\n\n  // Handle select all checkboxes password and\n\n  const toggleSelectAll = () => {\n    if (shareableCards.length === passwordCards.length) {\n      setShareableCards([]);\n    } else {\n      setShareableCards(passwordCards.map(card => card.id));\n    }\n  };\n\n  // Handle sorting\n  const handleSort = key => {\n    let direction = \"asc\";\n    if (sortConfig.key === key && sortConfig.direction === \"asc\") {\n      direction = \"desc\";\n    }\n    setSortConfig({\n      key,\n      direction\n    });\n  };\n\n  // Sort cards based on current sort config\n  const getSortedCards = cards => {\n    if (!sortConfig.key) return cards;\n    return [...cards].sort((a, b) => {\n      const aValue = a[sortConfig.key].toLowerCase();\n      const bValue = b[sortConfig.key].toLowerCase();\n      if (sortConfig.direction === \"asc\") {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n  };\n\n  // Handle shareable toggle\n  const toggleShareable = id => {\n    setShareableCards(prev => prev.includes(id) ? prev.filter(cardId => cardId !== id) : [...prev, id]);\n  };\n\n  // Handle form submission for new password cards\n  const handleAddPasswordCard = cardData => {\n    const newCard = {\n      ...cardData,\n      id: Date.now(),\n      password: \"••••••••••••\",\n      actualPassword: cardData.password\n    };\n    setNewPasswordCards(prev => [...prev, newCard]);\n    setShowAddForm(false);\n  };\n  const handleDelete = id => {\n    console.log(\"Delete password card:\", id);\n    // Show confirmation dialog and delete\n    setPasswordCards(prev => prev.filter(card => card.id !== id));\n  };\n\n  // Handle share functionality for department - only author can share\n  const handleShare = () => {\n    var _userData$departments, _userData$departments2;\n    // Check if current user is author of any cards\n    const isAuthor = passwordCards.some(card => card.authorId === currentUserId);\n    if (!isAuthor) {\n      alert(\"Only the author can share the table.\");\n      return;\n    }\n\n    // Get current user's department or team\n    const currentUserDepartment = (userData === null || userData === void 0 ? void 0 : (_userData$departments = userData.departments) === null || _userData$departments === void 0 ? void 0 : (_userData$departments2 = _userData$departments[0]) === null || _userData$departments2 === void 0 ? void 0 : _userData$departments2.name) || \"IT\";\n\n    // Filter cards that belong to the same department and are authored by current user\n    const departmentCards = passwordCards.filter(card => card.department === currentUserDepartment && card.authorId === currentUserId);\n    if (departmentCards.length === 0) {\n      alert(\"No password cards available to share in your department.\");\n      return;\n    }\n\n    // Create shareable data\n    const shareData = {\n      title: `${currentUserDepartment} Department Password Cards`,\n      cards: departmentCards,\n      sharedBy: `${userData === null || userData === void 0 ? void 0 : userData.fname} ${userData === null || userData === void 0 ? void 0 : userData.lname}` || \"Current User\",\n      sharedAt: new Date().toISOString()\n    };\n\n    // For now, copy to clipboard (you can implement actual sharing logic)\n    navigator.clipboard.writeText(JSON.stringify(shareData, null, 2)).then(() => {\n      alert(`${departmentCards.length} password cards from ${currentUserDepartment} department copied to clipboard!`);\n    }).catch(() => {\n      alert(\"Failed to copy to clipboard. Please try again.\");\n    });\n  };\n\n  // Placeholder avatar images\n  const avatarImages = [\"https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=A\", \"https://via.placeholder.com/32x32/10B981/FFFFFF?text=B\", \"https://via.placeholder.com/32x32/F59E0B/FFFFFF?text=C\", \"https://via.placeholder.com/32x32/EF4444/FFFFFF?text=D\"];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white dark:bg-gray-900\",\n    children: [showTeamTable && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-left text-2xl font-bold text-gray-900 dark:text-white\",\n            children: \"Teams Password Card\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleShare,\n            className: \"flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-rounded\",\n              children: \"share\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleDeleteTeamTable(),\n            className: \"flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg\",\n            title: \"Delete entire table\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-outlined text-sm\",\n              children: \"delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex -space-x-2\",\n            children: avatarImages.map((avatar, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n              src: avatar,\n              alt: `User ${index + 1}`,\n              className: \"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setEditingRowId(null);\n              setShowAddForm(!showAddForm);\n            },\n            className: `w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${showAddForm ? \"bg-primary text-white\" : \"bg-transparent text-primary border-2 border-primary\"}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-rounded mr-2\",\n              children: \"add\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this), \"Add Password\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), showAddForm && /*#__PURE__*/_jsxDEV(AddPasswordCardForm, {\n        onSubmit: handleAddPasswordCard,\n        onCancel: () => {\n          setShowAddForm(false);\n          setEditingRowId(null);\n        },\n        generatedPassword: generatedPassword,\n        passwordStrength: passwordStrength\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"w-12 px-4 py-3 text-left\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"checkbox-all\",\n                  type: \"checkbox\",\n                  checked: shareableCards.length === passwordCards.length && passwordCards.length > 0,\n                  onChange: toggleSelectAll,\n                  className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  onClick: () => handleSort(\"title\"),\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Title\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"title\" && sortConfig.direction === \"asc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25B2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"title\" && sortConfig.direction === \"desc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25BC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  onClick: () => handleSort(\"username\"),\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"User Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"username\" && sortConfig.direction === \"asc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25B2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 405,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"username\" && sortConfig.direction === \"desc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25BC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 415,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Department\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"divide-y divide-gray-200\",\n            children: getSortedCards(passwordCards).map(card => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: `shareable-${card.id}`,\n                  type: \"checkbox\",\n                  checked: shareableCards.includes(card.id),\n                  onChange: () => toggleShareable(card.id),\n                  className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\",\n                    children: \"TG\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 25\n                  }, this), editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    defaultValue: card.title,\n                    className: \"font-medium text-gray-900 border rounded px-2 py-1\",\n                    onBlur: e => {\n                      setPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                        ...c,\n                        title: e.target.value\n                      } : c));\n                      setEditingRowId(null);\n                    },\n                    onKeyDown: e => {\n                      if (e.key === \"Enter\") {\n                        e.target.blur();\n                      }\n                    },\n                    autoFocus: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: card.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    defaultValue: card.username,\n                    className: \"text-gray-900 border rounded px-2 py-1\",\n                    onBlur: e => {\n                      setPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                        ...c,\n                        username: e.target.value\n                      } : c));\n                    },\n                    onKeyDown: e => {\n                      if (e.key === \"Enter\") {\n                        e.target.blur();\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900\",\n                    children: card.username\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => copyToClipboard(card.username),\n                    className: \"ml-2 text-gray-400 hover:text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"content_copy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 520,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900 mr-2\",\n                    children: visiblePasswords[card.id] ? card.actualPassword : card.password\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 528,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => togglePasswordVisibility(card.id),\n                    className: \"text-gray-400 hover:text-gray-600 mr-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: visiblePasswords[card.id] ? \"visibility_off\" : \"visibility\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 537,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 533,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => copyToClipboard(card.actualPassword),\n                    className: \"text-gray-400 hover:text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"content_copy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 547,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 543,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 text-gray-900\",\n                children: card.team\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 text-gray-900\",\n                children: card.department\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`,\n                  children: card.strength\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(card.id),\n                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                    title: \"Edit\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"25\",\n                      height: \"24\",\n                      viewBox: \"0 0 25 24\",\n                      fill: \"none\",\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"mask\", {\n                        id: `mask1_${card.id}`,\n                        style: {\n                          maskType: \"alpha\"\n                        },\n                        maskUnits: \"userSpaceOnUse\",\n                        x: \"0\",\n                        y: \"0\",\n                        width: \"25\",\n                        height: \"24\",\n                        children: /*#__PURE__*/_jsxDEV(\"rect\", {\n                          x: \"0.5\",\n                          width: \"24\",\n                          height: \"24\",\n                          fill: \"#D9D9D9\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 587,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 578,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"g\", {\n                        mask: `url(#mask1_${card.id})`,\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12.9868 16.3213L14.4068 14.9013L7.50684 8.00134L6.08684 9.42134L12.9868 16.3213ZM15.8268 13.4813L17.2468 12.0613L15.8268 10.6413L14.4068 12.0613L15.8268 13.4813ZM5.50684 19.0013H8.92684L18.6668 9.26134C18.8441 9.08401 18.9441 8.84401 18.9441 8.59134C18.9441 8.33868 18.8441 8.09868 18.6668 7.92134L16.5868 5.84134C16.4095 5.66401 16.1695 5.56401 15.9168 5.56401C15.6641 5.56401 15.4241 5.66401 15.2468 5.84134L5.50684 15.5813V19.0013Z\",\n                          fill: \"currentColor\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 595,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 594,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 571,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 566,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(card.id),\n                    className: \"text-gray-400 hover:text-red-600 transition-colors\",\n                    title: \"Delete\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"25\",\n                      height: \"24\",\n                      viewBox: \"0 0 25 24\",\n                      fill: \"none\",\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"mask\", {\n                        id: `mask2_${card.id}`,\n                        style: {\n                          maskType: \"alpha\"\n                        },\n                        maskUnits: \"userSpaceOnUse\",\n                        x: \"0\",\n                        y: \"0\",\n                        width: \"25\",\n                        height: \"24\",\n                        children: /*#__PURE__*/_jsxDEV(\"rect\", {\n                          x: \"0.5\",\n                          width: \"24\",\n                          height: \"24\",\n                          fill: \"#D9D9D9\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 623,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 614,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"g\", {\n                        mask: `url(#mask2_${card.id})`,\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M8.60658 19.8997C8.15658 19.8997 7.77325 19.7414 7.45658 19.4247C7.13992 19.1081 6.98158 18.7247 6.98158 18.2747V6.89974H5.98158V4.89974H10.9816V3.89974H14.9816V4.89974H19.9816V6.89974H18.9816V18.2747C18.9816 18.7247 18.8232 19.1081 18.5066 19.4247C18.1899 19.7414 17.8066 19.8997 17.3566 19.8997H8.60658ZM16.9816 6.89974H8.98158V18.2747C8.98158 18.3581 9.01492 18.4331 9.08158 18.4997C9.14825 18.5664 9.22325 18.5997 9.30658 18.5997H16.6566C16.7399 18.5997 16.8149 18.5664 16.8816 18.4997C16.9482 18.4331 16.9816 18.3581 16.9816 18.2747V6.89974ZM10.9816 16.8997H12.9816V8.59974H10.9816V16.8997ZM13.9816 16.8997H15.9816V8.59974H13.9816V16.8997Z\",\n                          fill: \"currentColor\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 631,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 630,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 607,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 21\n              }, this)]\n            }, card.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), showNewTable && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-bold text-gray-900 mb-4\",\n        children: \"New Password Cards\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 651,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"w-12 px-4 py-3 text-left\",\n                children: \"Shareable\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  onClick: () => handleSort(\"title\"),\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Title\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 664,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"title\" && sortConfig.direction === \"asc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25B2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 666,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"title\" && sortConfig.direction === \"desc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25BC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 676,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 665,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 660,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  onClick: () => handleSort(\"username\"),\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"User Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 694,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"username\" && sortConfig.direction === \"asc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25B2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 696,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"username\" && sortConfig.direction === \"desc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25BC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 706,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Department\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"divide-y divide-gray-200\",\n            children: newPasswordCards.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"8\",\n                className: \"px-6 py-8 text-center text-gray-500\",\n                children: \"No password cards added yet. Click \\\"Add Password\\\" to add your first card.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 19\n            }, this) : getSortedCards(newPasswordCards).map(card => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: shareableCards.includes(card.id),\n                  onChange: () => toggleShareable(card.id),\n                  className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 751,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\",\n                    children: \"TG\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 760,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: card.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 763,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 text-gray-900\",\n                children: card.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900 mr-2\",\n                    children: card.password\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 773,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => copyToClipboard(card.actualPassword),\n                    className: \"text-gray-400 hover:text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"content_copy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 780,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 776,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 772,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 771,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 text-gray-900\",\n                children: card.team\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 786,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 text-gray-900\",\n                children: card.department\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 787,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`,\n                  children: card.strength\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 791,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(card.id),\n                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                    title: \"Edit\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"edit\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 804,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setNewPasswordCards(prev => prev.filter(c => c.id !== card.id)),\n                    className: \"text-gray-400 hover:text-red-600 transition-colors\",\n                    title: \"Delete\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"delete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 817,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 808,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 798,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 797,\n                columnNumber: 23\n              }, this)]\n            }, card.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 749,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 655,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 654,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 650,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center mt-6\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleCreateNewTable,\n        className: \"flex items-center justify-center px-6 py-2 text-sm font-medium text-white rounded-lg bg-primary hover:bg-primary/90 focus:ring-4 focus:ring-primary/30 transition-colors duration-200 focus:outline-none\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-rounded mr-2\",\n          children: \"add\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 838,\n          columnNumber: 11\n        }, this), \"Add New Password Card\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 834,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 833,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 276,\n    columnNumber: 5\n  }, this);\n};\n_s(PasswordCardsTable, \"sWXPl+U56r7bJ7WByuk1+qEn6Is=\", false, function () {\n  return [useDeleteUserDataMutation];\n});\n_c = PasswordCardsTable;\nexport default PasswordCardsTable;\nvar _c;\n$RefreshReg$(_c, \"PasswordCardsTable\");", "map": {"version": 3, "names": ["React", "useState", "AddPasswordCardForm", "<PERSON><PERSON><PERSON><PERSON>", "useDeleteUserDataMutation", "FetchLoggedInRole", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PasswordCardsTable", "generatedPassword", "passwordStrength", "_s", "userData", "currentUserId", "id", "passwordCards", "setPasswordCards", "title", "platform", "username", "password", "actualPassword", "team", "department", "strength", "strengthColor", "authorId", "visiblePasswords", "setVisiblePasswords", "showAddForm", "setShowAddForm", "showNewTable", "setShowNewTable", "showTeamTable", "setShowTeamTable", "newPasswordCards", "setNewPasswordCards", "editingRowId", "setEditingRowId", "shareableCards", "setShareableCards", "sortConfig", "setSortConfig", "key", "direction", "deleteUserData", "viewData", "setViewData", "togglePasswordVisibility", "prev", "copyToClipboard", "text", "navigator", "clipboard", "writeText", "handleEdit", "handleCreateNewTable", "handleDeleteTeamTable", "is<PERSON><PERSON><PERSON>", "some", "card", "alert", "length", "onConfirm", "toggleSelectAll", "map", "handleSort", "getSortedCards", "cards", "sort", "a", "b", "aValue", "toLowerCase", "bValue", "toggleShareable", "includes", "filter", "cardId", "handleAddPasswordCard", "cardData", "newCard", "Date", "now", "handleDelete", "console", "log", "handleShare", "_userData$departments", "_userData$departments2", "currentUserDepartment", "departments", "name", "departmentCards", "shareData", "sharedBy", "fname", "lname", "sharedAt", "toISOString", "JSON", "stringify", "then", "catch", "avatarImages", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "avatar", "index", "src", "alt", "onSubmit", "onCancel", "type", "checked", "onChange", "defaultValue", "onBlur", "e", "c", "target", "value", "onKeyDown", "blur", "autoFocus", "width", "height", "viewBox", "fill", "xmlns", "style", "maskType", "maskUnits", "x", "y", "mask", "d", "colSpan", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/PasswordCardsTable.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport AddPasswordCardForm from \"./AddPasswordCardForm\";\nimport { confirmationAlert } from \"../../common/coreui\";\nimport { useDeleteUserDataMutation } from \"../../features/api/userDataApi\";\nimport FetchLoggedInRole from \"../../common/fetchData/FetchLoggedInRole\";\n\nconst PasswordCardsTable = ({ generatedPassword, passwordStrength }) => {\n  // Get current user data\n  const { userData } = FetchLoggedInRole();\n  const currentUserId = userData?.id;\n\n  // Sample data - in real app this would come from API/state management\n  const [passwordCards, setPasswordCards] = useState([\n    {\n      id: 1,\n      title: \"Gmail Account\",\n      platform: \"Gmail\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn\",\n      team: \"Team Name\",\n      department: \"IT\",\n      strength: \"Weak Password\",\n      strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n      authorId: currentUserId, // Add author ID\n    },\n    {\n      id: 2,\n      title: \"Slack Workspace\",\n      platform: \"Slack\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"StrongPass123!@#\",\n      team: \"Team Name\",\n      department: \"IT\",\n      strength: \"Strong Password\",\n      strengthColor: \"bg-green-100 text-green-600 border-green-300\",\n      authorId: currentUserId,\n    },\n    {\n      id: 3,\n      title: \"GitHub Repository\",\n      platform: \"GitHub\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"ModeratePass456\",\n      team: \"Team Name\",\n      department: \"Development\",\n      strength: \"Moderate Password\",\n      strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\",\n      authorId: 999, // Different author ID to test permissions\n    },\n    {\n      id: 4,\n      title: \"AWS Console\",\n      platform: \"AWS\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"WeakPass\",\n      team: \"Team Name\",\n      department: \"DevOps\",\n      strength: \"Weak Password\",\n      strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n      authorId: currentUserId,\n    },\n    {\n      id: 5,\n      title: \"Jira Project\",\n      platform: \"Jira\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"AnotherStrongPass789!\",\n      team: \"Team Name\",\n      department: \"Project Management\",\n      strength: \"Strong Password\",\n      strengthColor: \"bg-green-100 text-green-600 border-green-300\",\n      authorId: 998, // Different author ID to test permissions\n    },\n    {\n      id: 6,\n      title: \"Office 365\",\n      platform: \"Microsoft 365\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"ModerateSecure123\",\n      team: \"Team Name\",\n      department: \"HR\",\n      strength: \"Moderate Password\",\n      strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\",\n      authorId: currentUserId,\n    },\n    {\n      id: 7,\n      title: \"Database Admin\",\n      platform: \"MySQL\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"VeryWeakPass\",\n      team: \"Team Name\",\n      department: \"Database\",\n      strength: \"Weak Password\",\n      strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n      authorId: currentUserId,\n    },\n  ]);\n\n  const [visiblePasswords, setVisiblePasswords] = useState({});\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [showNewTable, setShowNewTable] = useState(false);\n  const [showTeamTable, setShowTeamTable] = useState(true);\n  const [newPasswordCards, setNewPasswordCards] = useState([]);\n  const [editingRowId, setEditingRowId] = useState(null);\n  const [shareableCards, setShareableCards] = useState([]);\n  const [sortConfig, setSortConfig] = useState({ key: null, direction: \"asc\" });\n\n  const [deleteUserData] = useDeleteUserDataMutation();\n  const [viewData, setViewData] = useState(null);\n  const togglePasswordVisibility = (id) => {\n    setVisiblePasswords((prev) => ({\n      ...prev,\n      [id]: !prev[id],\n    }));\n  };\n\n  const copyToClipboard = (text) => {\n    navigator.clipboard.writeText(text);\n    // You could add a toast notification here\n  };\n\n  // Handle individual row editing\n  const handleEdit = (id) => {\n    setEditingRowId(editingRowId === id ? null : id);\n  };\n\n  // Handle creating new table\n  const handleCreateNewTable = () => {\n    setShowNewTable(true);\n  };\n\n  // Handle delete entire team table - only author can delete\n  const handleDeleteTeamTable = () => {\n    // Check if current user is author of any cards\n    const isAuthor = passwordCards.some(card => card.authorId === currentUserId);\n\n    if (!isAuthor) {\n      alert(\"Only the author can delete the entire table.\");\n      return;\n    }\n\n    // Only proceed if there are selected cards or if user is author\n    if (shareableCards.length === 0) {\n      alert(\"Please select at least one row to delete the table.\");\n      return;\n    }\n\n    confirmationAlert({\n      onConfirm: () => {\n        setPasswordCards([]);\n      },\n    });\n  };\n\n  // Handle select all checkboxes password and\n  \n  const toggleSelectAll = () => {\n    if (shareableCards.length === passwordCards.length) {\n      setShareableCards([]);\n    } else {\n      setShareableCards(passwordCards.map((card) => card.id));\n    }\n  };\n\n  // Handle sorting\n  const handleSort = (key) => {\n    let direction = \"asc\";\n    if (sortConfig.key === key && sortConfig.direction === \"asc\") {\n      direction = \"desc\";\n    }\n    setSortConfig({ key, direction });\n  };\n\n  // Sort cards based on current sort config\n  const getSortedCards = (cards) => {\n    if (!sortConfig.key) return cards;\n\n    return [...cards].sort((a, b) => {\n      const aValue = a[sortConfig.key].toLowerCase();\n      const bValue = b[sortConfig.key].toLowerCase();\n\n      if (sortConfig.direction === \"asc\") {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n  };\n\n  // Handle shareable toggle\n  const toggleShareable = (id) => {\n    setShareableCards((prev) =>\n      prev.includes(id) ? prev.filter((cardId) => cardId !== id) : [...prev, id]\n    );\n  };\n\n  // Handle form submission for new password cards\n  const handleAddPasswordCard = (cardData) => {\n    const newCard = {\n      ...cardData,\n      id: Date.now(),\n      password: \"••••••••••••\",\n      actualPassword: cardData.password,\n    };\n    setNewPasswordCards((prev) => [...prev, newCard]);\n    setShowAddForm(false);\n  };\n\n  const handleDelete = (id) => {\n    console.log(\"Delete password card:\", id);\n    // Show confirmation dialog and delete\n    setPasswordCards((prev) => prev.filter((card) => card.id !== id));\n  };\n\n  // Handle share functionality for department - only author can share\n  const handleShare = () => {\n    // Check if current user is author of any cards\n    const isAuthor = passwordCards.some(card => card.authorId === currentUserId);\n\n    if (!isAuthor) {\n      alert(\"Only the author can share the table.\");\n      return;\n    }\n\n    // Get current user's department or team\n    const currentUserDepartment = userData?.departments?.[0]?.name || \"IT\";\n\n    // Filter cards that belong to the same department and are authored by current user\n    const departmentCards = passwordCards.filter(\n      (card) => card.department === currentUserDepartment && card.authorId === currentUserId\n    );\n\n    if (departmentCards.length === 0) {\n      alert(\"No password cards available to share in your department.\");\n      return;\n    }\n\n    // Create shareable data\n    const shareData = {\n      title: `${currentUserDepartment} Department Password Cards`,\n      cards: departmentCards,\n      sharedBy: `${userData?.fname} ${userData?.lname}` || \"Current User\",\n      sharedAt: new Date().toISOString(),\n    };\n\n    // For now, copy to clipboard (you can implement actual sharing logic)\n    navigator.clipboard\n      .writeText(JSON.stringify(shareData, null, 2))\n      .then(() => {\n        alert(\n          `${departmentCards.length} password cards from ${currentUserDepartment} department copied to clipboard!`\n        );\n      })\n      .catch(() => {\n        alert(\"Failed to copy to clipboard. Please try again.\");\n      });\n  };\n\n  // Placeholder avatar images\n  const avatarImages = [\n    \"https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=A\",\n    \"https://via.placeholder.com/32x32/10B981/FFFFFF?text=B\",\n    \"https://via.placeholder.com/32x32/F59E0B/FFFFFF?text=C\",\n    \"https://via.placeholder.com/32x32/EF4444/FFFFFF?text=D\",\n  ];\n\n  return (\n    <div className=\"bg-white dark:bg-gray-900\">\n      {showTeamTable && (\n        <>\n          {/* Header */}\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\">\n            <div className=\"flex items-center space-x-4\">\n              <h2 className=\"text-left text-2xl font-bold text-gray-900 dark:text-white\">\n                Teams Password Card\n              </h2>\n\n              {/* Share Icon */}\n              <button\n                onClick={handleShare}\n                className=\"flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\"\n              >\n                <span className=\"material-symbols-rounded\">share</span>\n              </button>\n\n              {/* Delete Team Table Icon */}\n              <button\n                onClick={() => handleDeleteTeamTable()}\n                className=\"flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg\"\n                title=\"Delete entire table\"\n              >\n                {/* <span className=\"material-symbols-rounded\">delete</span> */}\n                <span className=\"material-symbols-outlined text-sm\">\n                  delete\n                </span>\n              </button>\n\n              {/* User Avatars */}\n              <div className=\"flex -space-x-2\">\n                {avatarImages.map((avatar, index) => (\n                  <img\n                    key={index}\n                    src={avatar}\n                    alt={`User ${index + 1}`}\n                    className=\"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"\n                  />\n                ))}\n              </div>\n            </div>\n\n            <div className=\"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0\">\n              {/* Add Password Card Button */}\n              <button\n                onClick={() => {\n                  setEditingRowId(null);\n                  setShowAddForm(!showAddForm);\n                }}\n                className={`w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${\n                  showAddForm\n                    ? \"bg-primary text-white\"\n                    : \"bg-transparent text-primary border-2 border-primary\"\n                }`}\n              >\n                <span className=\"material-symbols-rounded mr-2\">add</span>\n                Add Password\n              </button>\n            </div>\n          </div>\n\n          {/* Add/Edit Password Form - Embedded */}\n          {showAddForm && (\n            <AddPasswordCardForm\n              onSubmit={handleAddPasswordCard}\n              onCancel={() => {\n                setShowAddForm(false);\n                setEditingRowId(null);\n              }}\n              generatedPassword={generatedPassword}\n              passwordStrength={passwordStrength}\n            />\n          )}\n\n          {/* Table */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50 border-b border-gray-200\">\n                <tr>\n                  <th className=\"w-12 px-4 py-3 text-left\">\n                    <input\n                      id=\"checkbox-all\"\n                      type=\"checkbox\"\n                      checked={\n                        shareableCards.length === passwordCards.length &&\n                        passwordCards.length > 0\n                      }\n                      onChange={toggleSelectAll}\n                      className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                    />\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"title\")}\n                    >\n                      <span>Title</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"username\")}\n                    >\n                      <span>User Name</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Password\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Team\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Department\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Level\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Action\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"divide-y divide-gray-200\">\n                {getSortedCards(passwordCards).map((card) => (\n                  <tr key={card.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-4 py-4\">\n                      <input\n                        id={`shareable-${card.id}`}\n                        type=\"checkbox\"\n                        checked={shareableCards.includes(card.id)}\n                        onChange={() => toggleShareable(card.id)}\n                        className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                      />\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center\">\n                        <div className=\"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\">\n                          TG\n                        </div>\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.title}\n                            className=\"font-medium text-gray-900 border rounded px-2 py-1\"\n                            onBlur={(e) => {\n                              setPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, title: e.target.value }\n                                    : c\n                                )\n                              );\n                              setEditingRowId(null);\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                            autoFocus\n                          />\n                        ) : (\n                          <span className=\"font-medium text-gray-900\">\n                            {card.title}\n                          </span>\n                        )}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center\">\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.username}\n                            className=\"text-gray-900 border rounded px-2 py-1\"\n                            onBlur={(e) => {\n                              setPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, username: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                          />\n                        ) : (\n                          <span className=\"text-gray-900\">{card.username}</span>\n                        )}\n                        <button\n                          onClick={() => copyToClipboard(card.username)}\n                          className=\"ml-2 text-gray-400 hover:text-gray-600\"\n                        >\n                          <span className=\"material-symbols-rounded text-sm\">\n                            content_copy\n                          </span>\n                        </button>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center\">\n                        <span className=\"text-gray-900 mr-2\">\n                          {visiblePasswords[card.id]\n                            ? card.actualPassword\n                            : card.password}\n                        </span>\n                        <button\n                          onClick={() => togglePasswordVisibility(card.id)}\n                          className=\"text-gray-400 hover:text-gray-600 mr-2\"\n                        >\n                          <span className=\"material-symbols-rounded text-sm\">\n                            {visiblePasswords[card.id]\n                              ? \"visibility_off\"\n                              : \"visibility\"}\n                          </span>\n                        </button>\n                        <button\n                          onClick={() => copyToClipboard(card.actualPassword)}\n                          className=\"text-gray-400 hover:text-gray-600\"\n                        >\n                          <span className=\"material-symbols-rounded text-sm\">\n                            content_copy\n                          </span>\n                        </button>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 text-gray-900\">{card.team}</td>\n                    <td className=\"px-6 py-4 text-gray-900\">\n                      {card.department}\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <span\n                        className={`px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`}\n                      >\n                        {card.strength}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center space-x-3\">\n                        <button\n                          onClick={() => handleEdit(card.id)}\n                          className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n                          title=\"Edit\"\n                        >\n                          <svg\n                            width=\"25\"\n                            height=\"24\"\n                            viewBox=\"0 0 25 24\"\n                            fill=\"none\"\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                          >\n                            <mask\n                              id={`mask1_${card.id}`}\n                              style={{ maskType: \"alpha\" }}\n                              maskUnits=\"userSpaceOnUse\"\n                              x=\"0\"\n                              y=\"0\"\n                              width=\"25\"\n                              height=\"24\"\n                            >\n                              <rect\n                                x=\"0.5\"\n                                width=\"24\"\n                                height=\"24\"\n                                fill=\"#D9D9D9\"\n                              />\n                            </mask>\n                            <g mask={`url(#mask1_${card.id})`}>\n                              <path\n                                d=\"M12.9868 16.3213L14.4068 14.9013L7.50684 8.00134L6.08684 9.42134L12.9868 16.3213ZM15.8268 13.4813L17.2468 12.0613L15.8268 10.6413L14.4068 12.0613L15.8268 13.4813ZM5.50684 19.0013H8.92684L18.6668 9.26134C18.8441 9.08401 18.9441 8.84401 18.9441 8.59134C18.9441 8.33868 18.8441 8.09868 18.6668 7.92134L16.5868 5.84134C16.4095 5.66401 16.1695 5.56401 15.9168 5.56401C15.6641 5.56401 15.4241 5.66401 15.2468 5.84134L5.50684 15.5813V19.0013Z\"\n                                fill=\"currentColor\"\n                              />\n                            </g>\n                          </svg>\n                        </button>\n                        <button\n                          onClick={() => handleDelete(card.id)}\n                          className=\"text-gray-400 hover:text-red-600 transition-colors\"\n                          title=\"Delete\"\n                        >\n                          <svg\n                            width=\"25\"\n                            height=\"24\"\n                            viewBox=\"0 0 25 24\"\n                            fill=\"none\"\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                          >\n                            <mask\n                              id={`mask2_${card.id}`}\n                              style={{ maskType: \"alpha\" }}\n                              maskUnits=\"userSpaceOnUse\"\n                              x=\"0\"\n                              y=\"0\"\n                              width=\"25\"\n                              height=\"24\"\n                            >\n                              <rect\n                                x=\"0.5\"\n                                width=\"24\"\n                                height=\"24\"\n                                fill=\"#D9D9D9\"\n                              />\n                            </mask>\n                            <g mask={`url(#mask2_${card.id})`}>\n                              <path\n                                d=\"M8.60658 19.8997C8.15658 19.8997 7.77325 19.7414 7.45658 19.4247C7.13992 19.1081 6.98158 18.7247 6.98158 18.2747V6.89974H5.98158V4.89974H10.9816V3.89974H14.9816V4.89974H19.9816V6.89974H18.9816V18.2747C18.9816 18.7247 18.8232 19.1081 18.5066 19.4247C18.1899 19.7414 17.8066 19.8997 17.3566 19.8997H8.60658ZM16.9816 6.89974H8.98158V18.2747C8.98158 18.3581 9.01492 18.4331 9.08158 18.4997C9.14825 18.5664 9.22325 18.5997 9.30658 18.5997H16.6566C16.7399 18.5997 16.8149 18.5664 16.8816 18.4997C16.9482 18.4331 16.9816 18.3581 16.9816 18.2747V6.89974ZM10.9816 16.8997H12.9816V8.59974H10.9816V16.8997ZM13.9816 16.8997H15.9816V8.59974H13.9816V16.8997Z\"\n                                fill=\"currentColor\"\n                              />\n                            </g>\n                          </svg>\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </>\n      )}\n\n      {/* New Password Cards Table */}\n      {showNewTable && (\n        <div className=\"mt-8\">\n          <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n            New Password Cards\n          </h3>\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50 border-b border-gray-200\">\n                <tr>\n                  <th className=\"w-12 px-4 py-3 text-left\">Shareable</th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"title\")}\n                    >\n                      <span>Title</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"username\")}\n                    >\n                      <span>User Name</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Password\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Team\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Department\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Level\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Action\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"divide-y divide-gray-200\">\n                {newPasswordCards.length === 0 ? (\n                  <tr>\n                    <td\n                      colSpan=\"8\"\n                      className=\"px-6 py-8 text-center text-gray-500\"\n                    >\n                      No password cards added yet. Click \"Add Password\" to add\n                      your first card.\n                    </td>\n                  </tr>\n                ) : (\n                  getSortedCards(newPasswordCards).map((card) => (\n                    <tr key={card.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-4 py-4\">\n                        <input\n                          type=\"checkbox\"\n                          checked={shareableCards.includes(card.id)}\n                          onChange={() => toggleShareable(card.id)}\n                          className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                        />\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          <div className=\"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\">\n                            TG\n                          </div>\n                          <span className=\"font-medium text-gray-900\">\n                            {card.title}\n                          </span>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 text-gray-900\">\n                        {card.username}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          <span className=\"text-gray-900 mr-2\">\n                            {card.password}\n                          </span>\n                          <button\n                            onClick={() => copyToClipboard(card.actualPassword)}\n                            className=\"text-gray-400 hover:text-gray-600\"\n                          >\n                            <span className=\"material-symbols-rounded text-sm\">\n                              content_copy\n                            </span>\n                          </button>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 text-gray-900\">{card.team}</td>\n                      <td className=\"px-6 py-4 text-gray-900\">\n                        {card.department}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <span\n                          className={`px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`}\n                        >\n                          {card.strength}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center space-x-3\">\n                          <button\n                            onClick={() => handleEdit(card.id)}\n                            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n                            title=\"Edit\"\n                          >\n                            <span className=\"material-symbols-rounded text-sm\">\n                              edit\n                            </span>\n                          </button>\n                          <button\n                            onClick={() =>\n                              setNewPasswordCards((prev) =>\n                                prev.filter((c) => c.id !== card.id)\n                              )\n                            }\n                            className=\"text-gray-400 hover:text-red-600 transition-colors\"\n                            title=\"Delete\"\n                          >\n                            <span className=\"material-symbols-rounded text-sm\">\n                              delete\n                            </span>\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))\n                )}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      )}\n\n      {/* Add New Password Card Button (Bottom) */}\n      <div className=\"flex justify-center mt-6\">\n        <button\n          onClick={handleCreateNewTable}\n          className=\"flex items-center justify-center px-6 py-2 text-sm font-medium text-white rounded-lg bg-primary hover:bg-primary/90 focus:ring-4 focus:ring-primary/30 transition-colors duration-200 focus:outline-none\"\n        >\n          <span className=\"material-symbols-rounded mr-2\">add</span>\n          Add New Password Card\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default PasswordCardsTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,OAAOC,iBAAiB,MAAM,0CAA0C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzE,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,iBAAiB;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EACtE;EACA,MAAM;IAAEC;EAAS,CAAC,GAAGT,iBAAiB,CAAC,CAAC;EACxC,MAAMU,aAAa,GAAGD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,EAAE;;EAElC;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,CACjD;IACEe,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,6CAA6C;IAC7DC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE,wCAAwC;IACvDC,QAAQ,EAAEb,aAAa,CAAE;EAC3B,CAAC,EACD;IACEC,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,iBAAiB;IACxBC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,kBAAkB;IAClCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,iBAAiB;IAC3BC,aAAa,EAAE,8CAA8C;IAC7DC,QAAQ,EAAEb;EACZ,CAAC,EACD;IACEC,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,mBAAmB;IAC1BC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,iBAAiB;IACjCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,aAAa;IACzBC,QAAQ,EAAE,mBAAmB;IAC7BC,aAAa,EAAE,iDAAiD;IAChEC,QAAQ,EAAE,GAAG,CAAE;EACjB,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,aAAa;IACpBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,UAAU;IAC1BC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE,wCAAwC;IACvDC,QAAQ,EAAEb;EACZ,CAAC,EACD;IACEC,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,uBAAuB;IACvCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,oBAAoB;IAChCC,QAAQ,EAAE,iBAAiB;IAC3BC,aAAa,EAAE,8CAA8C;IAC7DC,QAAQ,EAAE,GAAG,CAAE;EACjB,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,eAAe;IACzBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,mBAAmB;IACnCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,mBAAmB;IAC7BC,aAAa,EAAE,iDAAiD;IAChEC,QAAQ,EAAEb;EACZ,CAAC,EACD;IACEC,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,gBAAgB;IACvBC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,cAAc;IAC9BC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE,wCAAwC;IACvDC,QAAQ,EAAEb;EACZ,CAAC,CACF,CAAC;EAEF,MAAM,CAACc,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC;IAAE4C,GAAG,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAM,CAAC,CAAC;EAE7E,MAAM,CAACC,cAAc,CAAC,GAAG3C,yBAAyB,CAAC,CAAC;EACpD,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAMiD,wBAAwB,GAAIlC,EAAE,IAAK;IACvCc,mBAAmB,CAAEqB,IAAI,KAAM;MAC7B,GAAGA,IAAI;MACP,CAACnC,EAAE,GAAG,CAACmC,IAAI,CAACnC,EAAE;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMoC,eAAe,GAAIC,IAAI,IAAK;IAChCC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMI,UAAU,GAAIzC,EAAE,IAAK;IACzBwB,eAAe,CAACD,YAAY,KAAKvB,EAAE,GAAG,IAAI,GAAGA,EAAE,CAAC;EAClD,CAAC;;EAED;EACA,MAAM0C,oBAAoB,GAAGA,CAAA,KAAM;IACjCxB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMyB,qBAAqB,GAAGA,CAAA,KAAM;IAClC;IACA,MAAMC,QAAQ,GAAG3C,aAAa,CAAC4C,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAClC,QAAQ,KAAKb,aAAa,CAAC;IAE5E,IAAI,CAAC6C,QAAQ,EAAE;MACbG,KAAK,CAAC,8CAA8C,CAAC;MACrD;IACF;;IAEA;IACA,IAAItB,cAAc,CAACuB,MAAM,KAAK,CAAC,EAAE;MAC/BD,KAAK,CAAC,qDAAqD,CAAC;MAC5D;IACF;IAEA5D,iBAAiB,CAAC;MAChB8D,SAAS,EAAEA,CAAA,KAAM;QACf/C,gBAAgB,CAAC,EAAE,CAAC;MACtB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;;EAEA,MAAMgD,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIzB,cAAc,CAACuB,MAAM,KAAK/C,aAAa,CAAC+C,MAAM,EAAE;MAClDtB,iBAAiB,CAAC,EAAE,CAAC;IACvB,CAAC,MAAM;MACLA,iBAAiB,CAACzB,aAAa,CAACkD,GAAG,CAAEL,IAAI,IAAKA,IAAI,CAAC9C,EAAE,CAAC,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAMoD,UAAU,GAAIvB,GAAG,IAAK;IAC1B,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIH,UAAU,CAACE,GAAG,KAAKA,GAAG,IAAIF,UAAU,CAACG,SAAS,KAAK,KAAK,EAAE;MAC5DA,SAAS,GAAG,MAAM;IACpB;IACAF,aAAa,CAAC;MAAEC,GAAG;MAAEC;IAAU,CAAC,CAAC;EACnC,CAAC;;EAED;EACA,MAAMuB,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAI,CAAC3B,UAAU,CAACE,GAAG,EAAE,OAAOyB,KAAK;IAEjC,OAAO,CAAC,GAAGA,KAAK,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC/B,MAAMC,MAAM,GAAGF,CAAC,CAAC7B,UAAU,CAACE,GAAG,CAAC,CAAC8B,WAAW,CAAC,CAAC;MAC9C,MAAMC,MAAM,GAAGH,CAAC,CAAC9B,UAAU,CAACE,GAAG,CAAC,CAAC8B,WAAW,CAAC,CAAC;MAE9C,IAAIhC,UAAU,CAACG,SAAS,KAAK,KAAK,EAAE;QAClC,OAAO4B,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MACvD,CAAC,MAAM;QACL,OAAOF,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MACvD;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAI7D,EAAE,IAAK;IAC9B0B,iBAAiB,CAAES,IAAI,IACrBA,IAAI,CAAC2B,QAAQ,CAAC9D,EAAE,CAAC,GAAGmC,IAAI,CAAC4B,MAAM,CAAEC,MAAM,IAAKA,MAAM,KAAKhE,EAAE,CAAC,GAAG,CAAC,GAAGmC,IAAI,EAAEnC,EAAE,CAC3E,CAAC;EACH,CAAC;;EAED;EACA,MAAMiE,qBAAqB,GAAIC,QAAQ,IAAK;IAC1C,MAAMC,OAAO,GAAG;MACd,GAAGD,QAAQ;MACXlE,EAAE,EAAEoE,IAAI,CAACC,GAAG,CAAC,CAAC;MACd/D,QAAQ,EAAE,cAAc;MACxBC,cAAc,EAAE2D,QAAQ,CAAC5D;IAC3B,CAAC;IACDgB,mBAAmB,CAAEa,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEgC,OAAO,CAAC,CAAC;IACjDnD,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMsD,YAAY,GAAItE,EAAE,IAAK;IAC3BuE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAExE,EAAE,CAAC;IACxC;IACAE,gBAAgB,CAAEiC,IAAI,IAAKA,IAAI,CAAC4B,MAAM,CAAEjB,IAAI,IAAKA,IAAI,CAAC9C,EAAE,KAAKA,EAAE,CAAC,CAAC;EACnE,CAAC;;EAED;EACA,MAAMyE,WAAW,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACxB;IACA,MAAM/B,QAAQ,GAAG3C,aAAa,CAAC4C,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAClC,QAAQ,KAAKb,aAAa,CAAC;IAE5E,IAAI,CAAC6C,QAAQ,EAAE;MACbG,KAAK,CAAC,sCAAsC,CAAC;MAC7C;IACF;;IAEA;IACA,MAAM6B,qBAAqB,GAAG,CAAA9E,QAAQ,aAARA,QAAQ,wBAAA4E,qBAAA,GAAR5E,QAAQ,CAAE+E,WAAW,cAAAH,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAwB,CAAC,CAAC,cAAAC,sBAAA,uBAA1BA,sBAAA,CAA4BG,IAAI,KAAI,IAAI;;IAEtE;IACA,MAAMC,eAAe,GAAG9E,aAAa,CAAC8D,MAAM,CACzCjB,IAAI,IAAKA,IAAI,CAACrC,UAAU,KAAKmE,qBAAqB,IAAI9B,IAAI,CAAClC,QAAQ,KAAKb,aAC3E,CAAC;IAED,IAAIgF,eAAe,CAAC/B,MAAM,KAAK,CAAC,EAAE;MAChCD,KAAK,CAAC,0DAA0D,CAAC;MACjE;IACF;;IAEA;IACA,MAAMiC,SAAS,GAAG;MAChB7E,KAAK,EAAE,GAAGyE,qBAAqB,4BAA4B;MAC3DtB,KAAK,EAAEyB,eAAe;MACtBE,QAAQ,EAAE,GAAGnF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEoF,KAAK,IAAIpF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqF,KAAK,EAAE,IAAI,cAAc;MACnEC,QAAQ,EAAE,IAAIhB,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC;IACnC,CAAC;;IAED;IACA/C,SAAS,CAACC,SAAS,CAChBC,SAAS,CAAC8C,IAAI,CAACC,SAAS,CAACP,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAC7CQ,IAAI,CAAC,MAAM;MACVzC,KAAK,CACH,GAAGgC,eAAe,CAAC/B,MAAM,wBAAwB4B,qBAAqB,kCACxE,CAAC;IACH,CAAC,CAAC,CACDa,KAAK,CAAC,MAAM;MACX1C,KAAK,CAAC,gDAAgD,CAAC;IACzD,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAM2C,YAAY,GAAG,CACnB,wDAAwD,EACxD,wDAAwD,EACxD,wDAAwD,EACxD,wDAAwD,CACzD;EAED,oBACEnG,OAAA;IAAKoG,SAAS,EAAC,2BAA2B;IAAAC,QAAA,GACvCzE,aAAa,iBACZ5B,OAAA,CAAAE,SAAA;MAAAmG,QAAA,gBAEErG,OAAA;QAAKoG,SAAS,EAAC,iGAAiG;QAAAC,QAAA,gBAC9GrG,OAAA;UAAKoG,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CrG,OAAA;YAAIoG,SAAS,EAAC,4DAA4D;YAAAC,QAAA,EAAC;UAE3E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGLzG,OAAA;YACE0G,OAAO,EAAExB,WAAY;YACrBkB,SAAS,EAAC,+KAA+K;YAAAC,QAAA,eAEzLrG,OAAA;cAAMoG,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAGTzG,OAAA;YACE0G,OAAO,EAAEA,CAAA,KAAMtD,qBAAqB,CAAC,CAAE;YACvCgD,SAAS,EAAC,qGAAqG;YAC/GxF,KAAK,EAAC,qBAAqB;YAAAyF,QAAA,eAG3BrG,OAAA;cAAMoG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGTzG,OAAA;YAAKoG,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BF,YAAY,CAACvC,GAAG,CAAC,CAAC+C,MAAM,EAAEC,KAAK,kBAC9B5G,OAAA;cAEE6G,GAAG,EAAEF,MAAO;cACZG,GAAG,EAAE,QAAQF,KAAK,GAAG,CAAC,EAAG;cACzBR,SAAS,EAAC;YAAiE,GAHtEQ,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzG,OAAA;UAAKoG,SAAS,EAAC,0IAA0I;UAAAC,QAAA,eAEvJrG,OAAA;YACE0G,OAAO,EAAEA,CAAA,KAAM;cACbzE,eAAe,CAAC,IAAI,CAAC;cACrBR,cAAc,CAAC,CAACD,WAAW,CAAC;YAC9B,CAAE;YACF4E,SAAS,EAAE,ucACT5E,WAAW,GACP,uBAAuB,GACvB,qDAAqD,EACxD;YAAA6E,QAAA,gBAEHrG,OAAA;cAAMoG,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAE5D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLjF,WAAW,iBACVxB,OAAA,CAACL,mBAAmB;QAClBoH,QAAQ,EAAErC,qBAAsB;QAChCsC,QAAQ,EAAEA,CAAA,KAAM;UACdvF,cAAc,CAAC,KAAK,CAAC;UACrBQ,eAAe,CAAC,IAAI,CAAC;QACvB,CAAE;QACF7B,iBAAiB,EAAEA,iBAAkB;QACrCC,gBAAgB,EAAEA;MAAiB;QAAAiG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACF,eAGDzG,OAAA;QAAKoG,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnErG,OAAA;UAAOoG,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACvBrG,OAAA;YAAOoG,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eACpDrG,OAAA;cAAAqG,QAAA,gBACErG,OAAA;gBAAIoG,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,eACtCrG,OAAA;kBACES,EAAE,EAAC,cAAc;kBACjBwG,IAAI,EAAC,UAAU;kBACfC,OAAO,EACLhF,cAAc,CAACuB,MAAM,KAAK/C,aAAa,CAAC+C,MAAM,IAC9C/C,aAAa,CAAC+C,MAAM,GAAG,CACxB;kBACD0D,QAAQ,EAAExD,eAAgB;kBAC1ByC,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,eACnErG,OAAA;kBACEoG,SAAS,EAAC,4CAA4C;kBACtDM,OAAO,EAAEA,CAAA,KAAM7C,UAAU,CAAC,OAAO,CAAE;kBAAAwC,QAAA,gBAEnCrG,OAAA;oBAAAqG,QAAA,EAAM;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClBzG,OAAA;oBAAKoG,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BrG,OAAA;sBACEoG,SAAS,EAAE,WACThE,UAAU,CAACE,GAAG,KAAK,OAAO,IAC1BF,UAAU,CAACG,SAAS,KAAK,KAAK,GAC1B,eAAe,GACf,eAAe,EAClB;sBAAA8D,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPzG,OAAA;sBACEoG,SAAS,EAAE,WACThE,UAAU,CAACE,GAAG,KAAK,OAAO,IAC1BF,UAAU,CAACG,SAAS,KAAK,MAAM,GAC3B,eAAe,GACf,eAAe,EAClB;sBAAA8D,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,eACnErG,OAAA;kBACEoG,SAAS,EAAC,4CAA4C;kBACtDM,OAAO,EAAEA,CAAA,KAAM7C,UAAU,CAAC,UAAU,CAAE;kBAAAwC,QAAA,gBAEtCrG,OAAA;oBAAAqG,QAAA,EAAM;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtBzG,OAAA;oBAAKoG,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BrG,OAAA;sBACEoG,SAAS,EAAE,WACThE,UAAU,CAACE,GAAG,KAAK,UAAU,IAC7BF,UAAU,CAACG,SAAS,KAAK,KAAK,GAC1B,eAAe,GACf,eAAe,EAClB;sBAAA8D,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPzG,OAAA;sBACEoG,SAAS,EAAE,WACThE,UAAU,CAACE,GAAG,KAAK,UAAU,IAC7BF,UAAU,CAACG,SAAS,KAAK,MAAM,GAC3B,eAAe,GACf,eAAe,EAClB;sBAAA8D,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRzG,OAAA;YAAOoG,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EACxCvC,cAAc,CAACpD,aAAa,CAAC,CAACkD,GAAG,CAAEL,IAAI,iBACtCvD,OAAA;cAAkBoG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC5CrG,OAAA;gBAAIoG,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBrG,OAAA;kBACES,EAAE,EAAE,aAAa8C,IAAI,CAAC9C,EAAE,EAAG;kBAC3BwG,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAEhF,cAAc,CAACqC,QAAQ,CAAChB,IAAI,CAAC9C,EAAE,CAAE;kBAC1C0G,QAAQ,EAAEA,CAAA,KAAM7C,eAAe,CAACf,IAAI,CAAC9C,EAAE,CAAE;kBACzC2F,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBrG,OAAA;kBAAKoG,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCrG,OAAA;oBAAKoG,SAAS,EAAC,gGAAgG;oBAAAC,QAAA,EAAC;kBAEhH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,EACLzE,YAAY,KAAKuB,IAAI,CAAC9C,EAAE,gBACvBT,OAAA;oBACEiH,IAAI,EAAC,MAAM;oBACXG,YAAY,EAAE7D,IAAI,CAAC3C,KAAM;oBACzBwF,SAAS,EAAC,oDAAoD;oBAC9DiB,MAAM,EAAGC,CAAC,IAAK;sBACb3G,gBAAgB,CAAEiC,IAAI,IACpBA,IAAI,CAACgB,GAAG,CAAE2D,CAAC,IACTA,CAAC,CAAC9G,EAAE,KAAK8C,IAAI,CAAC9C,EAAE,GACZ;wBAAE,GAAG8G,CAAC;wBAAE3G,KAAK,EAAE0G,CAAC,CAACE,MAAM,CAACC;sBAAM,CAAC,GAC/BF,CACN,CACF,CAAC;sBACDtF,eAAe,CAAC,IAAI,CAAC;oBACvB,CAAE;oBACFyF,SAAS,EAAGJ,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAAChF,GAAG,KAAK,OAAO,EAAE;wBACrBgF,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC;sBACjB;oBACF,CAAE;oBACFC,SAAS;kBAAA;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,gBAEFzG,OAAA;oBAAMoG,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EACxC9C,IAAI,CAAC3C;kBAAK;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBrG,OAAA;kBAAKoG,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,GAC/BrE,YAAY,KAAKuB,IAAI,CAAC9C,EAAE,gBACvBT,OAAA;oBACEiH,IAAI,EAAC,MAAM;oBACXG,YAAY,EAAE7D,IAAI,CAACzC,QAAS;oBAC5BsF,SAAS,EAAC,wCAAwC;oBAClDiB,MAAM,EAAGC,CAAC,IAAK;sBACb3G,gBAAgB,CAAEiC,IAAI,IACpBA,IAAI,CAACgB,GAAG,CAAE2D,CAAC,IACTA,CAAC,CAAC9G,EAAE,KAAK8C,IAAI,CAAC9C,EAAE,GACZ;wBAAE,GAAG8G,CAAC;wBAAEzG,QAAQ,EAAEwG,CAAC,CAACE,MAAM,CAACC;sBAAM,CAAC,GAClCF,CACN,CACF,CAAC;oBACH,CAAE;oBACFG,SAAS,EAAGJ,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAAChF,GAAG,KAAK,OAAO,EAAE;wBACrBgF,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC;sBACjB;oBACF;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEFzG,OAAA;oBAAMoG,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAE9C,IAAI,CAACzC;kBAAQ;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACtD,eACDzG,OAAA;oBACE0G,OAAO,EAAEA,CAAA,KAAM7D,eAAe,CAACU,IAAI,CAACzC,QAAQ,CAAE;oBAC9CsF,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eAElDrG,OAAA;sBAAMoG,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBrG,OAAA;kBAAKoG,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCrG,OAAA;oBAAMoG,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EACjC/E,gBAAgB,CAACiC,IAAI,CAAC9C,EAAE,CAAC,GACtB8C,IAAI,CAACvC,cAAc,GACnBuC,IAAI,CAACxC;kBAAQ;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eACPzG,OAAA;oBACE0G,OAAO,EAAEA,CAAA,KAAM/D,wBAAwB,CAACY,IAAI,CAAC9C,EAAE,CAAE;oBACjD2F,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eAElDrG,OAAA;sBAAMoG,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC/C/E,gBAAgB,CAACiC,IAAI,CAAC9C,EAAE,CAAC,GACtB,gBAAgB,GAChB;oBAAY;sBAAA6F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACTzG,OAAA;oBACE0G,OAAO,EAAEA,CAAA,KAAM7D,eAAe,CAACU,IAAI,CAACvC,cAAc,CAAE;oBACpDoF,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,eAE7CrG,OAAA;sBAAMoG,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAE9C,IAAI,CAACtC;cAAI;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxDzG,OAAA;gBAAIoG,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EACpC9C,IAAI,CAACrC;cAAU;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBrG,OAAA;kBACEoG,SAAS,EAAE,8CAA8C7C,IAAI,CAACnC,aAAa,EAAG;kBAAAiF,QAAA,EAE7E9C,IAAI,CAACpC;gBAAQ;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBrG,OAAA;kBAAKoG,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CrG,OAAA;oBACE0G,OAAO,EAAEA,CAAA,KAAMxD,UAAU,CAACK,IAAI,CAAC9C,EAAE,CAAE;oBACnC2F,SAAS,EAAC,qDAAqD;oBAC/DxF,KAAK,EAAC,MAAM;oBAAAyF,QAAA,eAEZrG,OAAA;sBACE6H,KAAK,EAAC,IAAI;sBACVC,MAAM,EAAC,IAAI;sBACXC,OAAO,EAAC,WAAW;sBACnBC,IAAI,EAAC,MAAM;sBACXC,KAAK,EAAC,4BAA4B;sBAAA5B,QAAA,gBAElCrG,OAAA;wBACES,EAAE,EAAE,SAAS8C,IAAI,CAAC9C,EAAE,EAAG;wBACvByH,KAAK,EAAE;0BAAEC,QAAQ,EAAE;wBAAQ,CAAE;wBAC7BC,SAAS,EAAC,gBAAgB;wBAC1BC,CAAC,EAAC,GAAG;wBACLC,CAAC,EAAC,GAAG;wBACLT,KAAK,EAAC,IAAI;wBACVC,MAAM,EAAC,IAAI;wBAAAzB,QAAA,eAEXrG,OAAA;0BACEqI,CAAC,EAAC,KAAK;0BACPR,KAAK,EAAC,IAAI;0BACVC,MAAM,EAAC,IAAI;0BACXE,IAAI,EAAC;wBAAS;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACPzG,OAAA;wBAAGuI,IAAI,EAAE,cAAchF,IAAI,CAAC9C,EAAE,GAAI;wBAAA4F,QAAA,eAChCrG,OAAA;0BACEwI,CAAC,EAAC,qbAAqb;0BACvbR,IAAI,EAAC;wBAAc;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACTzG,OAAA;oBACE0G,OAAO,EAAEA,CAAA,KAAM3B,YAAY,CAACxB,IAAI,CAAC9C,EAAE,CAAE;oBACrC2F,SAAS,EAAC,oDAAoD;oBAC9DxF,KAAK,EAAC,QAAQ;oBAAAyF,QAAA,eAEdrG,OAAA;sBACE6H,KAAK,EAAC,IAAI;sBACVC,MAAM,EAAC,IAAI;sBACXC,OAAO,EAAC,WAAW;sBACnBC,IAAI,EAAC,MAAM;sBACXC,KAAK,EAAC,4BAA4B;sBAAA5B,QAAA,gBAElCrG,OAAA;wBACES,EAAE,EAAE,SAAS8C,IAAI,CAAC9C,EAAE,EAAG;wBACvByH,KAAK,EAAE;0BAAEC,QAAQ,EAAE;wBAAQ,CAAE;wBAC7BC,SAAS,EAAC,gBAAgB;wBAC1BC,CAAC,EAAC,GAAG;wBACLC,CAAC,EAAC,GAAG;wBACLT,KAAK,EAAC,IAAI;wBACVC,MAAM,EAAC,IAAI;wBAAAzB,QAAA,eAEXrG,OAAA;0BACEqI,CAAC,EAAC,KAAK;0BACPR,KAAK,EAAC,IAAI;0BACVC,MAAM,EAAC,IAAI;0BACXE,IAAI,EAAC;wBAAS;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACPzG,OAAA;wBAAGuI,IAAI,EAAE,cAAchF,IAAI,CAAC9C,EAAE,GAAI;wBAAA4F,QAAA,eAChCrG,OAAA;0BACEwI,CAAC,EAAC,soBAAsoB;0BACxoBR,IAAI,EAAC;wBAAc;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAhMElD,IAAI,CAAC9C,EAAE;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiMZ,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA,eACN,CACH,EAGA/E,YAAY,iBACX1B,OAAA;MAAKoG,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBrG,OAAA;QAAIoG,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EAAC;MAErD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLzG,OAAA;QAAKoG,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnErG,OAAA;UAAOoG,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACvBrG,OAAA;YAAOoG,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eACpDrG,OAAA;cAAAqG,QAAA,gBACErG,OAAA;gBAAIoG,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvDzG,OAAA;gBAAIoG,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,eACnErG,OAAA;kBACEoG,SAAS,EAAC,4CAA4C;kBACtDM,OAAO,EAAEA,CAAA,KAAM7C,UAAU,CAAC,OAAO,CAAE;kBAAAwC,QAAA,gBAEnCrG,OAAA;oBAAAqG,QAAA,EAAM;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClBzG,OAAA;oBAAKoG,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BrG,OAAA;sBACEoG,SAAS,EAAE,WACThE,UAAU,CAACE,GAAG,KAAK,OAAO,IAC1BF,UAAU,CAACG,SAAS,KAAK,KAAK,GAC1B,eAAe,GACf,eAAe,EAClB;sBAAA8D,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPzG,OAAA;sBACEoG,SAAS,EAAE,WACThE,UAAU,CAACE,GAAG,KAAK,OAAO,IAC1BF,UAAU,CAACG,SAAS,KAAK,MAAM,GAC3B,eAAe,GACf,eAAe,EAClB;sBAAA8D,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,eACnErG,OAAA;kBACEoG,SAAS,EAAC,4CAA4C;kBACtDM,OAAO,EAAEA,CAAA,KAAM7C,UAAU,CAAC,UAAU,CAAE;kBAAAwC,QAAA,gBAEtCrG,OAAA;oBAAAqG,QAAA,EAAM;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtBzG,OAAA;oBAAKoG,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BrG,OAAA;sBACEoG,SAAS,EAAE,WACThE,UAAU,CAACE,GAAG,KAAK,UAAU,IAC7BF,UAAU,CAACG,SAAS,KAAK,KAAK,GAC1B,eAAe,GACf,eAAe,EAClB;sBAAA8D,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPzG,OAAA;sBACEoG,SAAS,EAAE,WACThE,UAAU,CAACE,GAAG,KAAK,UAAU,IAC7BF,UAAU,CAACG,SAAS,KAAK,MAAM,GAC3B,eAAe,GACf,eAAe,EAClB;sBAAA8D,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRzG,OAAA;YAAOoG,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EACxCvE,gBAAgB,CAAC2B,MAAM,KAAK,CAAC,gBAC5BzD,OAAA;cAAAqG,QAAA,eACErG,OAAA;gBACEyI,OAAO,EAAC,GAAG;gBACXrC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAChD;cAGD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GAEL3C,cAAc,CAAChC,gBAAgB,CAAC,CAAC8B,GAAG,CAAEL,IAAI,iBACxCvD,OAAA;cAAkBoG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC5CrG,OAAA;gBAAIoG,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBrG,OAAA;kBACEiH,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAEhF,cAAc,CAACqC,QAAQ,CAAChB,IAAI,CAAC9C,EAAE,CAAE;kBAC1C0G,QAAQ,EAAEA,CAAA,KAAM7C,eAAe,CAACf,IAAI,CAAC9C,EAAE,CAAE;kBACzC2F,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBrG,OAAA;kBAAKoG,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCrG,OAAA;oBAAKoG,SAAS,EAAC,gGAAgG;oBAAAC,QAAA,EAAC;kBAEhH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNzG,OAAA;oBAAMoG,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EACxC9C,IAAI,CAAC3C;kBAAK;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EACpC9C,IAAI,CAACzC;cAAQ;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBrG,OAAA;kBAAKoG,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCrG,OAAA;oBAAMoG,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EACjC9C,IAAI,CAACxC;kBAAQ;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACPzG,OAAA;oBACE0G,OAAO,EAAEA,CAAA,KAAM7D,eAAe,CAACU,IAAI,CAACvC,cAAc,CAAE;oBACpDoF,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,eAE7CrG,OAAA;sBAAMoG,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAE9C,IAAI,CAACtC;cAAI;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxDzG,OAAA;gBAAIoG,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EACpC9C,IAAI,CAACrC;cAAU;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBrG,OAAA;kBACEoG,SAAS,EAAE,8CAA8C7C,IAAI,CAACnC,aAAa,EAAG;kBAAAiF,QAAA,EAE7E9C,IAAI,CAACpC;gBAAQ;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBrG,OAAA;kBAAKoG,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CrG,OAAA;oBACE0G,OAAO,EAAEA,CAAA,KAAMxD,UAAU,CAACK,IAAI,CAAC9C,EAAE,CAAE;oBACnC2F,SAAS,EAAC,qDAAqD;oBAC/DxF,KAAK,EAAC,MAAM;oBAAAyF,QAAA,eAEZrG,OAAA;sBAAMoG,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACTzG,OAAA;oBACE0G,OAAO,EAAEA,CAAA,KACP3E,mBAAmB,CAAEa,IAAI,IACvBA,IAAI,CAAC4B,MAAM,CAAE+C,CAAC,IAAKA,CAAC,CAAC9G,EAAE,KAAK8C,IAAI,CAAC9C,EAAE,CACrC,CACD;oBACD2F,SAAS,EAAC,oDAAoD;oBAC9DxF,KAAK,EAAC,QAAQ;oBAAAyF,QAAA,eAEdrG,OAAA;sBAAMoG,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAzEElD,IAAI,CAAC9C,EAAE;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0EZ,CACL;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDzG,OAAA;MAAKoG,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvCrG,OAAA;QACE0G,OAAO,EAAEvD,oBAAqB;QAC9BiD,SAAS,EAAC,0MAA0M;QAAAC,QAAA,gBAEpNrG,OAAA;UAAMoG,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,yBAE5D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnG,EAAA,CAr0BIH,kBAAkB;EAAA,QA6GGN,yBAAyB;AAAA;AAAA6I,EAAA,GA7G9CvI,kBAAkB;AAu0BxB,eAAeA,kBAAkB;AAAC,IAAAuI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}