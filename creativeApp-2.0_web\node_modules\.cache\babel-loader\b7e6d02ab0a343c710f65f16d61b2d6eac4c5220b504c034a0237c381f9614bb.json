{"ast": null, "code": "import React,{useState}from\"react\";import AddPasswordCardForm from\"./AddPasswordCardForm\";import{confirmationAlert}from\"../../common/coreui\";import FetchLoggedInRole from\"../../common/fetchData/FetchLoggedInRole\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const PasswordCardsTable=_ref=>{let{generatedPassword,passwordStrength}=_ref;// Get current user data\nconst{userData}=FetchLoggedInRole();const currentUserId=userData===null||userData===void 0?void 0:userData.id;// Sample data - in real app this would come from API/state management\nconst[passwordCards,setPasswordCards]=useState([{id:1,title:\"Gmail Account\",platform:\"Gmail\",username:\"<EMAIL>\",password:\"••••••••••••\",actualPassword:\"xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn\",team:\"Team Name\",department:\"IT\",strength:\"Weak Password\",strengthColor:\"bg-red-100 text-red-600 border-red-300\",authorId:currentUserId// Add author ID\n},{id:2,title:\"Slack Workspace\",platform:\"Slack\",username:\"<EMAIL>\",password:\"••••••••••••\",actualPassword:\"StrongPass123!@#\",team:\"Team Name\",department:\"IT\",strength:\"Strong Password\",strengthColor:\"bg-green-100 text-green-600 border-green-300\",authorId:currentUserId},{id:3,title:\"GitHub Repository\",platform:\"GitHub\",username:\"<EMAIL>\",password:\"••••••••••••\",actualPassword:\"ModeratePass456\",team:\"Team Name\",department:\"Development\",strength:\"Moderate Password\",strengthColor:\"bg-yellow-100 text-yellow-600 border-yellow-300\",authorId:999// Different author ID to test permissions\n},{id:4,title:\"AWS Console\",platform:\"AWS\",username:\"<EMAIL>\",password:\"••••••••••••\",actualPassword:\"WeakPass\",team:\"Team Name\",department:\"DevOps\",strength:\"Weak Password\",strengthColor:\"bg-red-100 text-red-600 border-red-300\",authorId:currentUserId},{id:5,title:\"Jira Project\",platform:\"Jira\",username:\"<EMAIL>\",password:\"••••••••••••\",actualPassword:\"AnotherStrongPass789!\",team:\"Team Name\",department:\"Project Management\",strength:\"Strong Password\",strengthColor:\"bg-green-100 text-green-600 border-green-300\",authorId:998// Different author ID to test permissions\n},{id:6,title:\"Office 365\",platform:\"Microsoft 365\",username:\"<EMAIL>\",password:\"••••••••••••\",actualPassword:\"ModerateSecure123\",team:\"Team Name\",department:\"HR\",strength:\"Moderate Password\",strengthColor:\"bg-yellow-100 text-yellow-600 border-yellow-300\",authorId:currentUserId},{id:7,title:\"Database Admin\",platform:\"MySQL\",username:\"<EMAIL>\",password:\"••••••••••••\",actualPassword:\"VeryWeakPass\",team:\"Team Name\",department:\"Database\",strength:\"Weak Password\",strengthColor:\"bg-red-100 text-red-600 border-red-300\",authorId:currentUserId}]);const[visiblePasswords,setVisiblePasswords]=useState({});const[showAddForm,setShowAddForm]=useState(false);const[showNewTable,setShowNewTable]=useState(false);const[showTeamTable]=useState(true);const[newPasswordCards,setNewPasswordCards]=useState([]);const[editingRowId,setEditingRowId]=useState(null);const[shareableCards,setShareableCards]=useState([]);const[sortConfig,setSortConfig]=useState({key:null,direction:\"asc\"});const togglePasswordVisibility=id=>{setVisiblePasswords(prev=>({...prev,[id]:!prev[id]}));};const copyToClipboard=text=>{navigator.clipboard.writeText(text);// You could add a toast notification here\n};// Handle individual row editing\nconst handleEdit=id=>{setEditingRowId(editingRowId===id?null:id);};// Handle creating new table\nconst handleCreateNewTable=()=>{setShowNewTable(true);};// Handle delete entire team table - only author can delete\nconst handleDeleteTeamTable=()=>{// Check if current user is author of any cards\nconst isAuthor=passwordCards.some(card=>card.authorId===currentUserId);if(!isAuthor){alert(\"Only the author can delete the entire table.\");return;}// Only proceed if there are selected cards or if user is author\nif(shareableCards.length===0){alert(\"Please select at least one row to delete the table.\");return;}confirmationAlert({onConfirm:()=>{setPasswordCards([]);}});};// Handle select all checkboxes password and\nconst toggleSelectAll=()=>{if(shareableCards.length===passwordCards.length){setShareableCards([]);}else{setShareableCards(passwordCards.map(card=>card.id));}};// Handle sorting\nconst handleSort=key=>{let direction=\"asc\";if(sortConfig.key===key&&sortConfig.direction===\"asc\"){direction=\"desc\";}setSortConfig({key,direction});};// Sort cards based on current sort config\nconst getSortedCards=cards=>{if(!sortConfig.key)return cards;return[...cards].sort((a,b)=>{const aValue=a[sortConfig.key].toLowerCase();const bValue=b[sortConfig.key].toLowerCase();if(sortConfig.direction===\"asc\"){return aValue<bValue?-1:aValue>bValue?1:0;}else{return aValue>bValue?-1:aValue<bValue?1:0;}});};// Handle shareable toggle\nconst toggleShareable=id=>{setShareableCards(prev=>prev.includes(id)?prev.filter(cardId=>cardId!==id):[...prev,id]);};// Handle form submission for new password cards\nconst handleAddPasswordCard=cardData=>{const newCard={...cardData,id:Date.now(),password:\"••••••••••••\",actualPassword:cardData.password,authorId:currentUserId// Add current user as author\n};setNewPasswordCards(prev=>[...prev,newCard]);setShowAddForm(false);};const handleDelete=id=>{confirmationAlert({onConfirm:()=>{setPasswordCards(prev=>prev.filter(card=>card.id!==id));}});};// Handle share functionality for department - only author can share\nconst handleShare=()=>{var _userData$departments,_userData$departments2;// Check if current user is author of any cards\nconst isAuthor=passwordCards.some(card=>card.authorId===currentUserId);if(!isAuthor){alert(\"Only the author can share the table.\");return;}// Get current user's department or team\nconst currentUserDepartment=(userData===null||userData===void 0?void 0:(_userData$departments=userData.departments)===null||_userData$departments===void 0?void 0:(_userData$departments2=_userData$departments[0])===null||_userData$departments2===void 0?void 0:_userData$departments2.name)||\"IT\";// Filter cards that belong to the same department and are authored by current user\nconst departmentCards=passwordCards.filter(card=>card.department===currentUserDepartment&&card.authorId===currentUserId);if(departmentCards.length===0){alert(\"No password cards available to share in your department.\");return;}// Create shareable data\nconst shareData={title:`${currentUserDepartment} Department Password Cards`,cards:departmentCards,sharedBy:`${userData===null||userData===void 0?void 0:userData.fname} ${userData===null||userData===void 0?void 0:userData.lname}`||\"Current User\",sharedAt:new Date().toISOString()};// For now, copy to clipboard (you can implement actual sharing logic)\nnavigator.clipboard.writeText(JSON.stringify(shareData,null,2)).then(()=>{alert(`${departmentCards.length} password cards from ${currentUserDepartment} department copied to clipboard!`);}).catch(()=>{alert(\"Failed to copy to clipboard. Please try again.\");});};// Placeholder avatar images\nconst avatarImages=[\"https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=A\",\"https://via.placeholder.com/32x32/10B981/FFFFFF?text=B\",\"https://via.placeholder.com/32x32/F59E0B/FFFFFF?text=C\",\"https://via.placeholder.com/32x32/EF4444/FFFFFF?text=D\"];return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white dark:bg-gray-900\",children:[showTeamTable&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-left text-2xl font-bold text-gray-900 dark:text-white\",children:\"Teams Password Card\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleShare,className:\"flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-rounded\",children:\"share\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteTeamTable(),className:\"flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg\",title:\"Delete entire table\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-sm\",children:\"delete\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex -space-x-2\",children:avatarImages.map((avatar,index)=>/*#__PURE__*/_jsx(\"img\",{src:avatar,alt:`User ${index+1}`,className:\"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"},index))})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{setEditingRowId(null);setShowAddForm(!showAddForm);},className:`w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${showAddForm?\"bg-primary text-white\":\"bg-transparent text-primary border-2 border-primary\"}`,children:[/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-rounded mr-2\",children:\"add\"}),\"Add Password\"]})})]}),showAddForm&&/*#__PURE__*/_jsx(AddPasswordCardForm,{onSubmit:handleAddPasswordCard,onCancel:()=>{setShowAddForm(false);setEditingRowId(null);},generatedPassword:generatedPassword,passwordStrength:passwordStrength}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm border border-gray-200\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"w-full\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50 border-b border-gray-200\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"w-12 px-4 py-3 text-left\",children:/*#__PURE__*/_jsx(\"input\",{id:\"checkbox-all\",type:\"checkbox\",checked:shareableCards.length===passwordCards.length&&passwordCards.length>0,onChange:toggleSelectAll,className:\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500\"})}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-sm font-medium text-gray-700\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 cursor-pointer\",onClick:()=>handleSort(\"title\"),children:[/*#__PURE__*/_jsx(\"span\",{children:\"Title\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:`text-xs ${sortConfig.key===\"title\"&&sortConfig.direction===\"asc\"?\"text-blue-600\":\"text-gray-400\"}`,children:\"\\u25B2\"}),/*#__PURE__*/_jsx(\"span\",{className:`text-xs ${sortConfig.key===\"title\"&&sortConfig.direction===\"desc\"?\"text-blue-600\":\"text-gray-400\"}`,children:\"\\u25BC\"})]})]})}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-sm font-medium text-gray-700\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 cursor-pointer\",onClick:()=>handleSort(\"username\"),children:[/*#__PURE__*/_jsx(\"span\",{children:\"User Name\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:`text-xs ${sortConfig.key===\"username\"&&sortConfig.direction===\"asc\"?\"text-blue-600\":\"text-gray-400\"}`,children:\"\\u25B2\"}),/*#__PURE__*/_jsx(\"span\",{className:`text-xs ${sortConfig.key===\"username\"&&sortConfig.direction===\"desc\"?\"text-blue-600\":\"text-gray-400\"}`,children:\"\\u25BC\"})]})]})}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-sm font-medium text-gray-700\",children:\"Password\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-sm font-medium text-gray-700\",children:\"Team\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-sm font-medium text-gray-700\",children:\"Department\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-sm font-medium text-gray-700\",children:\"Level\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-sm font-medium text-gray-700\",children:\"Action\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"divide-y divide-gray-200\",children:getSortedCards(passwordCards).map(card=>/*#__PURE__*/_jsxs(\"tr\",{className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-4 py-4\",children:/*#__PURE__*/_jsx(\"input\",{id:`shareable-${card.id}`,type:\"checkbox\",checked:shareableCards.includes(card.id),onChange:()=>toggleShareable(card.id),className:\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\",children:\"TG\"}),editingRowId===card.id?/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:card.title,className:\"font-medium text-gray-900 border rounded px-2 py-1 w-full\",onBlur:e=>{setPasswordCards(prev=>prev.map(c=>c.id===card.id?{...c,title:e.target.value}:c));},onKeyDown:e=>{if(e.key===\"Enter\"){e.target.blur();}},autoFocus:true}):/*#__PURE__*/_jsx(\"span\",{className:\"font-medium text-gray-900\",children:card.title})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[editingRowId===card.id?/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:card.username,className:\"text-gray-900 border rounded px-2 py-1 w-full mr-2\",onBlur:e=>{setPasswordCards(prev=>prev.map(c=>c.id===card.id?{...c,username:e.target.value}:c));},onKeyDown:e=>{if(e.key===\"Enter\"){e.target.blur();}}}):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900\",children:card.username}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>copyToClipboard(card.username),className:\"ml-2 text-gray-400 hover:text-gray-600\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-rounded text-sm\",children:\"content_copy\"})})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[editingRowId===card.id?/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:card.actualPassword,className:\"text-gray-900 border rounded px-2 py-1 w-full mr-2\",onBlur:e=>{setPasswordCards(prev=>prev.map(c=>c.id===card.id?{...c,actualPassword:e.target.value}:c));},onKeyDown:e=>{if(e.key===\"Enter\"){e.target.blur();}}}):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900 mr-2\",children:visiblePasswords[card.id]?card.actualPassword:card.password}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>togglePasswordVisibility(card.id),className:\"text-gray-400 hover:text-gray-600 mr-2\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-rounded text-sm\",children:visiblePasswords[card.id]?\"visibility_off\":\"visibility\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>copyToClipboard(card.actualPassword),className:\"text-gray-400 hover:text-gray-600\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-rounded text-sm\",children:\"content_copy\"})})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4\",children:editingRowId===card.id?/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:card.team,className:\"text-gray-900 border rounded px-2 py-1 w-full\",onBlur:e=>{setPasswordCards(prev=>prev.map(c=>c.id===card.id?{...c,team:e.target.value}:c));},onKeyDown:e=>{if(e.key===\"Enter\"){e.target.blur();}}}):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900\",children:card.team})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4\",children:editingRowId===card.id?/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:card.department,className:\"text-gray-900 border rounded px-2 py-1 w-full\",onBlur:e=>{setPasswordCards(prev=>prev.map(c=>c.id===card.id?{...c,department:e.target.value}:c));},onKeyDown:e=>{if(e.key===\"Enter\"){e.target.blur();}}}):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900\",children:card.department})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4\",children:/*#__PURE__*/_jsx(\"span\",{className:`px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`,children:card.strength})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleEdit(card.id),className:\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",title:\"Edit\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-lg\",children:\"stylus_note\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDelete(card.id),className:\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",title:\"Delete\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-sm\",children:\"delete\"})})]})})]},card.id))})]})})]}),showNewTable&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-left text-2xl font-bold text-gray-900 dark:text-white\",children:\"Teams Password Card\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleShare,className:\"flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-rounded\",children:\"share\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteTeamTable(),className:\"flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg\",title:\"Delete entire table\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-sm\",children:\"delete\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex -space-x-2\",children:avatarImages.map((avatar,index)=>/*#__PURE__*/_jsx(\"img\",{src:avatar,alt:`User ${index+1}`,className:\"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"},index))})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{setEditingRowId(null);setShowAddForm(!showAddForm);},className:`w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${showAddForm?\"bg-primary text-white\":\"bg-transparent text-primary border-2 border-primary\"}`,children:[/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-rounded mr-2\",children:\"add\"}),\"Add Password\"]})})]}),showAddForm&&/*#__PURE__*/_jsx(AddPasswordCardForm,{onSubmit:handleAddPasswordCard,onCancel:()=>{setShowAddForm(false);setEditingRowId(null);},generatedPassword:generatedPassword,passwordStrength:passwordStrength}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm border border-gray-200\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"w-full\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50 border-b border-gray-200\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"w-12 px-4 py-3 text-left\",children:/*#__PURE__*/_jsx(\"input\",{id:\"checkbox-all-new\",type:\"checkbox\",checked:shareableCards.length===newPasswordCards.length&&newPasswordCards.length>0,onChange:()=>{if(shareableCards.length===newPasswordCards.length){setShareableCards([]);}else{setShareableCards(newPasswordCards.map(card=>card.id));}},className:\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"})}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-sm font-medium text-gray-700\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 cursor-pointer\",onClick:()=>handleSort(\"title\"),children:[/*#__PURE__*/_jsx(\"span\",{children:\"Title\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:`text-xs ${sortConfig.key===\"title\"&&sortConfig.direction===\"asc\"?\"text-blue-600\":\"text-gray-400\"}`,children:\"\\u25B2\"}),/*#__PURE__*/_jsx(\"span\",{className:`text-xs ${sortConfig.key===\"title\"&&sortConfig.direction===\"desc\"?\"text-blue-600\":\"text-gray-400\"}`,children:\"\\u25BC\"})]})]})}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-sm font-medium text-gray-700\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 cursor-pointer\",onClick:()=>handleSort(\"username\"),children:[/*#__PURE__*/_jsx(\"span\",{children:\"User Name\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:`text-xs ${sortConfig.key===\"username\"&&sortConfig.direction===\"asc\"?\"text-blue-600\":\"text-gray-400\"}`,children:\"\\u25B2\"}),/*#__PURE__*/_jsx(\"span\",{className:`text-xs ${sortConfig.key===\"username\"&&sortConfig.direction===\"desc\"?\"text-blue-600\":\"text-gray-400\"}`,children:\"\\u25BC\"})]})]})}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-sm font-medium text-gray-700\",children:\"Password\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-sm font-medium text-gray-700\",children:\"Team\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-sm font-medium text-gray-700\",children:\"Department\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-sm font-medium text-gray-700\",children:\"Level\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-sm font-medium text-gray-700\",children:\"Action\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"divide-y divide-gray-200\",children:newPasswordCards.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"8\",className:\"px-6 py-8 text-center text-gray-500\",children:\"No password cards added yet. Click \\\"Add Password\\\" to add your first card.\"})}):getSortedCards(newPasswordCards).map(card=>/*#__PURE__*/_jsxs(\"tr\",{className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-4 py-4\",children:/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:shareableCards.includes(card.id),onChange:()=>toggleShareable(card.id),className:\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\",children:\"TG\"}),editingRowId===card.id?/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:card.title,className:\"font-medium text-gray-900 border rounded px-2 py-1 w-full\",onBlur:e=>{setNewPasswordCards(prev=>prev.map(c=>c.id===card.id?{...c,title:e.target.value}:c));},onKeyDown:e=>{if(e.key===\"Enter\"){e.target.blur();}},autoFocus:true}):/*#__PURE__*/_jsx(\"span\",{className:\"font-medium text-gray-900\",children:card.title})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[editingRowId===card.id?/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:card.username,className:\"text-gray-900 border rounded px-2 py-1 w-full mr-2\",onBlur:e=>{setNewPasswordCards(prev=>prev.map(c=>c.id===card.id?{...c,username:e.target.value}:c));},onKeyDown:e=>{if(e.key===\"Enter\"){e.target.blur();}}}):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900\",children:card.username}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>copyToClipboard(card.username),className:\"ml-2 text-gray-400 hover:text-gray-600\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-rounded text-sm\",children:\"content_copy\"})})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[editingRowId===card.id?/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:card.actualPassword,className:\"text-gray-900 border rounded px-2 py-1 w-full mr-2\",onBlur:e=>{setNewPasswordCards(prev=>prev.map(c=>c.id===card.id?{...c,actualPassword:e.target.value}:c));},onKeyDown:e=>{if(e.key===\"Enter\"){e.target.blur();}}}):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900 mr-2\",children:visiblePasswords[card.id]?card.actualPassword:card.password}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>togglePasswordVisibility(card.id),className:\"text-gray-400 hover:text-gray-600 mr-2\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-rounded text-sm\",children:visiblePasswords[card.id]?\"visibility_off\":\"visibility\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>copyToClipboard(card.actualPassword),className:\"text-gray-400 hover:text-gray-600\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-rounded text-sm\",children:\"content_copy\"})})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4\",children:editingRowId===card.id?/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:card.team,className:\"text-gray-900 border rounded px-2 py-1 w-full\",onBlur:e=>{setNewPasswordCards(prev=>prev.map(c=>c.id===card.id?{...c,team:e.target.value}:c));},onKeyDown:e=>{if(e.key===\"Enter\"){e.target.blur();}}}):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900\",children:card.team})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4\",children:editingRowId===card.id?/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:card.department,className:\"text-gray-900 border rounded px-2 py-1 w-full\",onBlur:e=>{setNewPasswordCards(prev=>prev.map(c=>c.id===card.id?{...c,department:e.target.value}:c));},onKeyDown:e=>{if(e.key===\"Enter\"){e.target.blur();}}}):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900\",children:card.department})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4\",children:/*#__PURE__*/_jsx(\"span\",{className:`px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`,children:card.strength})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleEdit(card.id),className:\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",title:\"Edit\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-lg\",children:\"stylus_note\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{confirmationAlert({onConfirm:()=>{setNewPasswordCards(prev=>prev.filter(c=>c.id!==card.id));}});},className:\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",title:\"Delete\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-sm\",children:\"delete\"})})]})})]},card.id))})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center mt-6\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:handleCreateNewTable,className:\"flex items-center justify-center px-6 py-2 text-sm font-medium text-white rounded-lg bg-primary hover:bg-primary/90 focus:ring-4 focus:ring-primary/30 transition-colors duration-200 focus:outline-none\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-rounded mr-2\",children:\"add\"}),\"Add New Password Card\"]})})]});};export default PasswordCardsTable;", "map": {"version": 3, "names": ["React", "useState", "AddPasswordCardForm", "<PERSON><PERSON><PERSON><PERSON>", "FetchLoggedInRole", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "PasswordCardsTable", "_ref", "generatedPassword", "passwordStrength", "userData", "currentUserId", "id", "passwordCards", "setPasswordCards", "title", "platform", "username", "password", "actualPassword", "team", "department", "strength", "strengthColor", "authorId", "visiblePasswords", "setVisiblePasswords", "showAddForm", "setShowAddForm", "showNewTable", "setShowNewTable", "showTeamTable", "newPasswordCards", "setNewPasswordCards", "editingRowId", "setEditingRowId", "shareableCards", "setShareableCards", "sortConfig", "setSortConfig", "key", "direction", "togglePasswordVisibility", "prev", "copyToClipboard", "text", "navigator", "clipboard", "writeText", "handleEdit", "handleCreateNewTable", "handleDeleteTeamTable", "is<PERSON><PERSON><PERSON>", "some", "card", "alert", "length", "onConfirm", "toggleSelectAll", "map", "handleSort", "getSortedCards", "cards", "sort", "a", "b", "aValue", "toLowerCase", "bValue", "toggleShareable", "includes", "filter", "cardId", "handleAddPasswordCard", "cardData", "newCard", "Date", "now", "handleDelete", "handleShare", "_userData$departments", "_userData$departments2", "currentUserDepartment", "departments", "name", "departmentCards", "shareData", "sharedBy", "fname", "lname", "sharedAt", "toISOString", "JSON", "stringify", "then", "catch", "avatarImages", "className", "children", "onClick", "avatar", "index", "src", "alt", "onSubmit", "onCancel", "type", "checked", "onChange", "defaultValue", "onBlur", "e", "c", "target", "value", "onKeyDown", "blur", "autoFocus", "colSpan"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/PasswordCardsTable.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport AddPasswordCardForm from \"./AddPasswordCardForm\";\nimport { confirmationAlert } from \"../../common/coreui\";\nimport FetchLoggedInRole from \"../../common/fetchData/FetchLoggedInRole\";\n\nconst PasswordCardsTable = ({ generatedPassword, passwordStrength }) => {\n  // Get current user data\n  const { userData } = FetchLoggedInRole();\n  const currentUserId = userData?.id;\n\n  // Sample data - in real app this would come from API/state management\n  const [passwordCards, setPasswordCards] = useState([\n    {\n      id: 1,\n      title: \"Gmail Account\",\n      platform: \"Gmail\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn\",\n      team: \"Team Name\",\n      department: \"IT\",\n      strength: \"Weak Password\",\n      strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n      authorId: currentUserId, // Add author ID\n    },\n    {\n      id: 2,\n      title: \"Slack Workspace\",\n      platform: \"Slack\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"StrongPass123!@#\",\n      team: \"Team Name\",\n      department: \"IT\",\n      strength: \"Strong Password\",\n      strengthColor: \"bg-green-100 text-green-600 border-green-300\",\n      authorId: currentUserId,\n    },\n    {\n      id: 3,\n      title: \"GitHub Repository\",\n      platform: \"GitHub\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"ModeratePass456\",\n      team: \"Team Name\",\n      department: \"Development\",\n      strength: \"Moderate Password\",\n      strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\",\n      authorId: 999, // Different author ID to test permissions\n    },\n    {\n      id: 4,\n      title: \"AWS Console\",\n      platform: \"AWS\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"WeakPass\",\n      team: \"Team Name\",\n      department: \"DevOps\",\n      strength: \"Weak Password\",\n      strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n      authorId: currentUserId,\n    },\n    {\n      id: 5,\n      title: \"Jira Project\",\n      platform: \"Jira\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"AnotherStrongPass789!\",\n      team: \"Team Name\",\n      department: \"Project Management\",\n      strength: \"Strong Password\",\n      strengthColor: \"bg-green-100 text-green-600 border-green-300\",\n      authorId: 998, // Different author ID to test permissions\n    },\n    {\n      id: 6,\n      title: \"Office 365\",\n      platform: \"Microsoft 365\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"ModerateSecure123\",\n      team: \"Team Name\",\n      department: \"HR\",\n      strength: \"Moderate Password\",\n      strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\",\n      authorId: currentUserId,\n    },\n    {\n      id: 7,\n      title: \"Database Admin\",\n      platform: \"MySQL\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"VeryWeakPass\",\n      team: \"Team Name\",\n      department: \"Database\",\n      strength: \"Weak Password\",\n      strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n      authorId: currentUserId,\n    },\n  ]);\n\n  const [visiblePasswords, setVisiblePasswords] = useState({});\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [showNewTable, setShowNewTable] = useState(false);\n  const [showTeamTable] = useState(true);\n  const [newPasswordCards, setNewPasswordCards] = useState([]);\n  const [editingRowId, setEditingRowId] = useState(null);\n  const [shareableCards, setShareableCards] = useState([]);\n  const [sortConfig, setSortConfig] = useState({ key: null, direction: \"asc\" });\n  const togglePasswordVisibility = (id) => {\n    setVisiblePasswords((prev) => ({\n      ...prev,\n      [id]: !prev[id],\n    }));\n  };\n\n  const copyToClipboard = (text) => {\n    navigator.clipboard.writeText(text);\n    // You could add a toast notification here\n  };\n\n  // Handle individual row editing\n  const handleEdit = (id) => {\n    setEditingRowId(editingRowId === id ? null : id);\n  };\n\n  // Handle creating new table\n  const handleCreateNewTable = () => {\n    setShowNewTable(true);\n  };\n\n  // Handle delete entire team table - only author can delete\n  const handleDeleteTeamTable = () => {\n    // Check if current user is author of any cards\n    const isAuthor = passwordCards.some(card => card.authorId === currentUserId);\n\n    if (!isAuthor) {\n      alert(\"Only the author can delete the entire table.\");\n      return;\n    }\n\n    // Only proceed if there are selected cards or if user is author\n    if (shareableCards.length === 0) {\n      alert(\"Please select at least one row to delete the table.\");\n      return;\n    }\n\n    confirmationAlert({\n      onConfirm: () => {\n        setPasswordCards([]);\n      },\n    });\n  };\n\n  // Handle select all checkboxes password and\n  \n  const toggleSelectAll = () => {\n    if (shareableCards.length === passwordCards.length) {\n      setShareableCards([]);\n    } else {\n      setShareableCards(passwordCards.map((card) => card.id));\n    }\n  };\n\n  // Handle sorting\n  const handleSort = (key) => {\n    let direction = \"asc\";\n    if (sortConfig.key === key && sortConfig.direction === \"asc\") {\n      direction = \"desc\";\n    }\n    setSortConfig({ key, direction });\n  };\n\n  // Sort cards based on current sort config\n  const getSortedCards = (cards) => {\n    if (!sortConfig.key) return cards;\n\n    return [...cards].sort((a, b) => {\n      const aValue = a[sortConfig.key].toLowerCase();\n      const bValue = b[sortConfig.key].toLowerCase();\n\n      if (sortConfig.direction === \"asc\") {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n  };\n\n  // Handle shareable toggle\n  const toggleShareable = (id) => {\n    setShareableCards((prev) =>\n      prev.includes(id) ? prev.filter((cardId) => cardId !== id) : [...prev, id]\n    );\n  };\n\n  // Handle form submission for new password cards\n  const handleAddPasswordCard = (cardData) => {\n    const newCard = {\n      ...cardData,\n      id: Date.now(),\n      password: \"••••••••••••\",\n      actualPassword: cardData.password,\n      authorId: currentUserId, // Add current user as author\n    };\n    setNewPasswordCards((prev) => [...prev, newCard]);\n    setShowAddForm(false);\n  };\n\n  const handleDelete = (id) => {\n    confirmationAlert({\n      onConfirm: () => {\n        setPasswordCards((prev) => prev.filter((card) => card.id !== id));\n      },\n    });\n  };\n\n  // Handle share functionality for department - only author can share\n  const handleShare = () => {\n    // Check if current user is author of any cards\n    const isAuthor = passwordCards.some(card => card.authorId === currentUserId);\n\n    if (!isAuthor) {\n      alert(\"Only the author can share the table.\");\n      return;\n    }\n\n    // Get current user's department or team\n    const currentUserDepartment = userData?.departments?.[0]?.name || \"IT\";\n\n    // Filter cards that belong to the same department and are authored by current user\n    const departmentCards = passwordCards.filter(\n      (card) => card.department === currentUserDepartment && card.authorId === currentUserId\n    );\n\n    if (departmentCards.length === 0) {\n      alert(\"No password cards available to share in your department.\");\n      return;\n    }\n\n    // Create shareable data\n    const shareData = {\n      title: `${currentUserDepartment} Department Password Cards`,\n      cards: departmentCards,\n      sharedBy: `${userData?.fname} ${userData?.lname}` || \"Current User\",\n      sharedAt: new Date().toISOString(),\n    };\n\n    // For now, copy to clipboard (you can implement actual sharing logic)\n    navigator.clipboard\n      .writeText(JSON.stringify(shareData, null, 2))\n      .then(() => {\n        alert(\n          `${departmentCards.length} password cards from ${currentUserDepartment} department copied to clipboard!`\n        );\n      })\n      .catch(() => {\n        alert(\"Failed to copy to clipboard. Please try again.\");\n      });\n  };\n\n  // Placeholder avatar images\n  const avatarImages = [\n    \"https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=A\",\n    \"https://via.placeholder.com/32x32/10B981/FFFFFF?text=B\",\n    \"https://via.placeholder.com/32x32/F59E0B/FFFFFF?text=C\",\n    \"https://via.placeholder.com/32x32/EF4444/FFFFFF?text=D\",\n  ];\n\n  return (\n    <div className=\"bg-white dark:bg-gray-900\">\n      {showTeamTable && (\n        <>\n          {/* Header */}\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\">\n            <div className=\"flex items-center space-x-4\">\n              <h2 className=\"text-left text-2xl font-bold text-gray-900 dark:text-white\">\n                Teams Password Card\n              </h2>\n\n              {/* Share Icon */}\n              <button\n                onClick={handleShare}\n                className=\"flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\"\n              >\n                <span className=\"material-symbols-rounded\">share</span>\n              </button>\n\n              {/* Delete Team Table Icon */}\n              <button\n                onClick={() => handleDeleteTeamTable()}\n                className=\"flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg\"\n                title=\"Delete entire table\"\n              >\n                {/* <span className=\"material-symbols-rounded\">delete</span> */}\n                <span className=\"material-symbols-outlined text-sm\">\n                  delete\n                </span>\n              </button>\n\n              {/* User Avatars */}\n              <div className=\"flex -space-x-2\">\n                {avatarImages.map((avatar, index) => (\n                  <img\n                    key={index}\n                    src={avatar}\n                    alt={`User ${index + 1}`}\n                    className=\"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"\n                  />\n                ))}\n              </div>\n            </div>\n\n            <div className=\"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0\">\n              {/* Add Password Card Button */}\n              <button\n                onClick={() => {\n                  setEditingRowId(null);\n                  setShowAddForm(!showAddForm);\n                }}\n                className={`w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${\n                  showAddForm\n                    ? \"bg-primary text-white\"\n                    : \"bg-transparent text-primary border-2 border-primary\"\n                }`}\n              >\n                <span className=\"material-symbols-rounded mr-2\">add</span>\n                Add Password\n              </button>\n            </div>\n          </div>\n\n          {/* Add/Edit Password Form - Embedded */}\n          {showAddForm && (\n            <AddPasswordCardForm\n              onSubmit={handleAddPasswordCard}\n              onCancel={() => {\n                setShowAddForm(false);\n                setEditingRowId(null);\n              }}\n              generatedPassword={generatedPassword}\n              passwordStrength={passwordStrength}\n            />\n          )}\n\n          {/* Table */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50 border-b border-gray-200\">\n                <tr>\n                  <th className=\"w-12 px-4 py-3 text-left\">\n                    <input\n                      id=\"checkbox-all\"\n                      type=\"checkbox\"\n                      checked={\n                        shareableCards.length === passwordCards.length &&\n                        passwordCards.length > 0\n                      }\n                      onChange={toggleSelectAll}\n                      className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500\"\n                    />\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"title\")}\n                    >\n                      <span>Title</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"username\")}\n                    >\n                      <span>User Name</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Password\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Team\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Department\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Level\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Action\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"divide-y divide-gray-200\">\n                {getSortedCards(passwordCards).map((card) => (\n                  <tr key={card.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-4 py-4\">\n                      <input\n                        id={`shareable-${card.id}`}\n                        type=\"checkbox\"\n                        checked={shareableCards.includes(card.id)}\n                        onChange={() => toggleShareable(card.id)}\n                        className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                      />\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center\">\n                        <div className=\"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\">\n                          TG\n                        </div>\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.title}\n                            className=\"font-medium text-gray-900 border rounded px-2 py-1 w-full\"\n                            onBlur={(e) => {\n                              setPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, title: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                            autoFocus\n                          />\n                        ) : (\n                          <span className=\"font-medium text-gray-900\">\n                            {card.title}\n                          </span>\n                        )}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center\">\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.username}\n                            className=\"text-gray-900 border rounded px-2 py-1 w-full mr-2\"\n                            onBlur={(e) => {\n                              setPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, username: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                          />\n                        ) : (\n                          <span className=\"text-gray-900\">{card.username}</span>\n                        )}\n                        <button\n                          onClick={() => copyToClipboard(card.username)}\n                          className=\"ml-2 text-gray-400 hover:text-gray-600\"\n                        >\n                          <span className=\"material-symbols-rounded text-sm\">\n                            content_copy\n                          </span>\n                        </button>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center\">\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.actualPassword}\n                            className=\"text-gray-900 border rounded px-2 py-1 w-full mr-2\"\n                            onBlur={(e) => {\n                              setPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, actualPassword: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                          />\n                        ) : (\n                          <span className=\"text-gray-900 mr-2\">\n                            {visiblePasswords[card.id]\n                              ? card.actualPassword\n                              : card.password}\n                          </span>\n                        )}\n                        <button\n                          onClick={() => togglePasswordVisibility(card.id)}\n                          className=\"text-gray-400 hover:text-gray-600 mr-2\"\n                        >\n                          <span className=\"material-symbols-rounded text-sm\">\n                            {visiblePasswords[card.id]\n                              ? \"visibility_off\"\n                              : \"visibility\"}\n                          </span>\n                        </button>\n                        <button\n                          onClick={() => copyToClipboard(card.actualPassword)}\n                          className=\"text-gray-400 hover:text-gray-600\"\n                        >\n                          <span className=\"material-symbols-rounded text-sm\">\n                            content_copy\n                          </span>\n                        </button>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      {editingRowId === card.id ? (\n                        <input\n                          type=\"text\"\n                          defaultValue={card.team}\n                          className=\"text-gray-900 border rounded px-2 py-1 w-full\"\n                          onBlur={(e) => {\n                            setPasswordCards((prev) =>\n                              prev.map((c) =>\n                                c.id === card.id\n                                  ? { ...c, team: e.target.value }\n                                  : c\n                              )\n                            );\n                          }}\n                          onKeyDown={(e) => {\n                            if (e.key === \"Enter\") {\n                              e.target.blur();\n                            }\n                          }}\n                        />\n                      ) : (\n                        <span className=\"text-gray-900\">{card.team}</span>\n                      )}\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      {editingRowId === card.id ? (\n                        <input\n                          type=\"text\"\n                          defaultValue={card.department}\n                          className=\"text-gray-900 border rounded px-2 py-1 w-full\"\n                          onBlur={(e) => {\n                            setPasswordCards((prev) =>\n                              prev.map((c) =>\n                                c.id === card.id\n                                  ? { ...c, department: e.target.value }\n                                  : c\n                              )\n                            );\n                          }}\n                          onKeyDown={(e) => {\n                            if (e.key === \"Enter\") {\n                              e.target.blur();\n                            }\n                          }}\n                        />\n                      ) : (\n                        <span className=\"text-gray-900\">{card.department}</span>\n                      )}\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <span\n                        className={`px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`}\n                      >\n                        {card.strength}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center space-x-1\">\n                        <button\n                          onClick={() => handleEdit(card.id)}\n                          className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\n                          title=\"Edit\"\n                        >\n                          <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\n                        </button>\n                        <button\n                          onClick={() => handleDelete(card.id)}\n                          className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\n                          title=\"Delete\"\n                        >\n                          <span className=\"material-symbols-outlined text-sm\">delete</span>\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </>\n      )}\n\n      {/* New Password Cards Table */}\n      {showNewTable && (\n        <div className=\"mt-8\">\n          {/* Header */}\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\">\n            <div className=\"flex items-center space-x-4\">\n              <h2 className=\"text-left text-2xl font-bold text-gray-900 dark:text-white\">\n                Teams Password Card\n              </h2>\n\n              {/* Share Icon */}\n              <button\n                onClick={handleShare}\n                className=\"flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\"\n              >\n                <span className=\"material-symbols-rounded\">share</span>\n              </button>\n\n              {/* Delete Team Table Icon */}\n              <button\n                onClick={() => handleDeleteTeamTable()}\n                className=\"flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg\"\n                title=\"Delete entire table\"\n              >\n                <span className=\"material-symbols-outlined text-sm\">\n                  delete\n                </span>\n              </button>\n\n              {/* User Avatars */}\n              <div className=\"flex -space-x-2\">\n                {avatarImages.map((avatar, index) => (\n                  <img\n                    key={index}\n                    src={avatar}\n                    alt={`User ${index + 1}`}\n                    className=\"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"\n                  />\n                ))}\n              </div>\n            </div>\n\n            <div className=\"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0\">\n              {/* Add Password Card Button */}\n              <button\n                onClick={() => {\n                  setEditingRowId(null);\n                  setShowAddForm(!showAddForm);\n                }}\n                className={`w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${\n                  showAddForm\n                    ? \"bg-primary text-white\"\n                    : \"bg-transparent text-primary border-2 border-primary\"\n                }`}\n              >\n                <span className=\"material-symbols-rounded mr-2\">add</span>\n                Add Password\n              </button>\n            </div>\n          </div>\n\n          {/* Add/Edit Password Form - Embedded */}\n          {showAddForm && (\n            <AddPasswordCardForm\n              onSubmit={handleAddPasswordCard}\n              onCancel={() => {\n                setShowAddForm(false);\n                setEditingRowId(null);\n              }}\n              generatedPassword={generatedPassword}\n              passwordStrength={passwordStrength}\n            />\n          )}\n\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50 border-b border-gray-200\">\n                <tr>\n                  <th className=\"w-12 px-4 py-3 text-left\">\n                    <input\n                      id=\"checkbox-all-new\"\n                      type=\"checkbox\"\n                      checked={\n                        shareableCards.length === newPasswordCards.length &&\n                        newPasswordCards.length > 0\n                      }\n                      onChange={() => {\n                        if (shareableCards.length === newPasswordCards.length) {\n                          setShareableCards([]);\n                        } else {\n                          setShareableCards(newPasswordCards.map((card) => card.id));\n                        }\n                      }}\n                      className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                    />\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"title\")}\n                    >\n                      <span>Title</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"username\")}\n                    >\n                      <span>User Name</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Password\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Team\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Department\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Level\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Action\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"divide-y divide-gray-200\">\n                {newPasswordCards.length === 0 ? (\n                  <tr>\n                    <td\n                      colSpan=\"8\"\n                      className=\"px-6 py-8 text-center text-gray-500\"\n                    >\n                      No password cards added yet. Click \"Add Password\" to add\n                      your first card.\n                    </td>\n                  </tr>\n                ) : (\n                  getSortedCards(newPasswordCards).map((card) => (\n                    <tr key={card.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-4 py-4\">\n                        <input\n                          type=\"checkbox\"\n                          checked={shareableCards.includes(card.id)}\n                          onChange={() => toggleShareable(card.id)}\n                          className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                        />\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          <div className=\"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\">\n                            TG\n                          </div>\n                          {editingRowId === card.id ? (\n                            <input\n                              type=\"text\"\n                              defaultValue={card.title}\n                              className=\"font-medium text-gray-900 border rounded px-2 py-1 w-full\"\n                              onBlur={(e) => {\n                                setNewPasswordCards((prev) =>\n                                  prev.map((c) =>\n                                    c.id === card.id\n                                      ? { ...c, title: e.target.value }\n                                      : c\n                                  )\n                                );\n                              }}\n                              onKeyDown={(e) => {\n                                if (e.key === \"Enter\") {\n                                  e.target.blur();\n                                }\n                              }}\n                              autoFocus\n                            />\n                          ) : (\n                            <span className=\"font-medium text-gray-900\">\n                              {card.title}\n                            </span>\n                          )}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          {editingRowId === card.id ? (\n                            <input\n                              type=\"text\"\n                              defaultValue={card.username}\n                              className=\"text-gray-900 border rounded px-2 py-1 w-full mr-2\"\n                              onBlur={(e) => {\n                                setNewPasswordCards((prev) =>\n                                  prev.map((c) =>\n                                    c.id === card.id\n                                      ? { ...c, username: e.target.value }\n                                      : c\n                                  )\n                                );\n                              }}\n                              onKeyDown={(e) => {\n                                if (e.key === \"Enter\") {\n                                  e.target.blur();\n                                }\n                              }}\n                            />\n                          ) : (\n                            <span className=\"text-gray-900\">{card.username}</span>\n                          )}\n                          <button\n                            onClick={() => copyToClipboard(card.username)}\n                            className=\"ml-2 text-gray-400 hover:text-gray-600\"\n                          >\n                            <span className=\"material-symbols-rounded text-sm\">\n                              content_copy\n                            </span>\n                          </button>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          {editingRowId === card.id ? (\n                            <input\n                              type=\"text\"\n                              defaultValue={card.actualPassword}\n                              className=\"text-gray-900 border rounded px-2 py-1 w-full mr-2\"\n                              onBlur={(e) => {\n                                setNewPasswordCards((prev) =>\n                                  prev.map((c) =>\n                                    c.id === card.id\n                                      ? { ...c, actualPassword: e.target.value }\n                                      : c\n                                  )\n                                );\n                              }}\n                              onKeyDown={(e) => {\n                                if (e.key === \"Enter\") {\n                                  e.target.blur();\n                                }\n                              }}\n                            />\n                          ) : (\n                            <span className=\"text-gray-900 mr-2\">\n                              {visiblePasswords[card.id]\n                                ? card.actualPassword\n                                : card.password}\n                            </span>\n                          )}\n                          <button\n                            onClick={() => togglePasswordVisibility(card.id)}\n                            className=\"text-gray-400 hover:text-gray-600 mr-2\"\n                          >\n                            <span className=\"material-symbols-rounded text-sm\">\n                              {visiblePasswords[card.id]\n                                ? \"visibility_off\"\n                                : \"visibility\"}\n                            </span>\n                          </button>\n                          <button\n                            onClick={() => copyToClipboard(card.actualPassword)}\n                            className=\"text-gray-400 hover:text-gray-600\"\n                          >\n                            <span className=\"material-symbols-rounded text-sm\">\n                              content_copy\n                            </span>\n                          </button>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.team}\n                            className=\"text-gray-900 border rounded px-2 py-1 w-full\"\n                            onBlur={(e) => {\n                              setNewPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, team: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                          />\n                        ) : (\n                          <span className=\"text-gray-900\">{card.team}</span>\n                        )}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.department}\n                            className=\"text-gray-900 border rounded px-2 py-1 w-full\"\n                            onBlur={(e) => {\n                              setNewPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, department: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                          />\n                        ) : (\n                          <span className=\"text-gray-900\">{card.department}</span>\n                        )}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <span\n                          className={`px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`}\n                        >\n                          {card.strength}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center space-x-1\">\n                          <button\n                            onClick={() => handleEdit(card.id)}\n                            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\n                            title=\"Edit\"\n                          >\n                            <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\n                          </button>\n                          <button\n                            onClick={() => {\n                              confirmationAlert({\n                                onConfirm: () => {\n                                  setNewPasswordCards((prev) =>\n                                    prev.filter((c) => c.id !== card.id)\n                                  );\n                                },\n                              });\n                            }}\n                            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\n                            title=\"Delete\"\n                          >\n                            <span className=\"material-symbols-outlined text-sm\">delete</span>\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))\n                )}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      )}\n\n      {/* Add New Password Card Button (Bottom) */}\n      <div className=\"flex justify-center mt-6\">\n        <button\n          onClick={handleCreateNewTable}\n          className=\"flex items-center justify-center px-6 py-2 text-sm font-medium text-white rounded-lg bg-primary hover:bg-primary/90 focus:ring-4 focus:ring-primary/30 transition-colors duration-200 focus:outline-none\"\n        >\n          <span className=\"material-symbols-rounded mr-2\">add</span>\n          Add New Password Card\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default PasswordCardsTable;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,mBAAmB,KAAM,uBAAuB,CACvD,OAASC,iBAAiB,KAAQ,qBAAqB,CACvD,MAAO,CAAAC,iBAAiB,KAAM,0CAA0C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEzE,KAAM,CAAAC,kBAAkB,CAAGC,IAAA,EAA6C,IAA5C,CAAEC,iBAAiB,CAAEC,gBAAiB,CAAC,CAAAF,IAAA,CACjE;AACA,KAAM,CAAEG,QAAS,CAAC,CAAGX,iBAAiB,CAAC,CAAC,CACxC,KAAM,CAAAY,aAAa,CAAGD,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEE,EAAE,CAElC;AACA,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGlB,QAAQ,CAAC,CACjD,CACEgB,EAAE,CAAE,CAAC,CACLG,KAAK,CAAE,eAAe,CACtBC,QAAQ,CAAE,OAAO,CACjBC,QAAQ,CAAE,yBAAyB,CACnCC,QAAQ,CAAE,cAAc,CACxBC,cAAc,CAAE,6CAA6C,CAC7DC,IAAI,CAAE,WAAW,CACjBC,UAAU,CAAE,IAAI,CAChBC,QAAQ,CAAE,eAAe,CACzBC,aAAa,CAAE,wCAAwC,CACvDC,QAAQ,CAAEb,aAAe;AAC3B,CAAC,CACD,CACEC,EAAE,CAAE,CAAC,CACLG,KAAK,CAAE,iBAAiB,CACxBC,QAAQ,CAAE,OAAO,CACjBC,QAAQ,CAAE,yBAAyB,CACnCC,QAAQ,CAAE,cAAc,CACxBC,cAAc,CAAE,kBAAkB,CAClCC,IAAI,CAAE,WAAW,CACjBC,UAAU,CAAE,IAAI,CAChBC,QAAQ,CAAE,iBAAiB,CAC3BC,aAAa,CAAE,8CAA8C,CAC7DC,QAAQ,CAAEb,aACZ,CAAC,CACD,CACEC,EAAE,CAAE,CAAC,CACLG,KAAK,CAAE,mBAAmB,CAC1BC,QAAQ,CAAE,QAAQ,CAClBC,QAAQ,CAAE,yBAAyB,CACnCC,QAAQ,CAAE,cAAc,CACxBC,cAAc,CAAE,iBAAiB,CACjCC,IAAI,CAAE,WAAW,CACjBC,UAAU,CAAE,aAAa,CACzBC,QAAQ,CAAE,mBAAmB,CAC7BC,aAAa,CAAE,iDAAiD,CAChEC,QAAQ,CAAE,GAAK;AACjB,CAAC,CACD,CACEZ,EAAE,CAAE,CAAC,CACLG,KAAK,CAAE,aAAa,CACpBC,QAAQ,CAAE,KAAK,CACfC,QAAQ,CAAE,yBAAyB,CACnCC,QAAQ,CAAE,cAAc,CACxBC,cAAc,CAAE,UAAU,CAC1BC,IAAI,CAAE,WAAW,CACjBC,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,eAAe,CACzBC,aAAa,CAAE,wCAAwC,CACvDC,QAAQ,CAAEb,aACZ,CAAC,CACD,CACEC,EAAE,CAAE,CAAC,CACLG,KAAK,CAAE,cAAc,CACrBC,QAAQ,CAAE,MAAM,CAChBC,QAAQ,CAAE,yBAAyB,CACnCC,QAAQ,CAAE,cAAc,CACxBC,cAAc,CAAE,uBAAuB,CACvCC,IAAI,CAAE,WAAW,CACjBC,UAAU,CAAE,oBAAoB,CAChCC,QAAQ,CAAE,iBAAiB,CAC3BC,aAAa,CAAE,8CAA8C,CAC7DC,QAAQ,CAAE,GAAK;AACjB,CAAC,CACD,CACEZ,EAAE,CAAE,CAAC,CACLG,KAAK,CAAE,YAAY,CACnBC,QAAQ,CAAE,eAAe,CACzBC,QAAQ,CAAE,yBAAyB,CACnCC,QAAQ,CAAE,cAAc,CACxBC,cAAc,CAAE,mBAAmB,CACnCC,IAAI,CAAE,WAAW,CACjBC,UAAU,CAAE,IAAI,CAChBC,QAAQ,CAAE,mBAAmB,CAC7BC,aAAa,CAAE,iDAAiD,CAChEC,QAAQ,CAAEb,aACZ,CAAC,CACD,CACEC,EAAE,CAAE,CAAC,CACLG,KAAK,CAAE,gBAAgB,CACvBC,QAAQ,CAAE,OAAO,CACjBC,QAAQ,CAAE,yBAAyB,CACnCC,QAAQ,CAAE,cAAc,CACxBC,cAAc,CAAE,cAAc,CAC9BC,IAAI,CAAE,WAAW,CACjBC,UAAU,CAAE,UAAU,CACtBC,QAAQ,CAAE,eAAe,CACzBC,aAAa,CAAE,wCAAwC,CACvDC,QAAQ,CAAEb,aACZ,CAAC,CACF,CAAC,CAEF,KAAM,CAACc,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG9B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC5D,KAAM,CAAC+B,WAAW,CAAEC,cAAc,CAAC,CAAGhC,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACiC,YAAY,CAAEC,eAAe,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACmC,aAAa,CAAC,CAAGnC,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAACoC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACsC,YAAY,CAAEC,eAAe,CAAC,CAAGvC,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACwC,cAAc,CAAEC,iBAAiB,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC0C,UAAU,CAAEC,aAAa,CAAC,CAAG3C,QAAQ,CAAC,CAAE4C,GAAG,CAAE,IAAI,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAC7E,KAAM,CAAAC,wBAAwB,CAAI9B,EAAE,EAAK,CACvCc,mBAAmB,CAAEiB,IAAI,GAAM,CAC7B,GAAGA,IAAI,CACP,CAAC/B,EAAE,EAAG,CAAC+B,IAAI,CAAC/B,EAAE,CAChB,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAgC,eAAe,CAAIC,IAAI,EAAK,CAChCC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC,CACnC;AACF,CAAC,CAED;AACA,KAAM,CAAAI,UAAU,CAAIrC,EAAE,EAAK,CACzBuB,eAAe,CAACD,YAAY,GAAKtB,EAAE,CAAG,IAAI,CAAGA,EAAE,CAAC,CAClD,CAAC,CAED;AACA,KAAM,CAAAsC,oBAAoB,CAAGA,CAAA,GAAM,CACjCpB,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAqB,qBAAqB,CAAGA,CAAA,GAAM,CAClC;AACA,KAAM,CAAAC,QAAQ,CAAGvC,aAAa,CAACwC,IAAI,CAACC,IAAI,EAAIA,IAAI,CAAC9B,QAAQ,GAAKb,aAAa,CAAC,CAE5E,GAAI,CAACyC,QAAQ,CAAE,CACbG,KAAK,CAAC,8CAA8C,CAAC,CACrD,OACF,CAEA;AACA,GAAInB,cAAc,CAACoB,MAAM,GAAK,CAAC,CAAE,CAC/BD,KAAK,CAAC,qDAAqD,CAAC,CAC5D,OACF,CAEAzD,iBAAiB,CAAC,CAChB2D,SAAS,CAAEA,CAAA,GAAM,CACf3C,gBAAgB,CAAC,EAAE,CAAC,CACtB,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AAEA,KAAM,CAAA4C,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAItB,cAAc,CAACoB,MAAM,GAAK3C,aAAa,CAAC2C,MAAM,CAAE,CAClDnB,iBAAiB,CAAC,EAAE,CAAC,CACvB,CAAC,IAAM,CACLA,iBAAiB,CAACxB,aAAa,CAAC8C,GAAG,CAAEL,IAAI,EAAKA,IAAI,CAAC1C,EAAE,CAAC,CAAC,CACzD,CACF,CAAC,CAED;AACA,KAAM,CAAAgD,UAAU,CAAIpB,GAAG,EAAK,CAC1B,GAAI,CAAAC,SAAS,CAAG,KAAK,CACrB,GAAIH,UAAU,CAACE,GAAG,GAAKA,GAAG,EAAIF,UAAU,CAACG,SAAS,GAAK,KAAK,CAAE,CAC5DA,SAAS,CAAG,MAAM,CACpB,CACAF,aAAa,CAAC,CAAEC,GAAG,CAAEC,SAAU,CAAC,CAAC,CACnC,CAAC,CAED;AACA,KAAM,CAAAoB,cAAc,CAAIC,KAAK,EAAK,CAChC,GAAI,CAACxB,UAAU,CAACE,GAAG,CAAE,MAAO,CAAAsB,KAAK,CAEjC,MAAO,CAAC,GAAGA,KAAK,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CAC/B,KAAM,CAAAC,MAAM,CAAGF,CAAC,CAAC1B,UAAU,CAACE,GAAG,CAAC,CAAC2B,WAAW,CAAC,CAAC,CAC9C,KAAM,CAAAC,MAAM,CAAGH,CAAC,CAAC3B,UAAU,CAACE,GAAG,CAAC,CAAC2B,WAAW,CAAC,CAAC,CAE9C,GAAI7B,UAAU,CAACG,SAAS,GAAK,KAAK,CAAE,CAClC,MAAO,CAAAyB,MAAM,CAAGE,MAAM,CAAG,CAAC,CAAC,CAAGF,MAAM,CAAGE,MAAM,CAAG,CAAC,CAAG,CAAC,CACvD,CAAC,IAAM,CACL,MAAO,CAAAF,MAAM,CAAGE,MAAM,CAAG,CAAC,CAAC,CAAGF,MAAM,CAAGE,MAAM,CAAG,CAAC,CAAG,CAAC,CACvD,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAC,eAAe,CAAIzD,EAAE,EAAK,CAC9ByB,iBAAiB,CAAEM,IAAI,EACrBA,IAAI,CAAC2B,QAAQ,CAAC1D,EAAE,CAAC,CAAG+B,IAAI,CAAC4B,MAAM,CAAEC,MAAM,EAAKA,MAAM,GAAK5D,EAAE,CAAC,CAAG,CAAC,GAAG+B,IAAI,CAAE/B,EAAE,CAC3E,CAAC,CACH,CAAC,CAED;AACA,KAAM,CAAA6D,qBAAqB,CAAIC,QAAQ,EAAK,CAC1C,KAAM,CAAAC,OAAO,CAAG,CACd,GAAGD,QAAQ,CACX9D,EAAE,CAAEgE,IAAI,CAACC,GAAG,CAAC,CAAC,CACd3D,QAAQ,CAAE,cAAc,CACxBC,cAAc,CAAEuD,QAAQ,CAACxD,QAAQ,CACjCM,QAAQ,CAAEb,aAAe;AAC3B,CAAC,CACDsB,mBAAmB,CAAEU,IAAI,EAAK,CAAC,GAAGA,IAAI,CAAEgC,OAAO,CAAC,CAAC,CACjD/C,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CAED,KAAM,CAAAkD,YAAY,CAAIlE,EAAE,EAAK,CAC3Bd,iBAAiB,CAAC,CAChB2D,SAAS,CAAEA,CAAA,GAAM,CACf3C,gBAAgB,CAAE6B,IAAI,EAAKA,IAAI,CAAC4B,MAAM,CAAEjB,IAAI,EAAKA,IAAI,CAAC1C,EAAE,GAAKA,EAAE,CAAC,CAAC,CACnE,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAmE,WAAW,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CACxB;AACA,KAAM,CAAA7B,QAAQ,CAAGvC,aAAa,CAACwC,IAAI,CAACC,IAAI,EAAIA,IAAI,CAAC9B,QAAQ,GAAKb,aAAa,CAAC,CAE5E,GAAI,CAACyC,QAAQ,CAAE,CACbG,KAAK,CAAC,sCAAsC,CAAC,CAC7C,OACF,CAEA;AACA,KAAM,CAAA2B,qBAAqB,CAAG,CAAAxE,QAAQ,SAARA,QAAQ,kBAAAsE,qBAAA,CAARtE,QAAQ,CAAEyE,WAAW,UAAAH,qBAAA,kBAAAC,sBAAA,CAArBD,qBAAA,CAAwB,CAAC,CAAC,UAAAC,sBAAA,iBAA1BA,sBAAA,CAA4BG,IAAI,GAAI,IAAI,CAEtE;AACA,KAAM,CAAAC,eAAe,CAAGxE,aAAa,CAAC0D,MAAM,CACzCjB,IAAI,EAAKA,IAAI,CAACjC,UAAU,GAAK6D,qBAAqB,EAAI5B,IAAI,CAAC9B,QAAQ,GAAKb,aAC3E,CAAC,CAED,GAAI0E,eAAe,CAAC7B,MAAM,GAAK,CAAC,CAAE,CAChCD,KAAK,CAAC,0DAA0D,CAAC,CACjE,OACF,CAEA;AACA,KAAM,CAAA+B,SAAS,CAAG,CAChBvE,KAAK,CAAE,GAAGmE,qBAAqB,4BAA4B,CAC3DpB,KAAK,CAAEuB,eAAe,CACtBE,QAAQ,CAAE,GAAG7E,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE8E,KAAK,IAAI9E,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE+E,KAAK,EAAE,EAAI,cAAc,CACnEC,QAAQ,CAAE,GAAI,CAAAd,IAAI,CAAC,CAAC,CAACe,WAAW,CAAC,CACnC,CAAC,CAED;AACA7C,SAAS,CAACC,SAAS,CAChBC,SAAS,CAAC4C,IAAI,CAACC,SAAS,CAACP,SAAS,CAAE,IAAI,CAAE,CAAC,CAAC,CAAC,CAC7CQ,IAAI,CAAC,IAAM,CACVvC,KAAK,CACH,GAAG8B,eAAe,CAAC7B,MAAM,wBAAwB0B,qBAAqB,kCACxE,CAAC,CACH,CAAC,CAAC,CACDa,KAAK,CAAC,IAAM,CACXxC,KAAK,CAAC,gDAAgD,CAAC,CACzD,CAAC,CAAC,CACN,CAAC,CAED;AACA,KAAM,CAAAyC,YAAY,CAAG,CACnB,wDAAwD,CACxD,wDAAwD,CACxD,wDAAwD,CACxD,wDAAwD,CACzD,CAED,mBACE7F,KAAA,QAAK8F,SAAS,CAAC,2BAA2B,CAAAC,QAAA,EACvCnE,aAAa,eACZ5B,KAAA,CAAAE,SAAA,EAAA6F,QAAA,eAEE/F,KAAA,QAAK8F,SAAS,CAAC,iGAAiG,CAAAC,QAAA,eAC9G/F,KAAA,QAAK8F,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CjG,IAAA,OAAIgG,SAAS,CAAC,4DAA4D,CAAAC,QAAA,CAAC,qBAE3E,CAAI,CAAC,cAGLjG,IAAA,WACEkG,OAAO,CAAEpB,WAAY,CACrBkB,SAAS,CAAC,+KAA+K,CAAAC,QAAA,cAEzLjG,IAAA,SAAMgG,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,OAAK,CAAM,CAAC,CACjD,CAAC,cAGTjG,IAAA,WACEkG,OAAO,CAAEA,CAAA,GAAMhD,qBAAqB,CAAC,CAAE,CACvC8C,SAAS,CAAC,qGAAqG,CAC/GlF,KAAK,CAAC,qBAAqB,CAAAmF,QAAA,cAG3BjG,IAAA,SAAMgG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,QAEpD,CAAM,CAAC,CACD,CAAC,cAGTjG,IAAA,QAAKgG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7BF,YAAY,CAACrC,GAAG,CAAC,CAACyC,MAAM,CAAEC,KAAK,gBAC9BpG,IAAA,QAEEqG,GAAG,CAAEF,MAAO,CACZG,GAAG,CAAE,QAAQF,KAAK,CAAG,CAAC,EAAG,CACzBJ,SAAS,CAAC,iEAAiE,EAHtEI,KAIN,CACF,CAAC,CACC,CAAC,EACH,CAAC,cAENpG,IAAA,QAAKgG,SAAS,CAAC,0IAA0I,CAAAC,QAAA,cAEvJ/F,KAAA,WACEgG,OAAO,CAAEA,CAAA,GAAM,CACbhE,eAAe,CAAC,IAAI,CAAC,CACrBP,cAAc,CAAC,CAACD,WAAW,CAAC,CAC9B,CAAE,CACFsE,SAAS,CAAE,ucACTtE,WAAW,CACP,uBAAuB,CACvB,qDAAqD,EACxD,CAAAuE,QAAA,eAEHjG,IAAA,SAAMgG,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,KAAG,CAAM,CAAC,eAE5D,EAAQ,CAAC,CACN,CAAC,EACH,CAAC,CAGLvE,WAAW,eACV1B,IAAA,CAACJ,mBAAmB,EAClB2G,QAAQ,CAAE/B,qBAAsB,CAChCgC,QAAQ,CAAEA,CAAA,GAAM,CACd7E,cAAc,CAAC,KAAK,CAAC,CACrBO,eAAe,CAAC,IAAI,CAAC,CACvB,CAAE,CACF3B,iBAAiB,CAAEA,iBAAkB,CACrCC,gBAAgB,CAAEA,gBAAiB,CACpC,CACF,cAGDR,IAAA,QAAKgG,SAAS,CAAC,sDAAsD,CAAAC,QAAA,cACnE/F,KAAA,UAAO8F,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACvBjG,IAAA,UAAOgG,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cACpD/F,KAAA,OAAA+F,QAAA,eACEjG,IAAA,OAAIgG,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACtCjG,IAAA,UACEW,EAAE,CAAC,cAAc,CACjB8F,IAAI,CAAC,UAAU,CACfC,OAAO,CACLvE,cAAc,CAACoB,MAAM,GAAK3C,aAAa,CAAC2C,MAAM,EAC9C3C,aAAa,CAAC2C,MAAM,CAAG,CACxB,CACDoD,QAAQ,CAAElD,eAAgB,CAC1BuC,SAAS,CAAC,kFAAkF,CAC7F,CAAC,CACA,CAAC,cACLhG,IAAA,OAAIgG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,cACnE/F,KAAA,QACE8F,SAAS,CAAC,4CAA4C,CACtDE,OAAO,CAAEA,CAAA,GAAMvC,UAAU,CAAC,OAAO,CAAE,CAAAsC,QAAA,eAEnCjG,IAAA,SAAAiG,QAAA,CAAM,OAAK,CAAM,CAAC,cAClB/F,KAAA,QAAK8F,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjG,IAAA,SACEgG,SAAS,CAAE,WACT3D,UAAU,CAACE,GAAG,GAAK,OAAO,EAC1BF,UAAU,CAACG,SAAS,GAAK,KAAK,CAC1B,eAAe,CACf,eAAe,EAClB,CAAAyD,QAAA,CACJ,QAED,CAAM,CAAC,cACPjG,IAAA,SACEgG,SAAS,CAAE,WACT3D,UAAU,CAACE,GAAG,GAAK,OAAO,EAC1BF,UAAU,CAACG,SAAS,GAAK,MAAM,CAC3B,eAAe,CACf,eAAe,EAClB,CAAAyD,QAAA,CACJ,QAED,CAAM,CAAC,EACJ,CAAC,EACH,CAAC,CACJ,CAAC,cACLjG,IAAA,OAAIgG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,cACnE/F,KAAA,QACE8F,SAAS,CAAC,4CAA4C,CACtDE,OAAO,CAAEA,CAAA,GAAMvC,UAAU,CAAC,UAAU,CAAE,CAAAsC,QAAA,eAEtCjG,IAAA,SAAAiG,QAAA,CAAM,WAAS,CAAM,CAAC,cACtB/F,KAAA,QAAK8F,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjG,IAAA,SACEgG,SAAS,CAAE,WACT3D,UAAU,CAACE,GAAG,GAAK,UAAU,EAC7BF,UAAU,CAACG,SAAS,GAAK,KAAK,CAC1B,eAAe,CACf,eAAe,EAClB,CAAAyD,QAAA,CACJ,QAED,CAAM,CAAC,cACPjG,IAAA,SACEgG,SAAS,CAAE,WACT3D,UAAU,CAACE,GAAG,GAAK,UAAU,EAC7BF,UAAU,CAACG,SAAS,GAAK,MAAM,CAC3B,eAAe,CACf,eAAe,EAClB,CAAAyD,QAAA,CACJ,QAED,CAAM,CAAC,EACJ,CAAC,EACH,CAAC,CACJ,CAAC,cACLjG,IAAA,OAAIgG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,UAEtE,CAAI,CAAC,cACLjG,IAAA,OAAIgG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,MAEtE,CAAI,CAAC,cACLjG,IAAA,OAAIgG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,YAEtE,CAAI,CAAC,cACLjG,IAAA,OAAIgG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,OAEtE,CAAI,CAAC,cACLjG,IAAA,OAAIgG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,QAEtE,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACRjG,IAAA,UAAOgG,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACxCrC,cAAc,CAAChD,aAAa,CAAC,CAAC8C,GAAG,CAAEL,IAAI,eACtCnD,KAAA,OAAkB8F,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC5CjG,IAAA,OAAIgG,SAAS,CAAC,WAAW,CAAAC,QAAA,cACvBjG,IAAA,UACEW,EAAE,CAAE,aAAa0C,IAAI,CAAC1C,EAAE,EAAG,CAC3B8F,IAAI,CAAC,UAAU,CACfC,OAAO,CAAEvE,cAAc,CAACkC,QAAQ,CAAChB,IAAI,CAAC1C,EAAE,CAAE,CAC1CgG,QAAQ,CAAEA,CAAA,GAAMvC,eAAe,CAACf,IAAI,CAAC1C,EAAE,CAAE,CACzCqF,SAAS,CAAC,+EAA+E,CAC1F,CAAC,CACA,CAAC,cACLhG,IAAA,OAAIgG,SAAS,CAAC,WAAW,CAAAC,QAAA,cACvB/F,KAAA,QAAK8F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCjG,IAAA,QAAKgG,SAAS,CAAC,gGAAgG,CAAAC,QAAA,CAAC,IAEhH,CAAK,CAAC,CACLhE,YAAY,GAAKoB,IAAI,CAAC1C,EAAE,cACvBX,IAAA,UACEyG,IAAI,CAAC,MAAM,CACXG,YAAY,CAAEvD,IAAI,CAACvC,KAAM,CACzBkF,SAAS,CAAC,2DAA2D,CACrEa,MAAM,CAAGC,CAAC,EAAK,CACbjG,gBAAgB,CAAE6B,IAAI,EACpBA,IAAI,CAACgB,GAAG,CAAEqD,CAAC,EACTA,CAAC,CAACpG,EAAE,GAAK0C,IAAI,CAAC1C,EAAE,CACZ,CAAE,GAAGoG,CAAC,CAAEjG,KAAK,CAAEgG,CAAC,CAACE,MAAM,CAACC,KAAM,CAAC,CAC/BF,CACN,CACF,CAAC,CACH,CAAE,CACFG,SAAS,CAAGJ,CAAC,EAAK,CAChB,GAAIA,CAAC,CAACvE,GAAG,GAAK,OAAO,CAAE,CACrBuE,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC,CACjB,CACF,CAAE,CACFC,SAAS,MACV,CAAC,cAEFpH,IAAA,SAAMgG,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACxC5C,IAAI,CAACvC,KAAK,CACP,CACP,EACE,CAAC,CACJ,CAAC,cACLd,IAAA,OAAIgG,SAAS,CAAC,WAAW,CAAAC,QAAA,cACvB/F,KAAA,QAAK8F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAC/BhE,YAAY,GAAKoB,IAAI,CAAC1C,EAAE,cACvBX,IAAA,UACEyG,IAAI,CAAC,MAAM,CACXG,YAAY,CAAEvD,IAAI,CAACrC,QAAS,CAC5BgF,SAAS,CAAC,oDAAoD,CAC9Da,MAAM,CAAGC,CAAC,EAAK,CACbjG,gBAAgB,CAAE6B,IAAI,EACpBA,IAAI,CAACgB,GAAG,CAAEqD,CAAC,EACTA,CAAC,CAACpG,EAAE,GAAK0C,IAAI,CAAC1C,EAAE,CACZ,CAAE,GAAGoG,CAAC,CAAE/F,QAAQ,CAAE8F,CAAC,CAACE,MAAM,CAACC,KAAM,CAAC,CAClCF,CACN,CACF,CAAC,CACH,CAAE,CACFG,SAAS,CAAGJ,CAAC,EAAK,CAChB,GAAIA,CAAC,CAACvE,GAAG,GAAK,OAAO,CAAE,CACrBuE,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC,CACjB,CACF,CAAE,CACH,CAAC,cAEFnH,IAAA,SAAMgG,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAE5C,IAAI,CAACrC,QAAQ,CAAO,CACtD,cACDhB,IAAA,WACEkG,OAAO,CAAEA,CAAA,GAAMvD,eAAe,CAACU,IAAI,CAACrC,QAAQ,CAAE,CAC9CgF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cAElDjG,IAAA,SAAMgG,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,cAEnD,CAAM,CAAC,CACD,CAAC,EACN,CAAC,CACJ,CAAC,cACLjG,IAAA,OAAIgG,SAAS,CAAC,WAAW,CAAAC,QAAA,cACvB/F,KAAA,QAAK8F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAC/BhE,YAAY,GAAKoB,IAAI,CAAC1C,EAAE,cACvBX,IAAA,UACEyG,IAAI,CAAC,MAAM,CACXG,YAAY,CAAEvD,IAAI,CAACnC,cAAe,CAClC8E,SAAS,CAAC,oDAAoD,CAC9Da,MAAM,CAAGC,CAAC,EAAK,CACbjG,gBAAgB,CAAE6B,IAAI,EACpBA,IAAI,CAACgB,GAAG,CAAEqD,CAAC,EACTA,CAAC,CAACpG,EAAE,GAAK0C,IAAI,CAAC1C,EAAE,CACZ,CAAE,GAAGoG,CAAC,CAAE7F,cAAc,CAAE4F,CAAC,CAACE,MAAM,CAACC,KAAM,CAAC,CACxCF,CACN,CACF,CAAC,CACH,CAAE,CACFG,SAAS,CAAGJ,CAAC,EAAK,CAChB,GAAIA,CAAC,CAACvE,GAAG,GAAK,OAAO,CAAE,CACrBuE,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC,CACjB,CACF,CAAE,CACH,CAAC,cAEFnH,IAAA,SAAMgG,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CACjCzE,gBAAgB,CAAC6B,IAAI,CAAC1C,EAAE,CAAC,CACtB0C,IAAI,CAACnC,cAAc,CACnBmC,IAAI,CAACpC,QAAQ,CACb,CACP,cACDjB,IAAA,WACEkG,OAAO,CAAEA,CAAA,GAAMzD,wBAAwB,CAACY,IAAI,CAAC1C,EAAE,CAAE,CACjDqF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cAElDjG,IAAA,SAAMgG,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC/CzE,gBAAgB,CAAC6B,IAAI,CAAC1C,EAAE,CAAC,CACtB,gBAAgB,CAChB,YAAY,CACZ,CAAC,CACD,CAAC,cACTX,IAAA,WACEkG,OAAO,CAAEA,CAAA,GAAMvD,eAAe,CAACU,IAAI,CAACnC,cAAc,CAAE,CACpD8E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAE7CjG,IAAA,SAAMgG,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,cAEnD,CAAM,CAAC,CACD,CAAC,EACN,CAAC,CACJ,CAAC,cACLjG,IAAA,OAAIgG,SAAS,CAAC,WAAW,CAAAC,QAAA,CACtBhE,YAAY,GAAKoB,IAAI,CAAC1C,EAAE,cACvBX,IAAA,UACEyG,IAAI,CAAC,MAAM,CACXG,YAAY,CAAEvD,IAAI,CAAClC,IAAK,CACxB6E,SAAS,CAAC,+CAA+C,CACzDa,MAAM,CAAGC,CAAC,EAAK,CACbjG,gBAAgB,CAAE6B,IAAI,EACpBA,IAAI,CAACgB,GAAG,CAAEqD,CAAC,EACTA,CAAC,CAACpG,EAAE,GAAK0C,IAAI,CAAC1C,EAAE,CACZ,CAAE,GAAGoG,CAAC,CAAE5F,IAAI,CAAE2F,CAAC,CAACE,MAAM,CAACC,KAAM,CAAC,CAC9BF,CACN,CACF,CAAC,CACH,CAAE,CACFG,SAAS,CAAGJ,CAAC,EAAK,CAChB,GAAIA,CAAC,CAACvE,GAAG,GAAK,OAAO,CAAE,CACrBuE,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC,CACjB,CACF,CAAE,CACH,CAAC,cAEFnH,IAAA,SAAMgG,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAE5C,IAAI,CAAClC,IAAI,CAAO,CAClD,CACC,CAAC,cACLnB,IAAA,OAAIgG,SAAS,CAAC,WAAW,CAAAC,QAAA,CACtBhE,YAAY,GAAKoB,IAAI,CAAC1C,EAAE,cACvBX,IAAA,UACEyG,IAAI,CAAC,MAAM,CACXG,YAAY,CAAEvD,IAAI,CAACjC,UAAW,CAC9B4E,SAAS,CAAC,+CAA+C,CACzDa,MAAM,CAAGC,CAAC,EAAK,CACbjG,gBAAgB,CAAE6B,IAAI,EACpBA,IAAI,CAACgB,GAAG,CAAEqD,CAAC,EACTA,CAAC,CAACpG,EAAE,GAAK0C,IAAI,CAAC1C,EAAE,CACZ,CAAE,GAAGoG,CAAC,CAAE3F,UAAU,CAAE0F,CAAC,CAACE,MAAM,CAACC,KAAM,CAAC,CACpCF,CACN,CACF,CAAC,CACH,CAAE,CACFG,SAAS,CAAGJ,CAAC,EAAK,CAChB,GAAIA,CAAC,CAACvE,GAAG,GAAK,OAAO,CAAE,CACrBuE,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC,CACjB,CACF,CAAE,CACH,CAAC,cAEFnH,IAAA,SAAMgG,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAE5C,IAAI,CAACjC,UAAU,CAAO,CACxD,CACC,CAAC,cACLpB,IAAA,OAAIgG,SAAS,CAAC,WAAW,CAAAC,QAAA,cACvBjG,IAAA,SACEgG,SAAS,CAAE,8CAA8C3C,IAAI,CAAC/B,aAAa,EAAG,CAAA2E,QAAA,CAE7E5C,IAAI,CAAChC,QAAQ,CACV,CAAC,CACL,CAAC,cACLrB,IAAA,OAAIgG,SAAS,CAAC,WAAW,CAAAC,QAAA,cACvB/F,KAAA,QAAK8F,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CjG,IAAA,WACEkG,OAAO,CAAEA,CAAA,GAAMlD,UAAU,CAACK,IAAI,CAAC1C,EAAE,CAAE,CACnCqF,SAAS,CAAC,mLAAmL,CAC7LlF,KAAK,CAAC,MAAM,CAAAmF,QAAA,cAEZjG,IAAA,SAAMgG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,aAAW,CAAM,CAAC,CAChE,CAAC,cACTjG,IAAA,WACEkG,OAAO,CAAEA,CAAA,GAAMrB,YAAY,CAACxB,IAAI,CAAC1C,EAAE,CAAE,CACrCqF,SAAS,CAAC,mLAAmL,CAC7LlF,KAAK,CAAC,QAAQ,CAAAmF,QAAA,cAEdjG,IAAA,SAAMgG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,CAC3D,CAAC,EACN,CAAC,CACJ,CAAC,GAzME5C,IAAI,CAAC1C,EA0MV,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACN,CACH,CAGAiB,YAAY,eACX1B,KAAA,QAAK8F,SAAS,CAAC,MAAM,CAAAC,QAAA,eAEnB/F,KAAA,QAAK8F,SAAS,CAAC,iGAAiG,CAAAC,QAAA,eAC9G/F,KAAA,QAAK8F,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CjG,IAAA,OAAIgG,SAAS,CAAC,4DAA4D,CAAAC,QAAA,CAAC,qBAE3E,CAAI,CAAC,cAGLjG,IAAA,WACEkG,OAAO,CAAEpB,WAAY,CACrBkB,SAAS,CAAC,+KAA+K,CAAAC,QAAA,cAEzLjG,IAAA,SAAMgG,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,OAAK,CAAM,CAAC,CACjD,CAAC,cAGTjG,IAAA,WACEkG,OAAO,CAAEA,CAAA,GAAMhD,qBAAqB,CAAC,CAAE,CACvC8C,SAAS,CAAC,qGAAqG,CAC/GlF,KAAK,CAAC,qBAAqB,CAAAmF,QAAA,cAE3BjG,IAAA,SAAMgG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,QAEpD,CAAM,CAAC,CACD,CAAC,cAGTjG,IAAA,QAAKgG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7BF,YAAY,CAACrC,GAAG,CAAC,CAACyC,MAAM,CAAEC,KAAK,gBAC9BpG,IAAA,QAEEqG,GAAG,CAAEF,MAAO,CACZG,GAAG,CAAE,QAAQF,KAAK,CAAG,CAAC,EAAG,CACzBJ,SAAS,CAAC,iEAAiE,EAHtEI,KAIN,CACF,CAAC,CACC,CAAC,EACH,CAAC,cAENpG,IAAA,QAAKgG,SAAS,CAAC,0IAA0I,CAAAC,QAAA,cAEvJ/F,KAAA,WACEgG,OAAO,CAAEA,CAAA,GAAM,CACbhE,eAAe,CAAC,IAAI,CAAC,CACrBP,cAAc,CAAC,CAACD,WAAW,CAAC,CAC9B,CAAE,CACFsE,SAAS,CAAE,ucACTtE,WAAW,CACP,uBAAuB,CACvB,qDAAqD,EACxD,CAAAuE,QAAA,eAEHjG,IAAA,SAAMgG,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,KAAG,CAAM,CAAC,eAE5D,EAAQ,CAAC,CACN,CAAC,EACH,CAAC,CAGLvE,WAAW,eACV1B,IAAA,CAACJ,mBAAmB,EAClB2G,QAAQ,CAAE/B,qBAAsB,CAChCgC,QAAQ,CAAEA,CAAA,GAAM,CACd7E,cAAc,CAAC,KAAK,CAAC,CACrBO,eAAe,CAAC,IAAI,CAAC,CACvB,CAAE,CACF3B,iBAAiB,CAAEA,iBAAkB,CACrCC,gBAAgB,CAAEA,gBAAiB,CACpC,CACF,cAEDR,IAAA,QAAKgG,SAAS,CAAC,sDAAsD,CAAAC,QAAA,cACnE/F,KAAA,UAAO8F,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACvBjG,IAAA,UAAOgG,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cACpD/F,KAAA,OAAA+F,QAAA,eACEjG,IAAA,OAAIgG,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACtCjG,IAAA,UACEW,EAAE,CAAC,kBAAkB,CACrB8F,IAAI,CAAC,UAAU,CACfC,OAAO,CACLvE,cAAc,CAACoB,MAAM,GAAKxB,gBAAgB,CAACwB,MAAM,EACjDxB,gBAAgB,CAACwB,MAAM,CAAG,CAC3B,CACDoD,QAAQ,CAAEA,CAAA,GAAM,CACd,GAAIxE,cAAc,CAACoB,MAAM,GAAKxB,gBAAgB,CAACwB,MAAM,CAAE,CACrDnB,iBAAiB,CAAC,EAAE,CAAC,CACvB,CAAC,IAAM,CACLA,iBAAiB,CAACL,gBAAgB,CAAC2B,GAAG,CAAEL,IAAI,EAAKA,IAAI,CAAC1C,EAAE,CAAC,CAAC,CAC5D,CACF,CAAE,CACFqF,SAAS,CAAC,+EAA+E,CAC1F,CAAC,CACA,CAAC,cACLhG,IAAA,OAAIgG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,cACnE/F,KAAA,QACE8F,SAAS,CAAC,4CAA4C,CACtDE,OAAO,CAAEA,CAAA,GAAMvC,UAAU,CAAC,OAAO,CAAE,CAAAsC,QAAA,eAEnCjG,IAAA,SAAAiG,QAAA,CAAM,OAAK,CAAM,CAAC,cAClB/F,KAAA,QAAK8F,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjG,IAAA,SACEgG,SAAS,CAAE,WACT3D,UAAU,CAACE,GAAG,GAAK,OAAO,EAC1BF,UAAU,CAACG,SAAS,GAAK,KAAK,CAC1B,eAAe,CACf,eAAe,EAClB,CAAAyD,QAAA,CACJ,QAED,CAAM,CAAC,cACPjG,IAAA,SACEgG,SAAS,CAAE,WACT3D,UAAU,CAACE,GAAG,GAAK,OAAO,EAC1BF,UAAU,CAACG,SAAS,GAAK,MAAM,CAC3B,eAAe,CACf,eAAe,EAClB,CAAAyD,QAAA,CACJ,QAED,CAAM,CAAC,EACJ,CAAC,EACH,CAAC,CACJ,CAAC,cACLjG,IAAA,OAAIgG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,cACnE/F,KAAA,QACE8F,SAAS,CAAC,4CAA4C,CACtDE,OAAO,CAAEA,CAAA,GAAMvC,UAAU,CAAC,UAAU,CAAE,CAAAsC,QAAA,eAEtCjG,IAAA,SAAAiG,QAAA,CAAM,WAAS,CAAM,CAAC,cACtB/F,KAAA,QAAK8F,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjG,IAAA,SACEgG,SAAS,CAAE,WACT3D,UAAU,CAACE,GAAG,GAAK,UAAU,EAC7BF,UAAU,CAACG,SAAS,GAAK,KAAK,CAC1B,eAAe,CACf,eAAe,EAClB,CAAAyD,QAAA,CACJ,QAED,CAAM,CAAC,cACPjG,IAAA,SACEgG,SAAS,CAAE,WACT3D,UAAU,CAACE,GAAG,GAAK,UAAU,EAC7BF,UAAU,CAACG,SAAS,GAAK,MAAM,CAC3B,eAAe,CACf,eAAe,EAClB,CAAAyD,QAAA,CACJ,QAED,CAAM,CAAC,EACJ,CAAC,EACH,CAAC,CACJ,CAAC,cACLjG,IAAA,OAAIgG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,UAEtE,CAAI,CAAC,cACLjG,IAAA,OAAIgG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,MAEtE,CAAI,CAAC,cACLjG,IAAA,OAAIgG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,YAEtE,CAAI,CAAC,cACLjG,IAAA,OAAIgG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,OAEtE,CAAI,CAAC,cACLjG,IAAA,OAAIgG,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,QAEtE,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACRjG,IAAA,UAAOgG,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACxClE,gBAAgB,CAACwB,MAAM,GAAK,CAAC,cAC5BvD,IAAA,OAAAiG,QAAA,cACEjG,IAAA,OACEqH,OAAO,CAAC,GAAG,CACXrB,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAChD,6EAGD,CAAI,CAAC,CACH,CAAC,CAELrC,cAAc,CAAC7B,gBAAgB,CAAC,CAAC2B,GAAG,CAAEL,IAAI,eACxCnD,KAAA,OAAkB8F,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC5CjG,IAAA,OAAIgG,SAAS,CAAC,WAAW,CAAAC,QAAA,cACvBjG,IAAA,UACEyG,IAAI,CAAC,UAAU,CACfC,OAAO,CAAEvE,cAAc,CAACkC,QAAQ,CAAChB,IAAI,CAAC1C,EAAE,CAAE,CAC1CgG,QAAQ,CAAEA,CAAA,GAAMvC,eAAe,CAACf,IAAI,CAAC1C,EAAE,CAAE,CACzCqF,SAAS,CAAC,+EAA+E,CAC1F,CAAC,CACA,CAAC,cACLhG,IAAA,OAAIgG,SAAS,CAAC,WAAW,CAAAC,QAAA,cACvB/F,KAAA,QAAK8F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCjG,IAAA,QAAKgG,SAAS,CAAC,gGAAgG,CAAAC,QAAA,CAAC,IAEhH,CAAK,CAAC,CACLhE,YAAY,GAAKoB,IAAI,CAAC1C,EAAE,cACvBX,IAAA,UACEyG,IAAI,CAAC,MAAM,CACXG,YAAY,CAAEvD,IAAI,CAACvC,KAAM,CACzBkF,SAAS,CAAC,2DAA2D,CACrEa,MAAM,CAAGC,CAAC,EAAK,CACb9E,mBAAmB,CAAEU,IAAI,EACvBA,IAAI,CAACgB,GAAG,CAAEqD,CAAC,EACTA,CAAC,CAACpG,EAAE,GAAK0C,IAAI,CAAC1C,EAAE,CACZ,CAAE,GAAGoG,CAAC,CAAEjG,KAAK,CAAEgG,CAAC,CAACE,MAAM,CAACC,KAAM,CAAC,CAC/BF,CACN,CACF,CAAC,CACH,CAAE,CACFG,SAAS,CAAGJ,CAAC,EAAK,CAChB,GAAIA,CAAC,CAACvE,GAAG,GAAK,OAAO,CAAE,CACrBuE,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC,CACjB,CACF,CAAE,CACFC,SAAS,MACV,CAAC,cAEFpH,IAAA,SAAMgG,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACxC5C,IAAI,CAACvC,KAAK,CACP,CACP,EACE,CAAC,CACJ,CAAC,cACLd,IAAA,OAAIgG,SAAS,CAAC,WAAW,CAAAC,QAAA,cACvB/F,KAAA,QAAK8F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAC/BhE,YAAY,GAAKoB,IAAI,CAAC1C,EAAE,cACvBX,IAAA,UACEyG,IAAI,CAAC,MAAM,CACXG,YAAY,CAAEvD,IAAI,CAACrC,QAAS,CAC5BgF,SAAS,CAAC,oDAAoD,CAC9Da,MAAM,CAAGC,CAAC,EAAK,CACb9E,mBAAmB,CAAEU,IAAI,EACvBA,IAAI,CAACgB,GAAG,CAAEqD,CAAC,EACTA,CAAC,CAACpG,EAAE,GAAK0C,IAAI,CAAC1C,EAAE,CACZ,CAAE,GAAGoG,CAAC,CAAE/F,QAAQ,CAAE8F,CAAC,CAACE,MAAM,CAACC,KAAM,CAAC,CAClCF,CACN,CACF,CAAC,CACH,CAAE,CACFG,SAAS,CAAGJ,CAAC,EAAK,CAChB,GAAIA,CAAC,CAACvE,GAAG,GAAK,OAAO,CAAE,CACrBuE,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC,CACjB,CACF,CAAE,CACH,CAAC,cAEFnH,IAAA,SAAMgG,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAE5C,IAAI,CAACrC,QAAQ,CAAO,CACtD,cACDhB,IAAA,WACEkG,OAAO,CAAEA,CAAA,GAAMvD,eAAe,CAACU,IAAI,CAACrC,QAAQ,CAAE,CAC9CgF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cAElDjG,IAAA,SAAMgG,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,cAEnD,CAAM,CAAC,CACD,CAAC,EACN,CAAC,CACJ,CAAC,cACLjG,IAAA,OAAIgG,SAAS,CAAC,WAAW,CAAAC,QAAA,cACvB/F,KAAA,QAAK8F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAC/BhE,YAAY,GAAKoB,IAAI,CAAC1C,EAAE,cACvBX,IAAA,UACEyG,IAAI,CAAC,MAAM,CACXG,YAAY,CAAEvD,IAAI,CAACnC,cAAe,CAClC8E,SAAS,CAAC,oDAAoD,CAC9Da,MAAM,CAAGC,CAAC,EAAK,CACb9E,mBAAmB,CAAEU,IAAI,EACvBA,IAAI,CAACgB,GAAG,CAAEqD,CAAC,EACTA,CAAC,CAACpG,EAAE,GAAK0C,IAAI,CAAC1C,EAAE,CACZ,CAAE,GAAGoG,CAAC,CAAE7F,cAAc,CAAE4F,CAAC,CAACE,MAAM,CAACC,KAAM,CAAC,CACxCF,CACN,CACF,CAAC,CACH,CAAE,CACFG,SAAS,CAAGJ,CAAC,EAAK,CAChB,GAAIA,CAAC,CAACvE,GAAG,GAAK,OAAO,CAAE,CACrBuE,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC,CACjB,CACF,CAAE,CACH,CAAC,cAEFnH,IAAA,SAAMgG,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CACjCzE,gBAAgB,CAAC6B,IAAI,CAAC1C,EAAE,CAAC,CACtB0C,IAAI,CAACnC,cAAc,CACnBmC,IAAI,CAACpC,QAAQ,CACb,CACP,cACDjB,IAAA,WACEkG,OAAO,CAAEA,CAAA,GAAMzD,wBAAwB,CAACY,IAAI,CAAC1C,EAAE,CAAE,CACjDqF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cAElDjG,IAAA,SAAMgG,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC/CzE,gBAAgB,CAAC6B,IAAI,CAAC1C,EAAE,CAAC,CACtB,gBAAgB,CAChB,YAAY,CACZ,CAAC,CACD,CAAC,cACTX,IAAA,WACEkG,OAAO,CAAEA,CAAA,GAAMvD,eAAe,CAACU,IAAI,CAACnC,cAAc,CAAE,CACpD8E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAE7CjG,IAAA,SAAMgG,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,cAEnD,CAAM,CAAC,CACD,CAAC,EACN,CAAC,CACJ,CAAC,cACLjG,IAAA,OAAIgG,SAAS,CAAC,WAAW,CAAAC,QAAA,CACtBhE,YAAY,GAAKoB,IAAI,CAAC1C,EAAE,cACvBX,IAAA,UACEyG,IAAI,CAAC,MAAM,CACXG,YAAY,CAAEvD,IAAI,CAAClC,IAAK,CACxB6E,SAAS,CAAC,+CAA+C,CACzDa,MAAM,CAAGC,CAAC,EAAK,CACb9E,mBAAmB,CAAEU,IAAI,EACvBA,IAAI,CAACgB,GAAG,CAAEqD,CAAC,EACTA,CAAC,CAACpG,EAAE,GAAK0C,IAAI,CAAC1C,EAAE,CACZ,CAAE,GAAGoG,CAAC,CAAE5F,IAAI,CAAE2F,CAAC,CAACE,MAAM,CAACC,KAAM,CAAC,CAC9BF,CACN,CACF,CAAC,CACH,CAAE,CACFG,SAAS,CAAGJ,CAAC,EAAK,CAChB,GAAIA,CAAC,CAACvE,GAAG,GAAK,OAAO,CAAE,CACrBuE,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC,CACjB,CACF,CAAE,CACH,CAAC,cAEFnH,IAAA,SAAMgG,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAE5C,IAAI,CAAClC,IAAI,CAAO,CAClD,CACC,CAAC,cACLnB,IAAA,OAAIgG,SAAS,CAAC,WAAW,CAAAC,QAAA,CACtBhE,YAAY,GAAKoB,IAAI,CAAC1C,EAAE,cACvBX,IAAA,UACEyG,IAAI,CAAC,MAAM,CACXG,YAAY,CAAEvD,IAAI,CAACjC,UAAW,CAC9B4E,SAAS,CAAC,+CAA+C,CACzDa,MAAM,CAAGC,CAAC,EAAK,CACb9E,mBAAmB,CAAEU,IAAI,EACvBA,IAAI,CAACgB,GAAG,CAAEqD,CAAC,EACTA,CAAC,CAACpG,EAAE,GAAK0C,IAAI,CAAC1C,EAAE,CACZ,CAAE,GAAGoG,CAAC,CAAE3F,UAAU,CAAE0F,CAAC,CAACE,MAAM,CAACC,KAAM,CAAC,CACpCF,CACN,CACF,CAAC,CACH,CAAE,CACFG,SAAS,CAAGJ,CAAC,EAAK,CAChB,GAAIA,CAAC,CAACvE,GAAG,GAAK,OAAO,CAAE,CACrBuE,CAAC,CAACE,MAAM,CAACG,IAAI,CAAC,CAAC,CACjB,CACF,CAAE,CACH,CAAC,cAEFnH,IAAA,SAAMgG,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAE5C,IAAI,CAACjC,UAAU,CAAO,CACxD,CACC,CAAC,cACLpB,IAAA,OAAIgG,SAAS,CAAC,WAAW,CAAAC,QAAA,cACvBjG,IAAA,SACEgG,SAAS,CAAE,8CAA8C3C,IAAI,CAAC/B,aAAa,EAAG,CAAA2E,QAAA,CAE7E5C,IAAI,CAAChC,QAAQ,CACV,CAAC,CACL,CAAC,cACLrB,IAAA,OAAIgG,SAAS,CAAC,WAAW,CAAAC,QAAA,cACvB/F,KAAA,QAAK8F,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CjG,IAAA,WACEkG,OAAO,CAAEA,CAAA,GAAMlD,UAAU,CAACK,IAAI,CAAC1C,EAAE,CAAE,CACnCqF,SAAS,CAAC,mLAAmL,CAC7LlF,KAAK,CAAC,MAAM,CAAAmF,QAAA,cAEZjG,IAAA,SAAMgG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,aAAW,CAAM,CAAC,CAChE,CAAC,cACTjG,IAAA,WACEkG,OAAO,CAAEA,CAAA,GAAM,CACbrG,iBAAiB,CAAC,CAChB2D,SAAS,CAAEA,CAAA,GAAM,CACfxB,mBAAmB,CAAEU,IAAI,EACvBA,IAAI,CAAC4B,MAAM,CAAEyC,CAAC,EAAKA,CAAC,CAACpG,EAAE,GAAK0C,IAAI,CAAC1C,EAAE,CACrC,CAAC,CACH,CACF,CAAC,CAAC,CACJ,CAAE,CACFqF,SAAS,CAAC,mLAAmL,CAC7LlF,KAAK,CAAC,QAAQ,CAAAmF,QAAA,cAEdjG,IAAA,SAAMgG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,CAC3D,CAAC,EACN,CAAC,CACJ,CAAC,GAhNE5C,IAAI,CAAC1C,EAiNV,CACL,CACF,CACI,CAAC,EACH,CAAC,CACL,CAAC,EACH,CACN,cAGDX,IAAA,QAAKgG,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvC/F,KAAA,WACEgG,OAAO,CAAEjD,oBAAqB,CAC9B+C,SAAS,CAAC,0MAA0M,CAAAC,QAAA,eAEpNjG,IAAA,SAAMgG,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,KAAG,CAAM,CAAC,wBAE5D,EAAQ,CAAC,CACN,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5F,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}