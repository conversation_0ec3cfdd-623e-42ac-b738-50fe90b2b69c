"use strict";(self.webpackChunkcreativeapp=self.webpackChunkcreativeapp||[]).push([[784],{43784:(e,t,a)=>{a.r(t),a.d(t,{default:()=>j});var s=a(65043),r=a(58786),l=a(13076),n=a(82949),i=a(83003),o=a(47554),d=a(72450),c=a(11238),u=a(78854),m=a(73216),x=a(70579);const g="https://creativeapp.sebpo.net/banner/test/backend/public/api",p=e=>{let{isVisible:t,setVisible:a,dataItemsId:r}=e;const[l,i]=(0,s.useState)(""),[o,d]=(0,s.useState)(""),[c,u]=(0,s.useState)(""),[m,p]=(0,s.useState)(null);(0,s.useEffect)((()=>{const e=localStorage.getItem("user_id");e&&p(e)}),[]),(0,s.useEffect)((()=>{(async()=>{if(r){const e=localStorage.getItem("token");try{const t=await fetch(`${g}/onsite_statuses/${r}`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to fetch Onsite status: "+t.statusText);const a=await t.json();i(a.onsite_status.name)}catch(o){d(o.message)}}})()}),[r]);return t?(0,x.jsx)("div",{className:"fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50",onClick:()=>a(!1),children:(0,x.jsxs)("div",{className:"relative bg-white rounded-lg shadow-lg max-w-md w-full p-5",onClick:e=>e.stopPropagation(),children:[(0,x.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,x.jsx)("h3",{className:"text-lg font-semibold",children:"Update Onsite Status"}),(0,x.jsx)("button",{className:"text-gray-500 hover:text-gray-800",onClick:()=>a(!1),children:"\xd7"})]}),o&&(0,x.jsx)("div",{className:"text-red-500",children:o}),c&&(0,x.jsx)("div",{className:"text-green-500",children:c}),(0,x.jsxs)("form",{onSubmit:async e=>{e.preventDefault();const t=localStorage.getItem("token"),s=m;if(s)if(t)try{const e=await fetch(`${g}/onsite_statuses/${r}`,{method:"PUT",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({name:l.trim(),updated_by:s})});if(!e.ok)throw new Error("Failed to update Onsite Status: "+e.statusText);const i=await e.json();(0,n.GW)({icon:"success",title:"Success!",text:(null===i||void 0===i?void 0:i.message)||"Onsite status updated successfully."}),setTimeout((()=>{a(!1),u("")}),1e3)}catch(o){(0,n.GW)("error")}else d("Authentication token is missing.");else d("User is not logged in.")},children:[(0,x.jsxs)("div",{className:"mb-4",children:[(0,x.jsx)("label",{htmlFor:"name",className:"block mb-2",children:"Onsite Status Name"}),(0,x.jsx)("input",{type:"text",id:"name",value:l,onChange:e=>i(e.target.value),className:"border rounded w-full p-2",required:!0})]}),(0,x.jsx)("button",{type:"submit",className:"bg-primary hover:bg-secondary text-white rounded-md px-4 py-2",children:"Update Onsite Status"})]})]})}):null};var h=a(76416),f=a(17974),b=a(58598);const y="On-site Status",v=()=>{const[e,t]=(0,s.useState)({}),[a,g]=(0,s.useState)({}),[v,j]=(0,s.useState)(""),[w,N]=(0,s.useState)(""),[_,k]=(0,s.useState)(!1),[S,C]=(0,s.useState)(!1),[O,$]=(0,s.useState)(null),[A,F]=(0,s.useState)(null),[R,E]=(0,s.useState)(null),[T,P]=((0,m.Zp)(),(0,s.useState)(!1)),[D,I]=(0,s.useState)("created_at"),[M,U]=(0,s.useState)("desc"),[V,z]=(0,s.useState)("10"),[W,B]=(0,s.useState)(1),{data:q,isFetching:G,error:L}=(0,u.s19)({sort_by:D,order:M,page:W,per_page:V,query:w}),[H,{data:Q,error:J}]=(0,u.RcC)(),[Y]=(0,u.mcW)(),Z=e=>{let t=Object.entries(e).reduce(((e,t)=>{let[a,s]=t;if("string"===typeof s)return e+`&${a}=${s}`;if(Array.isArray(s)){return e+`&${a}=${s.map((e=>e.value)).join(",")}`}return e}),"");N(t)},K=e=>{(0,o.$3)(e,["id","team","department","updated_at","updated_by","updater","created_at","creator","created_by","updated_by"]);E(null),k(!0)},X=e=>{E(null),$(e),k(!0)},ee=e=>{(0,n.YU)({onConfirm:()=>{Y(e),E(null)}})};let te=1;const{rolePermissions:ae}=(0,f.h)(),[se,re]=(0,s.useState)((()=>[{id:te++,name:"Action",width:"180px",className:"bg-red-300",cell:e=>(0,x.jsxs)("div",{className:"flex gap-1 mx-2 !min-w-[200px] pl-3",children:[(0,x.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>E(e),children:(0,x.jsx)("span",{className:"material-symbols-outlined text-lg",children:"visibility"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,x.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>X(e.id),children:(0,x.jsx)("span",{className:"material-symbols-outlined text-lg",children:"stylus_note"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,x.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>K(e),children:(0,x.jsx)("span",{className:"material-symbols-outlined text-lg",children:"content_copy"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,x.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>ee(e.id),children:(0,x.jsx)("span",{className:"material-symbols-outlined text-sm",children:"delete"})})]})},{id:te++,name:"S.No",selector:(e,t)=>(W-1)*V+t+1,width:"80px",omit:!1},{id:te++,name:"Resource Status Name",db_field:"name",selector:e=>e.name||"",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created by",selector:e=>{var t,a;return`${(null===(t=e.creator)||void 0===t?void 0:t.fname)||""} ${(null===(a=e.creator)||void 0===a?void 0:a.lname)||""}`},db_field:"created_by",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created Date",selector:e=>(0,b.hb)(e.created_at),db_field:"created_at",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created Time",selector:e=>(0,b.DF)(e.created_at),db_field:"created_at",omit:!1,sortable:!0,filterable:!1},{id:te++,name:"Updated by",selector:e=>{var t,a;return`${(null===(t=e.updater)||void 0===t?void 0:t.fname)||""} ${(null===(a=e.updater)||void 0===a?void 0:a.lname)||""}`},db_field:"updated_by",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Updated Date",selector:e=>(0,b.hb)(e.updated_at),db_field:"updated_at",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Updated Time",selector:e=>(0,b.DF)(e.updated_at),db_field:"updated_at",omit:!1,sortable:!0,filterable:!1}]));(0,s.useEffect)((()=>{re((e=>[...e.map((e=>"Action"===e.name?{...e,cell:e=>(0,x.jsxs)("div",{className:"flex gap-1 mx-2 !min-w-[200px] pl-3",children:[(0,x.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>E(e),children:(0,x.jsx)("span",{className:"material-symbols-outlined text-lg",children:"visibility"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,x.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>X(e.id),children:(0,x.jsx)("span",{className:"material-symbols-outlined text-lg",children:"stylus_note"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,x.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>K(e),children:(0,x.jsx)("span",{className:"material-symbols-outlined text-lg",children:"content_copy"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,x.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>ee(e.id),children:(0,x.jsx)("span",{className:"material-symbols-outlined text-sm",children:"delete"})})]})}:e))]))}),[ae]);const le=(0,i.wA)(),ne=(0,s.useCallback)((async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"group",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"select",l=e.db_field||"title";try{j(l),C(!0);var n=[];const i=await H({type:a.trim(),column:l.trim(),text:s.trim()});if(i.data&&(n=i.data),n.length){if("searchable"===r)return t((e=>({...e,[l]:n}))),n;const a=n.map((t=>{if(e.selector){let a=e.selector(t);return a?(t.total&&t.total>1&&(a+=` (${t.total})`),{label:a,value:t[l]}):null}})).filter(Boolean);return t((t=>({...t,[e.id]:(0,o.eb)(a)}))),a}}catch(A){F(A.message)}finally{C(!1)}}),[]);return(0,x.jsx)("section",{className:"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]",children:(0,x.jsxs)("div",{className:"mx-auto pb-6 ",children:[(0,x.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4",children:[(0,x.jsx)("div",{className:"w-4/12 md:w-10/12 text-start",children:(0,x.jsx)("h2",{className:"text-2xl font-bold ",children:y})}),(0,x.jsxs)("div",{className:"w-8/12 flex items-end justify-end gap-1",children:[(0,x.jsx)(n.DF,{columns:se,setColumns:re}),!G&&q&&parseInt(q.total)>0&&(0,x.jsx)(x.Fragment,{children:(0,x.jsxs)("button",{className:"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:async()=>{try{const t=await le(u.fej.endpoints.getOnsiteStatusData.initiate({sort_by:D,order:M,page:W,per_page:(null===q||void 0===q?void 0:q.total)||10,query:w})).unwrap();if(null===t||void 0===t||!t.total||t.total<1)return!1;var e=1;let a=t.data.map((t=>{if(se.length){let a={};return se.forEach((s=>{!s.omit&&s.selector&&(a[s.name]="S.No"===s.name?e++:s.selector(t)||"")})),a}}));const s=c.Wp.json_to_sheet(a),r=c.Wp.book_new();c.Wp.book_append_sheet(r,s,"Sheet1");const l=c.M9(r,{bookType:"xlsx",type:"array"}),n=new Blob([l],{type:"application/octet-stream"});(0,d.saveAs)(n,`${y.replace(/ /g,"_")}_${a.length}.xlsx`)}catch(A){console.error("Error exporting to Excel:",A)}},children:[G&&(0,x.jsx)(x.Fragment,{children:(0,x.jsx)("span",{className:"material-symbols-outlined animate-spin text-sm me-2",children:"progress_activity"})}),!G&&(0,x.jsx)("span",{className:"material-symbols-outlined text-sm me-2",children:"file_export"}),"Export to Excel (",q.total,")"]})}),ae.hasManagerRole&&(0,x.jsx)("button",{className:" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:()=>P(!0),children:"Add New"})]})]}),(0,x.jsx)(n.$6,{columns:se,selectedFilterOptions:a,setSelectedFilterOptions:g,fetchDataOptionsForFilterBy:ne,filterOptions:e,filterOptionLoading:S,showFilterOption:v,resetPage:()=>{if(Object.keys(a).length){let e={};Object.keys(a).map((t=>{"string"===typeof a[t]?e[t]="":e[t]=[]})),g({...e}),Z({...e})}B(1)},setCurrentPage:B,buildQueryParams:Z}),L&&(0,x.jsx)("div",{className:"text-red-500",children:A}),G&&(0,x.jsx)(l.A,{}),(0,x.jsx)("div",{className:"border border-gray-200 p-0 pb-1 rounded-lg my-5 ",children:(0,x.jsx)(r.Ay,{columns:se,data:(null===q||void 0===q?void 0:q.data)||[],className:"p-0 scrollbar-horizontal-10",fixedHeader:!0,highlightOnHover:!0,responsive:!0,pagination:!0,paginationServer:!0,paginationPerPage:V,paginationTotalRows:(null===q||void 0===q?void 0:q.total)||0,onChangePage:e=>{e!==W&&B(e)},onChangeRowsPerPage:e=>{e!==V&&(z(e),B(1))},paginationComponentOptions:{selectAllRowsItem:!0,selectAllRowsItemText:"ALL"},sortServer:!0,onSort:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"desc";Object.keys(e).length&&(I(e.db_field||e.name||"created_at"),U(t||"desc"))}})}),T&&(0,x.jsx)(h.A,{isVisible:T,setVisible:P}),_&&(0,x.jsx)(p,{isVisible:_,setVisible:k,dataItemsId:O}),R&&(0,x.jsx)(n.Qg,{item:R,setViewData:E,columns:se,handleEdit:X,handleDelete:ee})]})})},j=()=>(0,x.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-xl",children:(0,x.jsx)(v,{})})}}]);
//# sourceMappingURL=784.8c81fe5e.chunk.js.map