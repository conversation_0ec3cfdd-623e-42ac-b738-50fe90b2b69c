"use strict";(self.webpackChunkcreativeapp=self.webpackChunkcreativeapp||[]).push([[544],{19544:(e,t,a)=>{a.r(t),a.d(t,{default:()=>j});var s=a(65043),r=a(58786),l=a(13076),n=a(82949),o=a(83003),i=a(47554),d=a(72450),c=a(11238),u=a(78854),m=a(73216),p=a(70579);const x="https://creativeapp.sebpo.net/banner/test/backend/public/api",g=e=>{let{isVisible:t,setVisible:a,dataItemsId:r}=e;const[l,o]=(0,s.useState)(""),[i,d]=(0,s.useState)(""),[c,u]=(0,s.useState)(""),[m,g]=(0,s.useState)(null);(0,s.useEffect)((()=>{const e=localStorage.getItem("user_id");e&&g(e)}),[]),(0,s.useEffect)((()=>{(async()=>{if(r){const e=localStorage.getItem("token");try{const t=await fetch(`${x}/contact_types/${r}`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to fetch contact type: "+t.statusText);const a=await t.json();o(a.contact_type.name)}catch(i){d(i.message)}}})()}),[r]);return t?(0,p.jsx)("div",{className:"fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50",onClick:()=>a(!1),children:(0,p.jsxs)("div",{className:"relative bg-white rounded-lg shadow-lg max-w-md w-full p-5",onClick:e=>e.stopPropagation(),children:[(0,p.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,p.jsx)("h3",{className:"text-lg font-semibold",children:"Update Contact Type"}),(0,p.jsx)("button",{className:"text-gray-500 hover:text-gray-800",onClick:()=>a(!1),children:"\xd7"})]}),i&&(0,p.jsx)("div",{className:"text-red-500",children:i}),c&&(0,p.jsx)("div",{className:"text-green-500",children:c}),(0,p.jsxs)("form",{onSubmit:async e=>{e.preventDefault();const t=m;if(!t)return void d("User is not logged in.");const s=localStorage.getItem("token");if(s)try{const e=await fetch(`${x}/contact_types/${r}`,{method:"PUT",headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"},body:JSON.stringify({name:l.trim(),updated_by:t})});if(!e.ok)throw new Error("Failed to update contact type: "+e.statusText);const o=await e.json();(0,n.GW)({icon:"success",title:"Success!",text:(null===o||void 0===o?void 0:o.message)||"Contact type updated successfully."}),setTimeout((()=>{a(!1),u("")}),1e3)}catch(i){(0,n.GW)("error")}else d("Authentication token is missing.")},children:[(0,p.jsxs)("div",{className:"mb-4",children:[(0,p.jsx)("label",{htmlFor:"name",className:"block mb-2",children:"Contact Type Name"}),(0,p.jsx)("input",{type:"text",id:"name",value:l,onChange:e=>o(e.target.value),className:"border rounded w-full p-2",required:!0})]}),(0,p.jsx)("button",{type:"submit",className:"bg-primary hover:bg-secondary text-white rounded-md px-4 py-2",children:"Update Contact Type"})]})]})}):null};var h=a(11458),f=a(17974),y=a(58598);const b="Contact Type",v=()=>{const[e,t]=(0,s.useState)({}),[a,x]=(0,s.useState)({}),[v,j]=(0,s.useState)(""),[w,N]=(0,s.useState)(""),[_,k]=(0,s.useState)(!1),[C,S]=(0,s.useState)(!1),[$,A]=(0,s.useState)(null),[T,F]=(0,s.useState)(null),[O,E]=(0,s.useState)(null),[R,P]=((0,m.Zp)(),(0,s.useState)(!1)),[D,I]=(0,s.useState)("created_at"),[M,U]=(0,s.useState)("desc"),[V,z]=(0,s.useState)("10"),[B,W]=(0,s.useState)(1),{data:L,isFetching:q,error:G}=(0,u.kn6)({sort_by:D,order:M,page:B,per_page:V,query:w}),[H,{data:Q,error:J}]=(0,u.Vl)(),[Y]=(0,u.ap5)(),Z=e=>{let t=Object.entries(e).reduce(((e,t)=>{let[a,s]=t;if("string"===typeof s)return e+`&${a}=${s}`;if(Array.isArray(s)){return e+`&${a}=${s.map((e=>e.value)).join(",")}`}return e}),"");N(t)},K=e=>{(0,i.$3)(e,["id","team","department","updated_at","updated_by","updater","created_at","creator","created_by","updated_by"]);E(null),k(!0)},X=e=>{E(null),A(e),k(!0)},ee=e=>{(0,n.YU)({onConfirm:()=>{Y(e),E(null)}})};let te=1;const{rolePermissions:ae}=(0,f.h)(),[se,re]=(0,s.useState)((()=>[{id:te++,name:"Action",width:"180px",className:"bg-red-300",cell:e=>(0,p.jsxs)("div",{className:"flex gap-1 mx-2 !min-w-[200px] pl-3",children:[(0,p.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>E(e),children:(0,p.jsx)("span",{className:"material-symbols-outlined text-lg",children:"visibility"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,p.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>X(e.id),children:(0,p.jsx)("span",{className:"material-symbols-outlined text-lg",children:"stylus_note"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,p.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>K(e),children:(0,p.jsx)("span",{className:"material-symbols-outlined text-lg",children:"content_copy"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,p.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>ee(e.id),children:(0,p.jsx)("span",{className:"material-symbols-outlined text-sm",children:"delete"})})]})},{id:te++,name:"S.No",selector:(e,t)=>(B-1)*V+t+1,width:"80px",omit:!1},{id:te++,name:"Resource Status Name",db_field:"name",selector:e=>e.name||"",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created by",selector:e=>{var t,a;return`${(null===(t=e.creator)||void 0===t?void 0:t.fname)||""} ${(null===(a=e.creator)||void 0===a?void 0:a.lname)||""}`},db_field:"created_by",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created Date",selector:e=>(0,y.hb)(e.created_at),db_field:"created_at",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created Time",selector:e=>(0,y.DF)(e.created_at),db_field:"created_at",omit:!1,sortable:!0,filterable:!1},{id:te++,name:"Updated by",selector:e=>{var t,a;return`${(null===(t=e.updater)||void 0===t?void 0:t.fname)||""} ${(null===(a=e.updater)||void 0===a?void 0:a.lname)||""}`},db_field:"updated_by",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Updated Date",selector:e=>(0,y.hb)(e.updated_at),db_field:"updated_at",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Updated Time",selector:e=>(0,y.DF)(e.updated_at),db_field:"updated_at",omit:!1,sortable:!0,filterable:!1}]));(0,s.useEffect)((()=>{re((e=>[...e.map((e=>"Action"===e.name?{...e,cell:e=>(0,p.jsxs)("div",{className:"flex gap-1 mx-2 !min-w-[200px] pl-3",children:[(0,p.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>E(e),children:(0,p.jsx)("span",{className:"material-symbols-outlined text-lg",children:"visibility"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,p.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>X(e.id),children:(0,p.jsx)("span",{className:"material-symbols-outlined text-lg",children:"stylus_note"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,p.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>K(e),children:(0,p.jsx)("span",{className:"material-symbols-outlined text-lg",children:"content_copy"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,p.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>ee(e.id),children:(0,p.jsx)("span",{className:"material-symbols-outlined text-sm",children:"delete"})})]})}:e))]))}),[ae]);const le=(0,o.wA)(),ne=(0,s.useCallback)((async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"group",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"select",l=e.db_field||"title";try{j(l),S(!0);var n=[];const o=await H({type:a.trim(),column:l.trim(),text:s.trim()});if(o.data&&(n=o.data),n.length){if("searchable"===r)return t((e=>({...e,[l]:n}))),n;const a=n.map((t=>{if(e.selector){let a=e.selector(t);return a?(t.total&&t.total>1&&(a+=` (${t.total})`),{label:a,value:t[l]}):null}})).filter(Boolean);return t((t=>({...t,[e.id]:(0,i.eb)(a)}))),a}}catch(T){F(T.message)}finally{S(!1)}}),[]);return(0,p.jsx)("section",{className:"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]",children:(0,p.jsxs)("div",{className:"mx-auto pb-6 ",children:[(0,p.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4",children:[(0,p.jsx)("div",{className:"w-4/12 md:w-10/12 text-start",children:(0,p.jsx)("h2",{className:"text-2xl font-bold ",children:b})}),(0,p.jsxs)("div",{className:"w-8/12 flex items-end justify-end gap-1",children:[(0,p.jsx)(n.DF,{columns:se,setColumns:re}),!q&&L&&parseInt(L.total)>0&&(0,p.jsx)(p.Fragment,{children:(0,p.jsxs)("button",{className:"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:async()=>{try{const t=await le(u.tCL.endpoints.getContactTypeData.initiate({sort_by:D,order:M,page:B,per_page:(null===L||void 0===L?void 0:L.total)||10,query:w})).unwrap();if(null===t||void 0===t||!t.total||t.total<1)return!1;var e=1;let a=t.data.map((t=>{if(se.length){let a={};return se.forEach((s=>{!s.omit&&s.selector&&(a[s.name]="S.No"===s.name?e++:s.selector(t)||"")})),a}}));const s=c.Wp.json_to_sheet(a),r=c.Wp.book_new();c.Wp.book_append_sheet(r,s,"Sheet1");const l=c.M9(r,{bookType:"xlsx",type:"array"}),n=new Blob([l],{type:"application/octet-stream"});(0,d.saveAs)(n,`${b.replace(/ /g,"_")}_${a.length}.xlsx`)}catch(T){console.error("Error exporting to Excel:",T)}},children:[q&&(0,p.jsx)(p.Fragment,{children:(0,p.jsx)("span",{className:"material-symbols-outlined animate-spin text-sm me-2",children:"progress_activity"})}),!q&&(0,p.jsx)("span",{className:"material-symbols-outlined text-sm me-2",children:"file_export"}),"Export to Excel (",L.total,")"]})}),ae.hasManagerRole&&(0,p.jsx)("button",{className:" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:()=>P(!0),children:"Add New"})]})]}),(0,p.jsx)(n.$6,{columns:se,selectedFilterOptions:a,setSelectedFilterOptions:x,fetchDataOptionsForFilterBy:ne,filterOptions:e,filterOptionLoading:C,showFilterOption:v,resetPage:()=>{if(Object.keys(a).length){let e={};Object.keys(a).map((t=>{"string"===typeof a[t]?e[t]="":e[t]=[]})),x({...e}),Z({...e})}W(1)},setCurrentPage:W,buildQueryParams:Z}),G&&(0,p.jsx)("div",{className:"text-red-500",children:T}),q&&(0,p.jsx)(l.A,{}),(0,p.jsx)("div",{className:"border border-gray-200 p-0 pb-1 rounded-lg my-5 ",children:(0,p.jsx)(r.Ay,{columns:se,data:(null===L||void 0===L?void 0:L.data)||[],className:"p-0 scrollbar-horizontal-10",fixedHeader:!0,highlightOnHover:!0,responsive:!0,pagination:!0,paginationServer:!0,paginationPerPage:V,paginationTotalRows:(null===L||void 0===L?void 0:L.total)||0,onChangePage:e=>{e!==B&&W(e)},onChangeRowsPerPage:e=>{e!==V&&(z(e),W(1))},paginationComponentOptions:{selectAllRowsItem:!0,selectAllRowsItemText:"ALL"},sortServer:!0,onSort:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"desc";Object.keys(e).length&&(I(e.db_field||e.name||"created_at"),U(t||"desc"))}})}),R&&(0,p.jsx)(h.A,{isVisible:R,setVisible:P}),_&&(0,p.jsx)(g,{isVisible:_,setVisible:k,dataItemsId:$}),O&&(0,p.jsx)(n.Qg,{item:O,setViewData:E,columns:se,handleEdit:X,handleDelete:ee})]})})},j=()=>(0,p.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-xl",children:(0,p.jsx)(v,{})})}}]);
//# sourceMappingURL=544.4d51e638.chunk.js.map