{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\components\\\\password-manager\\\\PasswordCardsTable.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport AddPasswordCardForm from \"./AddPasswordCardForm\";\nimport { confirmationAlert } from \"../../common/coreui\";\nimport FetchLoggedInRole from \"../../common/fetchData/FetchLoggedInRole\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PasswordCardsTable = ({\n  generatedPassword,\n  passwordStrength\n}) => {\n  _s();\n  // Get current user data\n  const {\n    userData\n  } = FetchLoggedInRole();\n  const currentUserId = userData === null || userData === void 0 ? void 0 : userData.id;\n\n  // Sample data - in real app this would come from API/state management\n  const [passwordCards, setPasswordCards] = useState([{\n    id: 1,\n    title: \"Gmail Account\",\n    platform: \"Gmail\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn\",\n    team: \"Team Name\",\n    department: \"IT\",\n    strength: \"Weak Password\",\n    strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n    authorId: currentUserId // Add author ID\n  }, {\n    id: 2,\n    title: \"Slack Workspace\",\n    platform: \"Slack\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"StrongPass123!@#\",\n    team: \"Team Name\",\n    department: \"IT\",\n    strength: \"Strong Password\",\n    strengthColor: \"bg-green-100 text-green-600 border-green-300\",\n    authorId: currentUserId\n  }, {\n    id: 3,\n    title: \"GitHub Repository\",\n    platform: \"GitHub\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"ModeratePass456\",\n    team: \"Team Name\",\n    department: \"Development\",\n    strength: \"Moderate Password\",\n    strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\",\n    authorId: 999 // Different author ID to test permissions\n  }, {\n    id: 4,\n    title: \"AWS Console\",\n    platform: \"AWS\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"WeakPass\",\n    team: \"Team Name\",\n    department: \"DevOps\",\n    strength: \"Weak Password\",\n    strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n    authorId: currentUserId\n  }, {\n    id: 5,\n    title: \"Jira Project\",\n    platform: \"Jira\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"AnotherStrongPass789!\",\n    team: \"Team Name\",\n    department: \"Project Management\",\n    strength: \"Strong Password\",\n    strengthColor: \"bg-green-100 text-green-600 border-green-300\",\n    authorId: 998 // Different author ID to test permissions\n  }, {\n    id: 6,\n    title: \"Office 365\",\n    platform: \"Microsoft 365\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"ModerateSecure123\",\n    team: \"Team Name\",\n    department: \"HR\",\n    strength: \"Moderate Password\",\n    strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\",\n    authorId: currentUserId\n  }, {\n    id: 7,\n    title: \"Database Admin\",\n    platform: \"MySQL\",\n    username: \"<EMAIL>\",\n    password: \"••••••••••••\",\n    actualPassword: \"VeryWeakPass\",\n    team: \"Team Name\",\n    department: \"Database\",\n    strength: \"Weak Password\",\n    strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n    authorId: currentUserId\n  }]);\n  const [visiblePasswords, setVisiblePasswords] = useState({});\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [showAddFormNew, setShowAddFormNew] = useState(false);\n  const [showNewTable, setShowNewTable] = useState(false);\n  const [showTeamTable] = useState(true);\n  const [newPasswordCards, setNewPasswordCards] = useState([]);\n  const [editingRowId, setEditingRowId] = useState(null);\n  const [shareableCards, setShareableCards] = useState([]);\n  const [shareableNewCards, setShareableNewCards] = useState([]);\n  const [sortConfig, setSortConfig] = useState({\n    key: null,\n    direction: \"asc\"\n  });\n  const [sortConfigNew, setSortConfigNew] = useState({\n    key: null,\n    direction: \"asc\"\n  });\n  const togglePasswordVisibility = id => {\n    setVisiblePasswords(prev => ({\n      ...prev,\n      [id]: !prev[id]\n    }));\n  };\n  const copyToClipboard = text => {\n    navigator.clipboard.writeText(text);\n    // You could add a toast notification here\n  };\n\n  // Handle individual row editing\n  const handleEdit = id => {\n    setEditingRowId(editingRowId === id ? null : id);\n  };\n\n  // Handle creating new table\n  const handleCreateNewTable = () => {\n    setShowNewTable(true);\n  };\n\n  // Handle delete entire team table - only author can delete\n  const handleDeleteTeamTable = () => {\n    // Check if current user is author of any cards\n    const isAuthor = passwordCards.some(card => card.authorId === currentUserId);\n    if (!isAuthor) {\n      alert(\"Only the author can delete the entire table.\");\n      return;\n    }\n\n    // Only proceed if there are selected cards or if user is author\n    if (shareableCards.length === 0) {\n      alert(\"Please select at least one row to delete the table.\");\n      return;\n    }\n    confirmationAlert({\n      onConfirm: () => {\n        setPasswordCards([]);\n      }\n    });\n  };\n\n  // Handle select all checkboxes for main table - only author's cards\n  const toggleSelectAll = () => {\n    const authorCards = passwordCards.filter(card => card.authorId === currentUserId);\n    if (shareableCards.length === authorCards.length) {\n      setShareableCards([]);\n    } else {\n      setShareableCards(authorCards.map(card => card.id));\n    }\n  };\n\n  // Handle select all checkboxes for new table - only author's cards\n  const toggleSelectAllNew = () => {\n    const authorCards = newPasswordCards.filter(card => card.authorId === currentUserId);\n    if (shareableNewCards.length === authorCards.length) {\n      setShareableNewCards([]);\n    } else {\n      setShareableNewCards(authorCards.map(card => card.id));\n    }\n  };\n\n  // Handle sorting for main table\n  const handleSort = key => {\n    let direction = \"asc\";\n    if (sortConfig.key === key && sortConfig.direction === \"asc\") {\n      direction = \"desc\";\n    }\n    setSortConfig({\n      key,\n      direction\n    });\n  };\n\n  // Handle sorting for new table\n  const handleSortNew = key => {\n    let direction = \"asc\";\n    if (sortConfigNew.key === key && sortConfigNew.direction === \"asc\") {\n      direction = \"desc\";\n    }\n    setSortConfigNew({\n      key,\n      direction\n    });\n  };\n\n  // Sort cards based on current sort config for main table\n  const getSortedCards = cards => {\n    if (!sortConfig.key) return cards;\n    return [...cards].sort((a, b) => {\n      const aValue = a[sortConfig.key].toLowerCase();\n      const bValue = b[sortConfig.key].toLowerCase();\n      if (sortConfig.direction === \"asc\") {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n  };\n\n  // Sort cards based on current sort config for new table\n  const getSortedCardsNew = cards => {\n    if (!sortConfigNew.key) return cards;\n    return [...cards].sort((a, b) => {\n      const aValue = a[sortConfigNew.key].toLowerCase();\n      const bValue = b[sortConfigNew.key].toLowerCase();\n      if (sortConfigNew.direction === \"asc\") {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n  };\n\n  // Handle shareable toggle for main table - only author can select\n  const toggleShareable = id => {\n    const card = passwordCards.find(c => c.id === id);\n    if (card && card.authorId !== currentUserId) {\n      alert(\"Only the author can select this row.\");\n      return;\n    }\n    setShareableCards(prev => prev.includes(id) ? prev.filter(cardId => cardId !== id) : [...prev, id]);\n  };\n\n  // Handle shareable toggle for new table - only author can select\n  const toggleShareableNew = id => {\n    const card = newPasswordCards.find(c => c.id === id);\n    if (card && card.authorId !== currentUserId) {\n      alert(\"Only the author can select this row.\");\n      return;\n    }\n    setShareableNewCards(prev => prev.includes(id) ? prev.filter(cardId => cardId !== id) : [...prev, id]);\n  };\n\n  // Handle form submission for main table password cards\n  const handleAddPasswordCard = cardData => {\n    const newCard = {\n      ...cardData,\n      id: Date.now(),\n      password: \"••••••••••••\",\n      actualPassword: cardData.password,\n      authorId: currentUserId // Add current user as author\n    };\n    setPasswordCards(prev => [...prev, newCard]);\n    setShowAddForm(false);\n  };\n\n  // Handle form submission for new table password cards\n  const handleAddPasswordCardNew = cardData => {\n    const newCard = {\n      ...cardData,\n      id: Date.now(),\n      password: \"••••••••••••\",\n      actualPassword: cardData.password,\n      authorId: currentUserId // Add current user as author\n    };\n    setNewPasswordCards(prev => [...prev, newCard]);\n    setShowAddFormNew(false);\n  };\n  const handleDelete = id => {\n    confirmationAlert({\n      onConfirm: () => {\n        setPasswordCards(prev => prev.filter(card => card.id !== id));\n      }\n    });\n  };\n\n  // Handle share functionality for department - only author can share\n  const handleShare = () => {\n    var _userData$departments, _userData$departments2;\n    // Check if current user is author of any cards\n    const isAuthor = passwordCards.some(card => card.authorId === currentUserId);\n    if (!isAuthor) {\n      alert(\"Only the author can share the table.\");\n      return;\n    }\n\n    // Get current user's department or team\n    const currentUserDepartment = (userData === null || userData === void 0 ? void 0 : (_userData$departments = userData.departments) === null || _userData$departments === void 0 ? void 0 : (_userData$departments2 = _userData$departments[0]) === null || _userData$departments2 === void 0 ? void 0 : _userData$departments2.name) || \"IT\";\n\n    // Filter cards that belong to the same department and are authored by current user\n    const departmentCards = passwordCards.filter(card => card.department === currentUserDepartment && card.authorId === currentUserId);\n    if (departmentCards.length === 0) {\n      alert(\"No password cards available to share in your department.\");\n      return;\n    }\n\n    // Create shareable data\n    const shareData = {\n      title: `${currentUserDepartment} Department Password Cards`,\n      cards: departmentCards,\n      sharedBy: `${userData === null || userData === void 0 ? void 0 : userData.fname} ${userData === null || userData === void 0 ? void 0 : userData.lname}` || \"Current User\",\n      sharedAt: new Date().toISOString()\n    };\n\n    // For now, copy to clipboard (you can implement actual sharing logic)\n    navigator.clipboard.writeText(JSON.stringify(shareData, null, 2)).then(() => {\n      alert(`${departmentCards.length} password cards from ${currentUserDepartment} department copied to clipboard!`);\n    }).catch(() => {\n      alert(\"Failed to copy to clipboard. Please try again.\");\n    });\n  };\n\n  // Placeholder avatar images\n  const avatarImages = [\"https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=A\", \"https://via.placeholder.com/32x32/10B981/FFFFFF?text=B\", \"https://via.placeholder.com/32x32/F59E0B/FFFFFF?text=C\", \"https://via.placeholder.com/32x32/EF4444/FFFFFF?text=D\"];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white dark:bg-gray-900\",\n    children: [showTeamTable && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-left text-2xl font-bold text-gray-900 dark:text-white\",\n            children: \"Teams Password Card\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleShare,\n            className: \"flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-rounded\",\n              children: \"share\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleDeleteTeamTable(),\n            className: \"flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg\",\n            title: \"Delete entire table\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-outlined text-sm\",\n              children: \"delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex -space-x-2\",\n            children: avatarImages.map((avatar, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n              src: avatar,\n              alt: `User ${index + 1}`,\n              className: \"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setEditingRowId(null);\n              setShowAddForm(!showAddForm);\n            },\n            className: `w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${showAddForm ? \"bg-primary text-white\" : \"bg-transparent text-primary border-2 border-primary\"}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-rounded mr-2\",\n              children: \"add\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this), \"Add Password\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 11\n      }, this), showAddForm && /*#__PURE__*/_jsxDEV(AddPasswordCardForm, {\n        onSubmit: handleAddPasswordCard,\n        onCancel: () => {\n          setShowAddForm(false);\n          setEditingRowId(null);\n        },\n        generatedPassword: generatedPassword,\n        passwordStrength: passwordStrength\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"w-12 px-4 py-3 text-left\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"checkbox-all\",\n                  type: \"checkbox\",\n                  checked: shareableCards.length === passwordCards.length && passwordCards.length > 0,\n                  onChange: toggleSelectAll,\n                  className: \"w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  onClick: () => handleSort(\"title\"),\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Title\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"title\" && sortConfig.direction === \"asc\" ? \"text-primary\" : \"text-gray-400\"}`,\n                      children: \"\\u25B2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"title\" && sortConfig.direction === \"desc\" ? \"text-primary\" : \"text-gray-400\"}`,\n                      children: \"\\u25BC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 452,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  onClick: () => handleSort(\"username\"),\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"User Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"username\" && sortConfig.direction === \"asc\" ? \"text-primary\" : \"text-gray-400\"}`,\n                      children: \"\\u25B2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 472,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"username\" && sortConfig.direction === \"desc\" ? \"text-primary\" : \"text-gray-400\"}`,\n                      children: \"\\u25BC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 482,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Department\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"divide-y divide-gray-200\",\n            children: getSortedCards(passwordCards).map(card => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: `shareable-${card.id}`,\n                  type: \"checkbox\",\n                  checked: shareableCards.includes(card.id),\n                  onChange: () => toggleShareable(card.id),\n                  disabled: card.authorId !== currentUserId,\n                  className: `w-4 h-4 bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-2 ${card.authorId === currentUserId ? \"text-primary cursor-pointer\" : \"text-gray-400 cursor-not-allowed opacity-50\"}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\",\n                    children: \"TG\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 25\n                  }, this), editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    defaultValue: card.title,\n                    className: \"font-medium text-gray-900 border rounded px-2 py-1 w-full\",\n                    onBlur: e => {\n                      setPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                        ...c,\n                        title: e.target.value\n                      } : c));\n                    },\n                    onKeyDown: e => {\n                      if (e.key === \"Enter\") {\n                        e.target.blur();\n                      }\n                    },\n                    autoFocus: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: card.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    defaultValue: card.username,\n                    className: \"text-gray-900 border rounded px-2 py-1 w-full mr-2\",\n                    onBlur: e => {\n                      setPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                        ...c,\n                        username: e.target.value\n                      } : c));\n                    },\n                    onKeyDown: e => {\n                      if (e.key === \"Enter\") {\n                        e.target.blur();\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900\",\n                    children: card.username\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => copyToClipboard(card.username),\n                    className: \"ml-2 text-gray-400 hover:text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"content_copy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 591,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    defaultValue: card.actualPassword,\n                    className: \"text-gray-900 border rounded px-2 py-1 w-full mr-2\",\n                    onBlur: e => {\n                      setPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                        ...c,\n                        actualPassword: e.target.value\n                      } : c));\n                    },\n                    onKeyDown: e => {\n                      if (e.key === \"Enter\") {\n                        e.target.blur();\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900 mr-2\",\n                    children: visiblePasswords[card.id] ? card.actualPassword : card.password\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => togglePasswordVisibility(card.id),\n                    className: \"text-gray-400 hover:text-gray-600 mr-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: visiblePasswords[card.id] ? \"visibility_off\" : \"visibility\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 630,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => copyToClipboard(card.actualPassword),\n                    className: \"text-gray-400 hover:text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"content_copy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 640,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 636,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  defaultValue: card.team,\n                  className: \"text-gray-900 border rounded px-2 py-1 w-full\",\n                  onBlur: e => {\n                    setPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                      ...c,\n                      team: e.target.value\n                    } : c));\n                  },\n                  onKeyDown: e => {\n                    if (e.key === \"Enter\") {\n                      e.target.blur();\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-900\",\n                  children: card.team\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  defaultValue: card.department,\n                  className: \"text-gray-900 border rounded px-2 py-1 w-full\",\n                  onBlur: e => {\n                    setPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                      ...c,\n                      department: e.target.value\n                    } : c));\n                  },\n                  onKeyDown: e => {\n                    if (e.key === \"Enter\") {\n                      e.target.blur();\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-900\",\n                  children: card.department\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 693,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`,\n                  children: card.strength\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(card.id),\n                    className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n                    title: \"Edit\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-outlined text-lg\",\n                      children: \"stylus_note\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 710,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 705,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(card.id),\n                    className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n                    title: \"Delete\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-outlined text-sm\",\n                      children: \"delete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 717,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 712,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 21\n              }, this)]\n            }, card.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), showNewTable && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-left text-2xl font-bold text-gray-900 dark:text-white\",\n            children: \"Teams Password Card\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleShare,\n            className: \"flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-rounded\",\n              children: \"share\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleDeleteTeamTable(),\n            className: \"flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg\",\n            title: \"Delete entire table\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-outlined text-sm\",\n              children: \"delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 748,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex -space-x-2\",\n            children: avatarImages.map((avatar, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n              src: avatar,\n              alt: `User ${index + 1}`,\n              className: \"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 759,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 734,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setEditingRowId(null);\n              setShowAddForm(!showAddForm);\n            },\n            className: `w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${showAddForm ? \"bg-primary text-white\" : \"bg-transparent text-primary border-2 border-primary\"}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-rounded mr-2\",\n              children: \"add\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 784,\n              columnNumber: 17\n            }, this), \"Add Password\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 773,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 771,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 733,\n        columnNumber: 11\n      }, this), showAddForm && /*#__PURE__*/_jsxDEV(AddPasswordCardForm, {\n        onSubmit: handleAddPasswordCard,\n        onCancel: () => {\n          setShowAddForm(false);\n          setEditingRowId(null);\n        },\n        generatedPassword: generatedPassword,\n        passwordStrength: passwordStrength\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 792,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"w-12 px-4 py-3 text-left\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"checkbox-all-new\",\n                  type: \"checkbox\",\n                  checked: shareableCards.length === newPasswordCards.length && newPasswordCards.length > 0,\n                  onChange: () => {\n                    if (shareableCards.length === newPasswordCards.length) {\n                      setShareableCards([]);\n                    } else {\n                      setShareableCards(newPasswordCards.map(card => card.id));\n                    }\n                  },\n                  className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 808,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 807,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  onClick: () => handleSort(\"title\"),\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Title\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 830,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"title\" && sortConfig.direction === \"asc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25B2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 832,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"title\" && sortConfig.direction === \"desc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25BC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 842,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 831,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  onClick: () => handleSort(\"username\"),\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"User Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 860,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"username\" && sortConfig.direction === \"asc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25B2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 862,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${sortConfig.key === \"username\" && sortConfig.direction === \"desc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                      children: \"\\u25BC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 872,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 856,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 885,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 888,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Department\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 891,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 894,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-sm font-medium text-gray-700\",\n                children: \"Action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 897,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 806,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 805,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"divide-y divide-gray-200\",\n            children: newPasswordCards.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"8\",\n                className: \"px-6 py-8 text-center text-gray-500\",\n                children: \"No password cards added yet. Click \\\"Add Password\\\" to add your first card.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 905,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 19\n            }, this) : getSortedCards(newPasswordCards).map(card => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: shareableCards.includes(card.id),\n                  onChange: () => toggleShareable(card.id),\n                  className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 917,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 916,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\",\n                    children: \"TG\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 926,\n                    columnNumber: 27\n                  }, this), editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    defaultValue: card.title,\n                    className: \"font-medium text-gray-900 border rounded px-2 py-1 w-full\",\n                    onBlur: e => {\n                      setNewPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                        ...c,\n                        title: e.target.value\n                      } : c));\n                    },\n                    onKeyDown: e => {\n                      if (e.key === \"Enter\") {\n                        e.target.blur();\n                      }\n                    },\n                    autoFocus: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: card.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 951,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 925,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 924,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    defaultValue: card.username,\n                    className: \"text-gray-900 border rounded px-2 py-1 w-full mr-2\",\n                    onBlur: e => {\n                      setNewPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                        ...c,\n                        username: e.target.value\n                      } : c));\n                    },\n                    onKeyDown: e => {\n                      if (e.key === \"Enter\") {\n                        e.target.blur();\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 960,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900\",\n                    children: card.username\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 980,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => copyToClipboard(card.username),\n                    className: \"ml-2 text-gray-400 hover:text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"content_copy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 986,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 982,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 958,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 957,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    defaultValue: card.actualPassword,\n                    className: \"text-gray-900 border rounded px-2 py-1 w-full mr-2\",\n                    onBlur: e => {\n                      setNewPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                        ...c,\n                        actualPassword: e.target.value\n                      } : c));\n                    },\n                    onKeyDown: e => {\n                      if (e.key === \"Enter\") {\n                        e.target.blur();\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 995,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900 mr-2\",\n                    children: visiblePasswords[card.id] ? card.actualPassword : card.password\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1015,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => togglePasswordVisibility(card.id),\n                    className: \"text-gray-400 hover:text-gray-600 mr-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: visiblePasswords[card.id] ? \"visibility_off\" : \"visibility\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1025,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1021,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => copyToClipboard(card.actualPassword),\n                    className: \"text-gray-400 hover:text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-rounded text-sm\",\n                      children: \"content_copy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1035,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1031,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 993,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 992,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  defaultValue: card.team,\n                  className: \"text-gray-900 border rounded px-2 py-1 w-full\",\n                  onBlur: e => {\n                    setNewPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                      ...c,\n                      team: e.target.value\n                    } : c));\n                  },\n                  onKeyDown: e => {\n                    if (e.key === \"Enter\") {\n                      e.target.blur();\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1043,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-900\",\n                  children: card.team\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1063,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1041,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: editingRowId === card.id ? /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  defaultValue: card.department,\n                  className: \"text-gray-900 border rounded px-2 py-1 w-full\",\n                  onBlur: e => {\n                    setNewPasswordCards(prev => prev.map(c => c.id === card.id ? {\n                      ...c,\n                      department: e.target.value\n                    } : c));\n                  },\n                  onKeyDown: e => {\n                    if (e.key === \"Enter\") {\n                      e.target.blur();\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1068,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-900\",\n                  children: card.department\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1088,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1066,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`,\n                  children: card.strength\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1092,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1091,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(card.id),\n                    className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n                    title: \"Edit\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-outlined text-lg\",\n                      children: \"stylus_note\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1105,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1100,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      confirmationAlert({\n                        onConfirm: () => {\n                          setNewPasswordCards(prev => prev.filter(c => c.id !== card.id));\n                        }\n                      });\n                    },\n                    className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n                    title: \"Delete\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"material-symbols-outlined text-sm\",\n                      children: \"delete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1120,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1107,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1099,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1098,\n                columnNumber: 23\n              }, this)]\n            }, card.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 915,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 902,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 731,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center mt-6\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleCreateNewTable,\n        className: \"flex items-center justify-center px-6 py-2 text-sm font-medium text-white rounded-lg bg-primary hover:bg-primary/90 focus:ring-4 focus:ring-primary/30 transition-colors duration-200 focus:outline-none\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-rounded mr-2\",\n          children: \"add\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1139,\n          columnNumber: 11\n        }, this), \"Add New Password Card\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1135,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1134,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 343,\n    columnNumber: 5\n  }, this);\n};\n_s(PasswordCardsTable, \"NWkqzcEobss+UgB/m/ubwFOhj8s=\");\n_c = PasswordCardsTable;\nexport default PasswordCardsTable;\nvar _c;\n$RefreshReg$(_c, \"PasswordCardsTable\");", "map": {"version": 3, "names": ["React", "useState", "AddPasswordCardForm", "<PERSON><PERSON><PERSON><PERSON>", "FetchLoggedInRole", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PasswordCardsTable", "generatedPassword", "passwordStrength", "_s", "userData", "currentUserId", "id", "passwordCards", "setPasswordCards", "title", "platform", "username", "password", "actualPassword", "team", "department", "strength", "strengthColor", "authorId", "visiblePasswords", "setVisiblePasswords", "showAddForm", "setShowAddForm", "showAddFormNew", "setShowAddFormNew", "showNewTable", "setShowNewTable", "showTeamTable", "newPasswordCards", "setNewPasswordCards", "editingRowId", "setEditingRowId", "shareableCards", "setShareableCards", "shareableNewCards", "setShareableNewCards", "sortConfig", "setSortConfig", "key", "direction", "sortConfigNew", "setSortConfigNew", "togglePasswordVisibility", "prev", "copyToClipboard", "text", "navigator", "clipboard", "writeText", "handleEdit", "handleCreateNewTable", "handleDeleteTeamTable", "is<PERSON><PERSON><PERSON>", "some", "card", "alert", "length", "onConfirm", "toggleSelectAll", "authorCards", "filter", "map", "toggleSelectAllNew", "handleSort", "handleSortNew", "getSortedCards", "cards", "sort", "a", "b", "aValue", "toLowerCase", "bValue", "getSortedCardsNew", "toggleShareable", "find", "c", "includes", "cardId", "toggleShareableNew", "handleAddPasswordCard", "cardData", "newCard", "Date", "now", "handleAddPasswordCardNew", "handleDelete", "handleShare", "_userData$departments", "_userData$departments2", "currentUserDepartment", "departments", "name", "departmentCards", "shareData", "sharedBy", "fname", "lname", "sharedAt", "toISOString", "JSON", "stringify", "then", "catch", "avatarImages", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "avatar", "index", "src", "alt", "onSubmit", "onCancel", "type", "checked", "onChange", "disabled", "defaultValue", "onBlur", "e", "target", "value", "onKeyDown", "blur", "autoFocus", "colSpan", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/PasswordCardsTable.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport AddPasswordCardForm from \"./AddPasswordCardForm\";\nimport { confirmationAlert } from \"../../common/coreui\";\nimport FetchLoggedInRole from \"../../common/fetchData/FetchLoggedInRole\";\n\nconst PasswordCardsTable = ({ generatedPassword, passwordStrength }) => {\n  // Get current user data\n  const { userData } = FetchLoggedInRole();\n  const currentUserId = userData?.id;\n\n  // Sample data - in real app this would come from API/state management\n  const [passwordCards, setPasswordCards] = useState([\n    {\n      id: 1,\n      title: \"Gmail Account\",\n      platform: \"Gmail\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn\",\n      team: \"Team Name\",\n      department: \"IT\",\n      strength: \"Weak Password\",\n      strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n      authorId: currentUserId, // Add author ID\n    },\n    {\n      id: 2,\n      title: \"Slack Workspace\",\n      platform: \"Slack\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"StrongPass123!@#\",\n      team: \"Team Name\",\n      department: \"IT\",\n      strength: \"Strong Password\",\n      strengthColor: \"bg-green-100 text-green-600 border-green-300\",\n      authorId: currentUserId,\n    },\n    {\n      id: 3,\n      title: \"GitHub Repository\",\n      platform: \"GitHub\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"ModeratePass456\",\n      team: \"Team Name\",\n      department: \"Development\",\n      strength: \"Moderate Password\",\n      strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\",\n      authorId: 999, // Different author ID to test permissions\n    },\n    {\n      id: 4,\n      title: \"AWS Console\",\n      platform: \"AWS\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"WeakPass\",\n      team: \"Team Name\",\n      department: \"DevOps\",\n      strength: \"Weak Password\",\n      strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n      authorId: currentUserId,\n    },\n    {\n      id: 5,\n      title: \"Jira Project\",\n      platform: \"Jira\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"AnotherStrongPass789!\",\n      team: \"Team Name\",\n      department: \"Project Management\",\n      strength: \"Strong Password\",\n      strengthColor: \"bg-green-100 text-green-600 border-green-300\",\n      authorId: 998, // Different author ID to test permissions\n    },\n    {\n      id: 6,\n      title: \"Office 365\",\n      platform: \"Microsoft 365\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"ModerateSecure123\",\n      team: \"Team Name\",\n      department: \"HR\",\n      strength: \"Moderate Password\",\n      strengthColor: \"bg-yellow-100 text-yellow-600 border-yellow-300\",\n      authorId: currentUserId,\n    },\n    {\n      id: 7,\n      title: \"Database Admin\",\n      platform: \"MySQL\",\n      username: \"<EMAIL>\",\n      password: \"••••••••••••\",\n      actualPassword: \"VeryWeakPass\",\n      team: \"Team Name\",\n      department: \"Database\",\n      strength: \"Weak Password\",\n      strengthColor: \"bg-red-100 text-red-600 border-red-300\",\n      authorId: currentUserId,\n    },\n  ]);\n\n  const [visiblePasswords, setVisiblePasswords] = useState({});\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [showAddFormNew, setShowAddFormNew] = useState(false);\n  const [showNewTable, setShowNewTable] = useState(false);\n  const [showTeamTable] = useState(true);\n  const [newPasswordCards, setNewPasswordCards] = useState([]);\n  const [editingRowId, setEditingRowId] = useState(null);\n  const [shareableCards, setShareableCards] = useState([]);\n  const [shareableNewCards, setShareableNewCards] = useState([]);\n  const [sortConfig, setSortConfig] = useState({ key: null, direction: \"asc\" });\n  const [sortConfigNew, setSortConfigNew] = useState({ key: null, direction: \"asc\" });\n  const togglePasswordVisibility = (id) => {\n    setVisiblePasswords((prev) => ({\n      ...prev,\n      [id]: !prev[id],\n    }));\n  };\n\n  const copyToClipboard = (text) => {\n    navigator.clipboard.writeText(text);\n    // You could add a toast notification here\n  };\n\n  // Handle individual row editing\n  const handleEdit = (id) => {\n    setEditingRowId(editingRowId === id ? null : id);\n  };\n\n  // Handle creating new table\n  const handleCreateNewTable = () => {\n    setShowNewTable(true);\n  };\n\n  // Handle delete entire team table - only author can delete\n  const handleDeleteTeamTable = () => {\n    // Check if current user is author of any cards\n    const isAuthor = passwordCards.some(card => card.authorId === currentUserId);\n\n    if (!isAuthor) {\n      alert(\"Only the author can delete the entire table.\");\n      return;\n    }\n\n    // Only proceed if there are selected cards or if user is author\n    if (shareableCards.length === 0) {\n      alert(\"Please select at least one row to delete the table.\");\n      return;\n    }\n\n    confirmationAlert({\n      onConfirm: () => {\n        setPasswordCards([]);\n      },\n    });\n  };\n\n  // Handle select all checkboxes for main table - only author's cards\n  const toggleSelectAll = () => {\n    const authorCards = passwordCards.filter(card => card.authorId === currentUserId);\n    if (shareableCards.length === authorCards.length) {\n      setShareableCards([]);\n    } else {\n      setShareableCards(authorCards.map((card) => card.id));\n    }\n  };\n\n  // Handle select all checkboxes for new table - only author's cards\n  const toggleSelectAllNew = () => {\n    const authorCards = newPasswordCards.filter(card => card.authorId === currentUserId);\n    if (shareableNewCards.length === authorCards.length) {\n      setShareableNewCards([]);\n    } else {\n      setShareableNewCards(authorCards.map((card) => card.id));\n    }\n  };\n\n  // Handle sorting for main table\n  const handleSort = (key) => {\n    let direction = \"asc\";\n    if (sortConfig.key === key && sortConfig.direction === \"asc\") {\n      direction = \"desc\";\n    }\n    setSortConfig({ key, direction });\n  };\n\n  // Handle sorting for new table\n  const handleSortNew = (key) => {\n    let direction = \"asc\";\n    if (sortConfigNew.key === key && sortConfigNew.direction === \"asc\") {\n      direction = \"desc\";\n    }\n    setSortConfigNew({ key, direction });\n  };\n\n  // Sort cards based on current sort config for main table\n  const getSortedCards = (cards) => {\n    if (!sortConfig.key) return cards;\n\n    return [...cards].sort((a, b) => {\n      const aValue = a[sortConfig.key].toLowerCase();\n      const bValue = b[sortConfig.key].toLowerCase();\n\n      if (sortConfig.direction === \"asc\") {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n  };\n\n  // Sort cards based on current sort config for new table\n  const getSortedCardsNew = (cards) => {\n    if (!sortConfigNew.key) return cards;\n\n    return [...cards].sort((a, b) => {\n      const aValue = a[sortConfigNew.key].toLowerCase();\n      const bValue = b[sortConfigNew.key].toLowerCase();\n\n      if (sortConfigNew.direction === \"asc\") {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n  };\n\n  // Handle shareable toggle for main table - only author can select\n  const toggleShareable = (id) => {\n    const card = passwordCards.find(c => c.id === id);\n    if (card && card.authorId !== currentUserId) {\n      alert(\"Only the author can select this row.\");\n      return;\n    }\n    setShareableCards((prev) =>\n      prev.includes(id) ? prev.filter((cardId) => cardId !== id) : [...prev, id]\n    );\n  };\n\n  // Handle shareable toggle for new table - only author can select\n  const toggleShareableNew = (id) => {\n    const card = newPasswordCards.find(c => c.id === id);\n    if (card && card.authorId !== currentUserId) {\n      alert(\"Only the author can select this row.\");\n      return;\n    }\n    setShareableNewCards((prev) =>\n      prev.includes(id) ? prev.filter((cardId) => cardId !== id) : [...prev, id]\n    );\n  };\n\n  // Handle form submission for main table password cards\n  const handleAddPasswordCard = (cardData) => {\n    const newCard = {\n      ...cardData,\n      id: Date.now(),\n      password: \"••••••••••••\",\n      actualPassword: cardData.password,\n      authorId: currentUserId, // Add current user as author\n    };\n    setPasswordCards((prev) => [...prev, newCard]);\n    setShowAddForm(false);\n  };\n\n  // Handle form submission for new table password cards\n  const handleAddPasswordCardNew = (cardData) => {\n    const newCard = {\n      ...cardData,\n      id: Date.now(),\n      password: \"••••••••••••\",\n      actualPassword: cardData.password,\n      authorId: currentUserId, // Add current user as author\n    };\n    setNewPasswordCards((prev) => [...prev, newCard]);\n    setShowAddFormNew(false);\n  };\n\n  const handleDelete = (id) => {\n    confirmationAlert({\n      onConfirm: () => {\n        setPasswordCards((prev) => prev.filter((card) => card.id !== id));\n      },\n    });\n  };\n\n  // Handle share functionality for department - only author can share\n  const handleShare = () => {\n    // Check if current user is author of any cards\n    const isAuthor = passwordCards.some(card => card.authorId === currentUserId);\n\n    if (!isAuthor) {\n      alert(\"Only the author can share the table.\");\n      return;\n    }\n\n    // Get current user's department or team\n    const currentUserDepartment = userData?.departments?.[0]?.name || \"IT\";\n\n    // Filter cards that belong to the same department and are authored by current user\n    const departmentCards = passwordCards.filter(\n      (card) => card.department === currentUserDepartment && card.authorId === currentUserId\n    );\n\n    if (departmentCards.length === 0) {\n      alert(\"No password cards available to share in your department.\");\n      return;\n    }\n\n    // Create shareable data\n    const shareData = {\n      title: `${currentUserDepartment} Department Password Cards`,\n      cards: departmentCards,\n      sharedBy: `${userData?.fname} ${userData?.lname}` || \"Current User\",\n      sharedAt: new Date().toISOString(),\n    };\n\n    // For now, copy to clipboard (you can implement actual sharing logic)\n    navigator.clipboard\n      .writeText(JSON.stringify(shareData, null, 2))\n      .then(() => {\n        alert(\n          `${departmentCards.length} password cards from ${currentUserDepartment} department copied to clipboard!`\n        );\n      })\n      .catch(() => {\n        alert(\"Failed to copy to clipboard. Please try again.\");\n      });\n  };\n\n  // Placeholder avatar images\n  const avatarImages = [\n    \"https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=A\",\n    \"https://via.placeholder.com/32x32/10B981/FFFFFF?text=B\",\n    \"https://via.placeholder.com/32x32/F59E0B/FFFFFF?text=C\",\n    \"https://via.placeholder.com/32x32/EF4444/FFFFFF?text=D\",\n  ];\n\n  return (\n    <div className=\"bg-white dark:bg-gray-900\">\n      {showTeamTable && (\n        <>\n          {/* Header */}\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\">\n            <div className=\"flex items-center space-x-4\">\n              <h2 className=\"text-left text-2xl font-bold text-gray-900 dark:text-white\">\n                Teams Password Card\n              </h2>\n\n              {/* Share Icon */}\n              <button\n                onClick={handleShare}\n                className=\"flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\"\n              >\n                <span className=\"material-symbols-rounded\">share</span>\n              </button>\n\n              {/* Delete Team Table Icon */}\n              <button\n                onClick={() => handleDeleteTeamTable()}\n                className=\"flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg\"\n                title=\"Delete entire table\"\n              >\n                {/* <span className=\"material-symbols-rounded\">delete</span> */}\n                <span className=\"material-symbols-outlined text-sm\">\n                  delete\n                </span>\n              </button>\n\n              {/* User Avatars */}\n              <div className=\"flex -space-x-2\">\n                {avatarImages.map((avatar, index) => (\n                  <img\n                    key={index}\n                    src={avatar}\n                    alt={`User ${index + 1}`}\n                    className=\"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"\n                  />\n                ))}\n              </div>\n            </div>\n\n            <div className=\"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0\">\n              {/* Add Password Card Button */}\n              <button\n                onClick={() => {\n                  setEditingRowId(null);\n                  setShowAddForm(!showAddForm);\n                }}\n                className={`w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${\n                  showAddForm\n                    ? \"bg-primary text-white\"\n                    : \"bg-transparent text-primary border-2 border-primary\"\n                }`}\n              >\n                <span className=\"material-symbols-rounded mr-2\">add</span>\n                Add Password\n              </button>\n            </div>\n          </div>\n\n          {/* Add/Edit Password Form - Embedded */}\n          {showAddForm && (\n            <AddPasswordCardForm\n              onSubmit={handleAddPasswordCard}\n              onCancel={() => {\n                setShowAddForm(false);\n                setEditingRowId(null);\n              }}\n              generatedPassword={generatedPassword}\n              passwordStrength={passwordStrength}\n            />\n          )}\n\n          {/* Table */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50 border-b border-gray-200\">\n                <tr>\n                  <th className=\"w-12 px-4 py-3 text-left\">\n                    <input\n                      id=\"checkbox-all\"\n                      type=\"checkbox\"\n                      checked={\n                        shareableCards.length === passwordCards.length &&\n                        passwordCards.length > 0\n                      }\n                      onChange={toggleSelectAll}\n                      className=\"w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-2\"\n                    />\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"title\")}\n                    >\n                      <span>Title</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-primary\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-primary\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"username\")}\n                    >\n                      <span>User Name</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-primary\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-primary\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Password\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Team\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Department\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Level\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Action\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"divide-y divide-gray-200\">\n                {getSortedCards(passwordCards).map((card) => (\n                  <tr key={card.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-4 py-4\">\n                      <input\n                        id={`shareable-${card.id}`}\n                        type=\"checkbox\"\n                        checked={shareableCards.includes(card.id)}\n                        onChange={() => toggleShareable(card.id)}\n                        disabled={card.authorId !== currentUserId}\n                        className={`w-4 h-4 bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-2 ${\n                          card.authorId === currentUserId\n                            ? \"text-primary cursor-pointer\"\n                            : \"text-gray-400 cursor-not-allowed opacity-50\"\n                        }`}\n                      />\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center\">\n                        <div className=\"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\">\n                          TG\n                        </div>\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.title}\n                            className=\"font-medium text-gray-900 border rounded px-2 py-1 w-full\"\n                            onBlur={(e) => {\n                              setPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, title: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                            autoFocus\n                          />\n                        ) : (\n                          <span className=\"font-medium text-gray-900\">\n                            {card.title}\n                          </span>\n                        )}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center\">\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.username}\n                            className=\"text-gray-900 border rounded px-2 py-1 w-full mr-2\"\n                            onBlur={(e) => {\n                              setPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, username: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                          />\n                        ) : (\n                          <span className=\"text-gray-900\">{card.username}</span>\n                        )}\n                        <button\n                          onClick={() => copyToClipboard(card.username)}\n                          className=\"ml-2 text-gray-400 hover:text-gray-600\"\n                        >\n                          <span className=\"material-symbols-rounded text-sm\">\n                            content_copy\n                          </span>\n                        </button>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center\">\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.actualPassword}\n                            className=\"text-gray-900 border rounded px-2 py-1 w-full mr-2\"\n                            onBlur={(e) => {\n                              setPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, actualPassword: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                          />\n                        ) : (\n                          <span className=\"text-gray-900 mr-2\">\n                            {visiblePasswords[card.id]\n                              ? card.actualPassword\n                              : card.password}\n                          </span>\n                        )}\n                        <button\n                          onClick={() => togglePasswordVisibility(card.id)}\n                          className=\"text-gray-400 hover:text-gray-600 mr-2\"\n                        >\n                          <span className=\"material-symbols-rounded text-sm\">\n                            {visiblePasswords[card.id]\n                              ? \"visibility_off\"\n                              : \"visibility\"}\n                          </span>\n                        </button>\n                        <button\n                          onClick={() => copyToClipboard(card.actualPassword)}\n                          className=\"text-gray-400 hover:text-gray-600\"\n                        >\n                          <span className=\"material-symbols-rounded text-sm\">\n                            content_copy\n                          </span>\n                        </button>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      {editingRowId === card.id ? (\n                        <input\n                          type=\"text\"\n                          defaultValue={card.team}\n                          className=\"text-gray-900 border rounded px-2 py-1 w-full\"\n                          onBlur={(e) => {\n                            setPasswordCards((prev) =>\n                              prev.map((c) =>\n                                c.id === card.id\n                                  ? { ...c, team: e.target.value }\n                                  : c\n                              )\n                            );\n                          }}\n                          onKeyDown={(e) => {\n                            if (e.key === \"Enter\") {\n                              e.target.blur();\n                            }\n                          }}\n                        />\n                      ) : (\n                        <span className=\"text-gray-900\">{card.team}</span>\n                      )}\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      {editingRowId === card.id ? (\n                        <input\n                          type=\"text\"\n                          defaultValue={card.department}\n                          className=\"text-gray-900 border rounded px-2 py-1 w-full\"\n                          onBlur={(e) => {\n                            setPasswordCards((prev) =>\n                              prev.map((c) =>\n                                c.id === card.id\n                                  ? { ...c, department: e.target.value }\n                                  : c\n                              )\n                            );\n                          }}\n                          onKeyDown={(e) => {\n                            if (e.key === \"Enter\") {\n                              e.target.blur();\n                            }\n                          }}\n                        />\n                      ) : (\n                        <span className=\"text-gray-900\">{card.department}</span>\n                      )}\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <span\n                        className={`px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`}\n                      >\n                        {card.strength}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center space-x-1\">\n                        <button\n                          onClick={() => handleEdit(card.id)}\n                          className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\n                          title=\"Edit\"\n                        >\n                          <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\n                        </button>\n                        <button\n                          onClick={() => handleDelete(card.id)}\n                          className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\n                          title=\"Delete\"\n                        >\n                          <span className=\"material-symbols-outlined text-sm\">delete</span>\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </>\n      )}\n\n      {/* New Password Cards Table */}\n      {showNewTable && (\n        <div className=\"mt-8\">\n          {/* Header */}\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\">\n            <div className=\"flex items-center space-x-4\">\n              <h2 className=\"text-left text-2xl font-bold text-gray-900 dark:text-white\">\n                Teams Password Card\n              </h2>\n\n              {/* Share Icon */}\n              <button\n                onClick={handleShare}\n                className=\"flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\"\n              >\n                <span className=\"material-symbols-rounded\">share</span>\n              </button>\n\n              {/* Delete Team Table Icon */}\n              <button\n                onClick={() => handleDeleteTeamTable()}\n                className=\"flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg\"\n                title=\"Delete entire table\"\n              >\n                <span className=\"material-symbols-outlined text-sm\">\n                  delete\n                </span>\n              </button>\n\n              {/* User Avatars */}\n              <div className=\"flex -space-x-2\">\n                {avatarImages.map((avatar, index) => (\n                  <img\n                    key={index}\n                    src={avatar}\n                    alt={`User ${index + 1}`}\n                    className=\"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"\n                  />\n                ))}\n              </div>\n            </div>\n\n            <div className=\"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-start md:space-x-3 flex-shrink-0\">\n              {/* Add Password Card Button */}\n              <button\n                onClick={() => {\n                  setEditingRowId(null);\n                  setShowAddForm(!showAddForm);\n                }}\n                className={`w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${\n                  showAddForm\n                    ? \"bg-primary text-white\"\n                    : \"bg-transparent text-primary border-2 border-primary\"\n                }`}\n              >\n                <span className=\"material-symbols-rounded mr-2\">add</span>\n                Add Password\n              </button>\n            </div>\n          </div>\n\n          {/* Add/Edit Password Form - Embedded */}\n          {showAddForm && (\n            <AddPasswordCardForm\n              onSubmit={handleAddPasswordCard}\n              onCancel={() => {\n                setShowAddForm(false);\n                setEditingRowId(null);\n              }}\n              generatedPassword={generatedPassword}\n              passwordStrength={passwordStrength}\n            />\n          )}\n\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50 border-b border-gray-200\">\n                <tr>\n                  <th className=\"w-12 px-4 py-3 text-left\">\n                    <input\n                      id=\"checkbox-all-new\"\n                      type=\"checkbox\"\n                      checked={\n                        shareableCards.length === newPasswordCards.length &&\n                        newPasswordCards.length > 0\n                      }\n                      onChange={() => {\n                        if (shareableCards.length === newPasswordCards.length) {\n                          setShareableCards([]);\n                        } else {\n                          setShareableCards(newPasswordCards.map((card) => card.id));\n                        }\n                      }}\n                      className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                    />\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"title\")}\n                    >\n                      <span>Title</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"title\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    <div\n                      className=\"flex items-center space-x-2 cursor-pointer\"\n                      onClick={() => handleSort(\"username\")}\n                    >\n                      <span>User Name</span>\n                      <div className=\"flex flex-col\">\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"asc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▲\n                        </span>\n                        <span\n                          className={`text-xs ${\n                            sortConfig.key === \"username\" &&\n                            sortConfig.direction === \"desc\"\n                              ? \"text-blue-600\"\n                              : \"text-gray-400\"\n                          }`}\n                        >\n                          ▼\n                        </span>\n                      </div>\n                    </div>\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Password\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Team\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Department\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Level\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-sm font-medium text-gray-700\">\n                    Action\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"divide-y divide-gray-200\">\n                {newPasswordCards.length === 0 ? (\n                  <tr>\n                    <td\n                      colSpan=\"8\"\n                      className=\"px-6 py-8 text-center text-gray-500\"\n                    >\n                      No password cards added yet. Click \"Add Password\" to add\n                      your first card.\n                    </td>\n                  </tr>\n                ) : (\n                  getSortedCards(newPasswordCards).map((card) => (\n                    <tr key={card.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-4 py-4\">\n                        <input\n                          type=\"checkbox\"\n                          checked={shareableCards.includes(card.id)}\n                          onChange={() => toggleShareable(card.id)}\n                          className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                        />\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          <div className=\"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3\">\n                            TG\n                          </div>\n                          {editingRowId === card.id ? (\n                            <input\n                              type=\"text\"\n                              defaultValue={card.title}\n                              className=\"font-medium text-gray-900 border rounded px-2 py-1 w-full\"\n                              onBlur={(e) => {\n                                setNewPasswordCards((prev) =>\n                                  prev.map((c) =>\n                                    c.id === card.id\n                                      ? { ...c, title: e.target.value }\n                                      : c\n                                  )\n                                );\n                              }}\n                              onKeyDown={(e) => {\n                                if (e.key === \"Enter\") {\n                                  e.target.blur();\n                                }\n                              }}\n                              autoFocus\n                            />\n                          ) : (\n                            <span className=\"font-medium text-gray-900\">\n                              {card.title}\n                            </span>\n                          )}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          {editingRowId === card.id ? (\n                            <input\n                              type=\"text\"\n                              defaultValue={card.username}\n                              className=\"text-gray-900 border rounded px-2 py-1 w-full mr-2\"\n                              onBlur={(e) => {\n                                setNewPasswordCards((prev) =>\n                                  prev.map((c) =>\n                                    c.id === card.id\n                                      ? { ...c, username: e.target.value }\n                                      : c\n                                  )\n                                );\n                              }}\n                              onKeyDown={(e) => {\n                                if (e.key === \"Enter\") {\n                                  e.target.blur();\n                                }\n                              }}\n                            />\n                          ) : (\n                            <span className=\"text-gray-900\">{card.username}</span>\n                          )}\n                          <button\n                            onClick={() => copyToClipboard(card.username)}\n                            className=\"ml-2 text-gray-400 hover:text-gray-600\"\n                          >\n                            <span className=\"material-symbols-rounded text-sm\">\n                              content_copy\n                            </span>\n                          </button>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center\">\n                          {editingRowId === card.id ? (\n                            <input\n                              type=\"text\"\n                              defaultValue={card.actualPassword}\n                              className=\"text-gray-900 border rounded px-2 py-1 w-full mr-2\"\n                              onBlur={(e) => {\n                                setNewPasswordCards((prev) =>\n                                  prev.map((c) =>\n                                    c.id === card.id\n                                      ? { ...c, actualPassword: e.target.value }\n                                      : c\n                                  )\n                                );\n                              }}\n                              onKeyDown={(e) => {\n                                if (e.key === \"Enter\") {\n                                  e.target.blur();\n                                }\n                              }}\n                            />\n                          ) : (\n                            <span className=\"text-gray-900 mr-2\">\n                              {visiblePasswords[card.id]\n                                ? card.actualPassword\n                                : card.password}\n                            </span>\n                          )}\n                          <button\n                            onClick={() => togglePasswordVisibility(card.id)}\n                            className=\"text-gray-400 hover:text-gray-600 mr-2\"\n                          >\n                            <span className=\"material-symbols-rounded text-sm\">\n                              {visiblePasswords[card.id]\n                                ? \"visibility_off\"\n                                : \"visibility\"}\n                            </span>\n                          </button>\n                          <button\n                            onClick={() => copyToClipboard(card.actualPassword)}\n                            className=\"text-gray-400 hover:text-gray-600\"\n                          >\n                            <span className=\"material-symbols-rounded text-sm\">\n                              content_copy\n                            </span>\n                          </button>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.team}\n                            className=\"text-gray-900 border rounded px-2 py-1 w-full\"\n                            onBlur={(e) => {\n                              setNewPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, team: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                          />\n                        ) : (\n                          <span className=\"text-gray-900\">{card.team}</span>\n                        )}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        {editingRowId === card.id ? (\n                          <input\n                            type=\"text\"\n                            defaultValue={card.department}\n                            className=\"text-gray-900 border rounded px-2 py-1 w-full\"\n                            onBlur={(e) => {\n                              setNewPasswordCards((prev) =>\n                                prev.map((c) =>\n                                  c.id === card.id\n                                    ? { ...c, department: e.target.value }\n                                    : c\n                                )\n                              );\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === \"Enter\") {\n                                e.target.blur();\n                              }\n                            }}\n                          />\n                        ) : (\n                          <span className=\"text-gray-900\">{card.department}</span>\n                        )}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <span\n                          className={`px-3 py-1 text-xs font-medium rounded-full ${card.strengthColor}`}\n                        >\n                          {card.strength}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center space-x-1\">\n                          <button\n                            onClick={() => handleEdit(card.id)}\n                            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\n                            title=\"Edit\"\n                          >\n                            <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\n                          </button>\n                          <button\n                            onClick={() => {\n                              confirmationAlert({\n                                onConfirm: () => {\n                                  setNewPasswordCards((prev) =>\n                                    prev.filter((c) => c.id !== card.id)\n                                  );\n                                },\n                              });\n                            }}\n                            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\n                            title=\"Delete\"\n                          >\n                            <span className=\"material-symbols-outlined text-sm\">delete</span>\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))\n                )}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      )}\n\n      {/* Add New Password Card Button (Bottom) */}\n      <div className=\"flex justify-center mt-6\">\n        <button\n          onClick={handleCreateNewTable}\n          className=\"flex items-center justify-center px-6 py-2 text-sm font-medium text-white rounded-lg bg-primary hover:bg-primary/90 focus:ring-4 focus:ring-primary/30 transition-colors duration-200 focus:outline-none\"\n        >\n          <span className=\"material-symbols-rounded mr-2\">add</span>\n          Add New Password Card\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default PasswordCardsTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,OAAOC,iBAAiB,MAAM,0CAA0C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzE,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,iBAAiB;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EACtE;EACA,MAAM;IAAEC;EAAS,CAAC,GAAGT,iBAAiB,CAAC,CAAC;EACxC,MAAMU,aAAa,GAAGD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,EAAE;;EAElC;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,CACjD;IACEc,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,6CAA6C;IAC7DC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE,wCAAwC;IACvDC,QAAQ,EAAEb,aAAa,CAAE;EAC3B,CAAC,EACD;IACEC,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,iBAAiB;IACxBC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,kBAAkB;IAClCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,iBAAiB;IAC3BC,aAAa,EAAE,8CAA8C;IAC7DC,QAAQ,EAAEb;EACZ,CAAC,EACD;IACEC,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,mBAAmB;IAC1BC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,iBAAiB;IACjCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,aAAa;IACzBC,QAAQ,EAAE,mBAAmB;IAC7BC,aAAa,EAAE,iDAAiD;IAChEC,QAAQ,EAAE,GAAG,CAAE;EACjB,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,aAAa;IACpBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,UAAU;IAC1BC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE,wCAAwC;IACvDC,QAAQ,EAAEb;EACZ,CAAC,EACD;IACEC,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,uBAAuB;IACvCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,oBAAoB;IAChCC,QAAQ,EAAE,iBAAiB;IAC3BC,aAAa,EAAE,8CAA8C;IAC7DC,QAAQ,EAAE,GAAG,CAAE;EACjB,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,eAAe;IACzBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,mBAAmB;IACnCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,mBAAmB;IAC7BC,aAAa,EAAE,iDAAiD;IAChEC,QAAQ,EAAEb;EACZ,CAAC,EACD;IACEC,EAAE,EAAE,CAAC;IACLG,KAAK,EAAE,gBAAgB;IACvBC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,cAAc;IAC9BC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE,wCAAwC;IACvDC,QAAQ,EAAEb;EACZ,CAAC,CACF,CAAC;EAEF,MAAM,CAACc,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACoC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC;IAAE8C,GAAG,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAM,CAAC,CAAC;EAC7E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjD,QAAQ,CAAC;IAAE8C,GAAG,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAM,CAAC,CAAC;EACnF,MAAMG,wBAAwB,GAAIpC,EAAE,IAAK;IACvCc,mBAAmB,CAAEuB,IAAI,KAAM;MAC7B,GAAGA,IAAI;MACP,CAACrC,EAAE,GAAG,CAACqC,IAAI,CAACrC,EAAE;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMsC,eAAe,GAAIC,IAAI,IAAK;IAChCC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMI,UAAU,GAAI3C,EAAE,IAAK;IACzByB,eAAe,CAACD,YAAY,KAAKxB,EAAE,GAAG,IAAI,GAAGA,EAAE,CAAC;EAClD,CAAC;;EAED;EACA,MAAM4C,oBAAoB,GAAGA,CAAA,KAAM;IACjCxB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMyB,qBAAqB,GAAGA,CAAA,KAAM;IAClC;IACA,MAAMC,QAAQ,GAAG7C,aAAa,CAAC8C,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACpC,QAAQ,KAAKb,aAAa,CAAC;IAE5E,IAAI,CAAC+C,QAAQ,EAAE;MACbG,KAAK,CAAC,8CAA8C,CAAC;MACrD;IACF;;IAEA;IACA,IAAIvB,cAAc,CAACwB,MAAM,KAAK,CAAC,EAAE;MAC/BD,KAAK,CAAC,qDAAqD,CAAC;MAC5D;IACF;IAEA7D,iBAAiB,CAAC;MAChB+D,SAAS,EAAEA,CAAA,KAAM;QACfjD,gBAAgB,CAAC,EAAE,CAAC;MACtB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMkD,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,WAAW,GAAGpD,aAAa,CAACqD,MAAM,CAACN,IAAI,IAAIA,IAAI,CAACpC,QAAQ,KAAKb,aAAa,CAAC;IACjF,IAAI2B,cAAc,CAACwB,MAAM,KAAKG,WAAW,CAACH,MAAM,EAAE;MAChDvB,iBAAiB,CAAC,EAAE,CAAC;IACvB,CAAC,MAAM;MACLA,iBAAiB,CAAC0B,WAAW,CAACE,GAAG,CAAEP,IAAI,IAAKA,IAAI,CAAChD,EAAE,CAAC,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMwD,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMH,WAAW,GAAG/B,gBAAgB,CAACgC,MAAM,CAACN,IAAI,IAAIA,IAAI,CAACpC,QAAQ,KAAKb,aAAa,CAAC;IACpF,IAAI6B,iBAAiB,CAACsB,MAAM,KAAKG,WAAW,CAACH,MAAM,EAAE;MACnDrB,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,MAAM;MACLA,oBAAoB,CAACwB,WAAW,CAACE,GAAG,CAAEP,IAAI,IAAKA,IAAI,CAAChD,EAAE,CAAC,CAAC;IAC1D;EACF,CAAC;;EAED;EACA,MAAMyD,UAAU,GAAIzB,GAAG,IAAK;IAC1B,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIH,UAAU,CAACE,GAAG,KAAKA,GAAG,IAAIF,UAAU,CAACG,SAAS,KAAK,KAAK,EAAE;MAC5DA,SAAS,GAAG,MAAM;IACpB;IACAF,aAAa,CAAC;MAAEC,GAAG;MAAEC;IAAU,CAAC,CAAC;EACnC,CAAC;;EAED;EACA,MAAMyB,aAAa,GAAI1B,GAAG,IAAK;IAC7B,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,aAAa,CAACF,GAAG,KAAKA,GAAG,IAAIE,aAAa,CAACD,SAAS,KAAK,KAAK,EAAE;MAClEA,SAAS,GAAG,MAAM;IACpB;IACAE,gBAAgB,CAAC;MAAEH,GAAG;MAAEC;IAAU,CAAC,CAAC;EACtC,CAAC;;EAED;EACA,MAAM0B,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAI,CAAC9B,UAAU,CAACE,GAAG,EAAE,OAAO4B,KAAK;IAEjC,OAAO,CAAC,GAAGA,KAAK,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC/B,MAAMC,MAAM,GAAGF,CAAC,CAAChC,UAAU,CAACE,GAAG,CAAC,CAACiC,WAAW,CAAC,CAAC;MAC9C,MAAMC,MAAM,GAAGH,CAAC,CAACjC,UAAU,CAACE,GAAG,CAAC,CAACiC,WAAW,CAAC,CAAC;MAE9C,IAAInC,UAAU,CAACG,SAAS,KAAK,KAAK,EAAE;QAClC,OAAO+B,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MACvD,CAAC,MAAM;QACL,OAAOF,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MACvD;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAIP,KAAK,IAAK;IACnC,IAAI,CAAC1B,aAAa,CAACF,GAAG,EAAE,OAAO4B,KAAK;IAEpC,OAAO,CAAC,GAAGA,KAAK,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC/B,MAAMC,MAAM,GAAGF,CAAC,CAAC5B,aAAa,CAACF,GAAG,CAAC,CAACiC,WAAW,CAAC,CAAC;MACjD,MAAMC,MAAM,GAAGH,CAAC,CAAC7B,aAAa,CAACF,GAAG,CAAC,CAACiC,WAAW,CAAC,CAAC;MAEjD,IAAI/B,aAAa,CAACD,SAAS,KAAK,KAAK,EAAE;QACrC,OAAO+B,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MACvD,CAAC,MAAM;QACL,OAAOF,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MACvD;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,eAAe,GAAIpE,EAAE,IAAK;IAC9B,MAAMgD,IAAI,GAAG/C,aAAa,CAACoE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtE,EAAE,KAAKA,EAAE,CAAC;IACjD,IAAIgD,IAAI,IAAIA,IAAI,CAACpC,QAAQ,KAAKb,aAAa,EAAE;MAC3CkD,KAAK,CAAC,sCAAsC,CAAC;MAC7C;IACF;IACAtB,iBAAiB,CAAEU,IAAI,IACrBA,IAAI,CAACkC,QAAQ,CAACvE,EAAE,CAAC,GAAGqC,IAAI,CAACiB,MAAM,CAAEkB,MAAM,IAAKA,MAAM,KAAKxE,EAAE,CAAC,GAAG,CAAC,GAAGqC,IAAI,EAAErC,EAAE,CAC3E,CAAC;EACH,CAAC;;EAED;EACA,MAAMyE,kBAAkB,GAAIzE,EAAE,IAAK;IACjC,MAAMgD,IAAI,GAAG1B,gBAAgB,CAAC+C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtE,EAAE,KAAKA,EAAE,CAAC;IACpD,IAAIgD,IAAI,IAAIA,IAAI,CAACpC,QAAQ,KAAKb,aAAa,EAAE;MAC3CkD,KAAK,CAAC,sCAAsC,CAAC;MAC7C;IACF;IACApB,oBAAoB,CAAEQ,IAAI,IACxBA,IAAI,CAACkC,QAAQ,CAACvE,EAAE,CAAC,GAAGqC,IAAI,CAACiB,MAAM,CAAEkB,MAAM,IAAKA,MAAM,KAAKxE,EAAE,CAAC,GAAG,CAAC,GAAGqC,IAAI,EAAErC,EAAE,CAC3E,CAAC;EACH,CAAC;;EAED;EACA,MAAM0E,qBAAqB,GAAIC,QAAQ,IAAK;IAC1C,MAAMC,OAAO,GAAG;MACd,GAAGD,QAAQ;MACX3E,EAAE,EAAE6E,IAAI,CAACC,GAAG,CAAC,CAAC;MACdxE,QAAQ,EAAE,cAAc;MACxBC,cAAc,EAAEoE,QAAQ,CAACrE,QAAQ;MACjCM,QAAQ,EAAEb,aAAa,CAAE;IAC3B,CAAC;IACDG,gBAAgB,CAAEmC,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEuC,OAAO,CAAC,CAAC;IAC9C5D,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAM+D,wBAAwB,GAAIJ,QAAQ,IAAK;IAC7C,MAAMC,OAAO,GAAG;MACd,GAAGD,QAAQ;MACX3E,EAAE,EAAE6E,IAAI,CAACC,GAAG,CAAC,CAAC;MACdxE,QAAQ,EAAE,cAAc;MACxBC,cAAc,EAAEoE,QAAQ,CAACrE,QAAQ;MACjCM,QAAQ,EAAEb,aAAa,CAAE;IAC3B,CAAC;IACDwB,mBAAmB,CAAEc,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEuC,OAAO,CAAC,CAAC;IACjD1D,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,MAAM8D,YAAY,GAAIhF,EAAE,IAAK;IAC3BZ,iBAAiB,CAAC;MAChB+D,SAAS,EAAEA,CAAA,KAAM;QACfjD,gBAAgB,CAAEmC,IAAI,IAAKA,IAAI,CAACiB,MAAM,CAAEN,IAAI,IAAKA,IAAI,CAAChD,EAAE,KAAKA,EAAE,CAAC,CAAC;MACnE;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMiF,WAAW,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACxB;IACA,MAAMrC,QAAQ,GAAG7C,aAAa,CAAC8C,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACpC,QAAQ,KAAKb,aAAa,CAAC;IAE5E,IAAI,CAAC+C,QAAQ,EAAE;MACbG,KAAK,CAAC,sCAAsC,CAAC;MAC7C;IACF;;IAEA;IACA,MAAMmC,qBAAqB,GAAG,CAAAtF,QAAQ,aAARA,QAAQ,wBAAAoF,qBAAA,GAARpF,QAAQ,CAAEuF,WAAW,cAAAH,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAwB,CAAC,CAAC,cAAAC,sBAAA,uBAA1BA,sBAAA,CAA4BG,IAAI,KAAI,IAAI;;IAEtE;IACA,MAAMC,eAAe,GAAGtF,aAAa,CAACqD,MAAM,CACzCN,IAAI,IAAKA,IAAI,CAACvC,UAAU,KAAK2E,qBAAqB,IAAIpC,IAAI,CAACpC,QAAQ,KAAKb,aAC3E,CAAC;IAED,IAAIwF,eAAe,CAACrC,MAAM,KAAK,CAAC,EAAE;MAChCD,KAAK,CAAC,0DAA0D,CAAC;MACjE;IACF;;IAEA;IACA,MAAMuC,SAAS,GAAG;MAChBrF,KAAK,EAAE,GAAGiF,qBAAqB,4BAA4B;MAC3DxB,KAAK,EAAE2B,eAAe;MACtBE,QAAQ,EAAE,GAAG3F,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4F,KAAK,IAAI5F,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6F,KAAK,EAAE,IAAI,cAAc;MACnEC,QAAQ,EAAE,IAAIf,IAAI,CAAC,CAAC,CAACgB,WAAW,CAAC;IACnC,CAAC;;IAED;IACArD,SAAS,CAACC,SAAS,CAChBC,SAAS,CAACoD,IAAI,CAACC,SAAS,CAACP,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAC7CQ,IAAI,CAAC,MAAM;MACV/C,KAAK,CACH,GAAGsC,eAAe,CAACrC,MAAM,wBAAwBkC,qBAAqB,kCACxE,CAAC;IACH,CAAC,CAAC,CACDa,KAAK,CAAC,MAAM;MACXhD,KAAK,CAAC,gDAAgD,CAAC;IACzD,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMiD,YAAY,GAAG,CACnB,wDAAwD,EACxD,wDAAwD,EACxD,wDAAwD,EACxD,wDAAwD,CACzD;EAED,oBACE3G,OAAA;IAAK4G,SAAS,EAAC,2BAA2B;IAAAC,QAAA,GACvC/E,aAAa,iBACZ9B,OAAA,CAAAE,SAAA;MAAA2G,QAAA,gBAEE7G,OAAA;QAAK4G,SAAS,EAAC,iGAAiG;QAAAC,QAAA,gBAC9G7G,OAAA;UAAK4G,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C7G,OAAA;YAAI4G,SAAS,EAAC,4DAA4D;YAAAC,QAAA,EAAC;UAE3E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGLjH,OAAA;YACEkH,OAAO,EAAExB,WAAY;YACrBkB,SAAS,EAAC,+KAA+K;YAAAC,QAAA,eAEzL7G,OAAA;cAAM4G,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAGTjH,OAAA;YACEkH,OAAO,EAAEA,CAAA,KAAM5D,qBAAqB,CAAC,CAAE;YACvCsD,SAAS,EAAC,qGAAqG;YAC/GhG,KAAK,EAAC,qBAAqB;YAAAiG,QAAA,eAG3B7G,OAAA;cAAM4G,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGTjH,OAAA;YAAK4G,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BF,YAAY,CAAC3C,GAAG,CAAC,CAACmD,MAAM,EAAEC,KAAK,kBAC9BpH,OAAA;cAEEqH,GAAG,EAAEF,MAAO;cACZG,GAAG,EAAE,QAAQF,KAAK,GAAG,CAAC,EAAG;cACzBR,SAAS,EAAC;YAAiE,GAHtEQ,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjH,OAAA;UAAK4G,SAAS,EAAC,0IAA0I;UAAAC,QAAA,eAEvJ7G,OAAA;YACEkH,OAAO,EAAEA,CAAA,KAAM;cACbhF,eAAe,CAAC,IAAI,CAAC;cACrBT,cAAc,CAAC,CAACD,WAAW,CAAC;YAC9B,CAAE;YACFoF,SAAS,EAAE,ucACTpF,WAAW,GACP,uBAAuB,GACvB,qDAAqD,EACxD;YAAAqF,QAAA,gBAEH7G,OAAA;cAAM4G,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAE5D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLzF,WAAW,iBACVxB,OAAA,CAACJ,mBAAmB;QAClB2H,QAAQ,EAAEpC,qBAAsB;QAChCqC,QAAQ,EAAEA,CAAA,KAAM;UACd/F,cAAc,CAAC,KAAK,CAAC;UACrBS,eAAe,CAAC,IAAI,CAAC;QACvB,CAAE;QACF9B,iBAAiB,EAAEA,iBAAkB;QACrCC,gBAAgB,EAAEA;MAAiB;QAAAyG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACF,eAGDjH,OAAA;QAAK4G,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnE7G,OAAA;UAAO4G,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACvB7G,OAAA;YAAO4G,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eACpD7G,OAAA;cAAA6G,QAAA,gBACE7G,OAAA;gBAAI4G,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,eACtC7G,OAAA;kBACES,EAAE,EAAC,cAAc;kBACjBgH,IAAI,EAAC,UAAU;kBACfC,OAAO,EACLvF,cAAc,CAACwB,MAAM,KAAKjD,aAAa,CAACiD,MAAM,IAC9CjD,aAAa,CAACiD,MAAM,GAAG,CACxB;kBACDgE,QAAQ,EAAE9D,eAAgB;kBAC1B+C,SAAS,EAAC;gBAA0F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,eACnE7G,OAAA;kBACE4G,SAAS,EAAC,4CAA4C;kBACtDM,OAAO,EAAEA,CAAA,KAAMhD,UAAU,CAAC,OAAO,CAAE;kBAAA2C,QAAA,gBAEnC7G,OAAA;oBAAA6G,QAAA,EAAM;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClBjH,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B7G,OAAA;sBACE4G,SAAS,EAAE,WACTrE,UAAU,CAACE,GAAG,KAAK,OAAO,IAC1BF,UAAU,CAACG,SAAS,KAAK,KAAK,GAC1B,cAAc,GACd,eAAe,EAClB;sBAAAmE,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPjH,OAAA;sBACE4G,SAAS,EAAE,WACTrE,UAAU,CAACE,GAAG,KAAK,OAAO,IAC1BF,UAAU,CAACG,SAAS,KAAK,MAAM,GAC3B,cAAc,GACd,eAAe,EAClB;sBAAAmE,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,eACnE7G,OAAA;kBACE4G,SAAS,EAAC,4CAA4C;kBACtDM,OAAO,EAAEA,CAAA,KAAMhD,UAAU,CAAC,UAAU,CAAE;kBAAA2C,QAAA,gBAEtC7G,OAAA;oBAAA6G,QAAA,EAAM;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtBjH,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B7G,OAAA;sBACE4G,SAAS,EAAE,WACTrE,UAAU,CAACE,GAAG,KAAK,UAAU,IAC7BF,UAAU,CAACG,SAAS,KAAK,KAAK,GAC1B,cAAc,GACd,eAAe,EAClB;sBAAAmE,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPjH,OAAA;sBACE4G,SAAS,EAAE,WACTrE,UAAU,CAACE,GAAG,KAAK,UAAU,IAC7BF,UAAU,CAACG,SAAS,KAAK,MAAM,GAC3B,cAAc,GACd,eAAe,EAClB;sBAAAmE,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRjH,OAAA;YAAO4G,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EACxCzC,cAAc,CAAC1D,aAAa,CAAC,CAACsD,GAAG,CAAEP,IAAI,iBACtCzD,OAAA;cAAkB4G,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC5C7G,OAAA;gBAAI4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB7G,OAAA;kBACES,EAAE,EAAE,aAAagD,IAAI,CAAChD,EAAE,EAAG;kBAC3BgH,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAEvF,cAAc,CAAC6C,QAAQ,CAACvB,IAAI,CAAChD,EAAE,CAAE;kBAC1CkH,QAAQ,EAAEA,CAAA,KAAM9C,eAAe,CAACpB,IAAI,CAAChD,EAAE,CAAE;kBACzCmH,QAAQ,EAAEnE,IAAI,CAACpC,QAAQ,KAAKb,aAAc;kBAC1CoG,SAAS,EAAE,+EACTnD,IAAI,CAACpC,QAAQ,KAAKb,aAAa,GAC3B,6BAA6B,GAC7B,6CAA6C;gBAChD;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB7G,OAAA;kBAAK4G,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC7G,OAAA;oBAAK4G,SAAS,EAAC,gGAAgG;oBAAAC,QAAA,EAAC;kBAEhH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,EACLhF,YAAY,KAAKwB,IAAI,CAAChD,EAAE,gBACvBT,OAAA;oBACEyH,IAAI,EAAC,MAAM;oBACXI,YAAY,EAAEpE,IAAI,CAAC7C,KAAM;oBACzBgG,SAAS,EAAC,2DAA2D;oBACrEkB,MAAM,EAAGC,CAAC,IAAK;sBACbpH,gBAAgB,CAAEmC,IAAI,IACpBA,IAAI,CAACkB,GAAG,CAAEe,CAAC,IACTA,CAAC,CAACtE,EAAE,KAAKgD,IAAI,CAAChD,EAAE,GACZ;wBAAE,GAAGsE,CAAC;wBAAEnE,KAAK,EAAEmH,CAAC,CAACC,MAAM,CAACC;sBAAM,CAAC,GAC/BlD,CACN,CACF,CAAC;oBACH,CAAE;oBACFmD,SAAS,EAAGH,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAACtF,GAAG,KAAK,OAAO,EAAE;wBACrBsF,CAAC,CAACC,MAAM,CAACG,IAAI,CAAC,CAAC;sBACjB;oBACF,CAAE;oBACFC,SAAS;kBAAA;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,gBAEFjH,OAAA;oBAAM4G,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EACxCpD,IAAI,CAAC7C;kBAAK;oBAAAkG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB7G,OAAA;kBAAK4G,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,GAC/B5E,YAAY,KAAKwB,IAAI,CAAChD,EAAE,gBACvBT,OAAA;oBACEyH,IAAI,EAAC,MAAM;oBACXI,YAAY,EAAEpE,IAAI,CAAC3C,QAAS;oBAC5B8F,SAAS,EAAC,oDAAoD;oBAC9DkB,MAAM,EAAGC,CAAC,IAAK;sBACbpH,gBAAgB,CAAEmC,IAAI,IACpBA,IAAI,CAACkB,GAAG,CAAEe,CAAC,IACTA,CAAC,CAACtE,EAAE,KAAKgD,IAAI,CAAChD,EAAE,GACZ;wBAAE,GAAGsE,CAAC;wBAAEjE,QAAQ,EAAEiH,CAAC,CAACC,MAAM,CAACC;sBAAM,CAAC,GAClClD,CACN,CACF,CAAC;oBACH,CAAE;oBACFmD,SAAS,EAAGH,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAACtF,GAAG,KAAK,OAAO,EAAE;wBACrBsF,CAAC,CAACC,MAAM,CAACG,IAAI,CAAC,CAAC;sBACjB;oBACF;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEFjH,OAAA;oBAAM4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAEpD,IAAI,CAAC3C;kBAAQ;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACtD,eACDjH,OAAA;oBACEkH,OAAO,EAAEA,CAAA,KAAMnE,eAAe,CAACU,IAAI,CAAC3C,QAAQ,CAAE;oBAC9C8F,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eAElD7G,OAAA;sBAAM4G,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB7G,OAAA;kBAAK4G,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,GAC/B5E,YAAY,KAAKwB,IAAI,CAAChD,EAAE,gBACvBT,OAAA;oBACEyH,IAAI,EAAC,MAAM;oBACXI,YAAY,EAAEpE,IAAI,CAACzC,cAAe;oBAClC4F,SAAS,EAAC,oDAAoD;oBAC9DkB,MAAM,EAAGC,CAAC,IAAK;sBACbpH,gBAAgB,CAAEmC,IAAI,IACpBA,IAAI,CAACkB,GAAG,CAAEe,CAAC,IACTA,CAAC,CAACtE,EAAE,KAAKgD,IAAI,CAAChD,EAAE,GACZ;wBAAE,GAAGsE,CAAC;wBAAE/D,cAAc,EAAE+G,CAAC,CAACC,MAAM,CAACC;sBAAM,CAAC,GACxClD,CACN,CACF,CAAC;oBACH,CAAE;oBACFmD,SAAS,EAAGH,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAACtF,GAAG,KAAK,OAAO,EAAE;wBACrBsF,CAAC,CAACC,MAAM,CAACG,IAAI,CAAC,CAAC;sBACjB;oBACF;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEFjH,OAAA;oBAAM4G,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EACjCvF,gBAAgB,CAACmC,IAAI,CAAChD,EAAE,CAAC,GACtBgD,IAAI,CAACzC,cAAc,GACnByC,IAAI,CAAC1C;kBAAQ;oBAAA+F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CACP,eACDjH,OAAA;oBACEkH,OAAO,EAAEA,CAAA,KAAMrE,wBAAwB,CAACY,IAAI,CAAChD,EAAE,CAAE;oBACjDmG,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eAElD7G,OAAA;sBAAM4G,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC/CvF,gBAAgB,CAACmC,IAAI,CAAChD,EAAE,CAAC,GACtB,gBAAgB,GAChB;oBAAY;sBAAAqG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACTjH,OAAA;oBACEkH,OAAO,EAAEA,CAAA,KAAMnE,eAAe,CAACU,IAAI,CAACzC,cAAc,CAAE;oBACpD4F,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,eAE7C7G,OAAA;sBAAM4G,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB5E,YAAY,KAAKwB,IAAI,CAAChD,EAAE,gBACvBT,OAAA;kBACEyH,IAAI,EAAC,MAAM;kBACXI,YAAY,EAAEpE,IAAI,CAACxC,IAAK;kBACxB2F,SAAS,EAAC,+CAA+C;kBACzDkB,MAAM,EAAGC,CAAC,IAAK;oBACbpH,gBAAgB,CAAEmC,IAAI,IACpBA,IAAI,CAACkB,GAAG,CAAEe,CAAC,IACTA,CAAC,CAACtE,EAAE,KAAKgD,IAAI,CAAChD,EAAE,GACZ;sBAAE,GAAGsE,CAAC;sBAAE9D,IAAI,EAAE8G,CAAC,CAACC,MAAM,CAACC;oBAAM,CAAC,GAC9BlD,CACN,CACF,CAAC;kBACH,CAAE;kBACFmD,SAAS,EAAGH,CAAC,IAAK;oBAChB,IAAIA,CAAC,CAACtF,GAAG,KAAK,OAAO,EAAE;sBACrBsF,CAAC,CAACC,MAAM,CAACG,IAAI,CAAC,CAAC;oBACjB;kBACF;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEFjH,OAAA;kBAAM4G,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEpD,IAAI,CAACxC;gBAAI;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAClD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB5E,YAAY,KAAKwB,IAAI,CAAChD,EAAE,gBACvBT,OAAA;kBACEyH,IAAI,EAAC,MAAM;kBACXI,YAAY,EAAEpE,IAAI,CAACvC,UAAW;kBAC9B0F,SAAS,EAAC,+CAA+C;kBACzDkB,MAAM,EAAGC,CAAC,IAAK;oBACbpH,gBAAgB,CAAEmC,IAAI,IACpBA,IAAI,CAACkB,GAAG,CAAEe,CAAC,IACTA,CAAC,CAACtE,EAAE,KAAKgD,IAAI,CAAChD,EAAE,GACZ;sBAAE,GAAGsE,CAAC;sBAAE7D,UAAU,EAAE6G,CAAC,CAACC,MAAM,CAACC;oBAAM,CAAC,GACpClD,CACN,CACF,CAAC;kBACH,CAAE;kBACFmD,SAAS,EAAGH,CAAC,IAAK;oBAChB,IAAIA,CAAC,CAACtF,GAAG,KAAK,OAAO,EAAE;sBACrBsF,CAAC,CAACC,MAAM,CAACG,IAAI,CAAC,CAAC;oBACjB;kBACF;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEFjH,OAAA;kBAAM4G,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEpD,IAAI,CAACvC;gBAAU;kBAAA4F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cACxD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB7G,OAAA;kBACE4G,SAAS,EAAE,8CAA8CnD,IAAI,CAACrC,aAAa,EAAG;kBAAAyF,QAAA,EAE7EpD,IAAI,CAACtC;gBAAQ;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB7G,OAAA;kBAAK4G,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C7G,OAAA;oBACEkH,OAAO,EAAEA,CAAA,KAAM9D,UAAU,CAACK,IAAI,CAAChD,EAAE,CAAE;oBACnCmG,SAAS,EAAC,mLAAmL;oBAC7LhG,KAAK,EAAC,MAAM;oBAAAiG,QAAA,eAEZ7G,OAAA;sBAAM4G,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACTjH,OAAA;oBACEkH,OAAO,EAAEA,CAAA,KAAMzB,YAAY,CAAChC,IAAI,CAAChD,EAAE,CAAE;oBACrCmG,SAAS,EAAC,mLAAmL;oBAC7LhG,KAAK,EAAC,QAAQ;oBAAAiG,QAAA,eAEd7G,OAAA;sBAAM4G,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GA9MExD,IAAI,CAAChD,EAAE;cAAAqG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+MZ,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA,eACN,CACH,EAGArF,YAAY,iBACX5B,OAAA;MAAK4G,SAAS,EAAC,MAAM;MAAAC,QAAA,gBAEnB7G,OAAA;QAAK4G,SAAS,EAAC,iGAAiG;QAAAC,QAAA,gBAC9G7G,OAAA;UAAK4G,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C7G,OAAA;YAAI4G,SAAS,EAAC,4DAA4D;YAAAC,QAAA,EAAC;UAE3E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGLjH,OAAA;YACEkH,OAAO,EAAExB,WAAY;YACrBkB,SAAS,EAAC,+KAA+K;YAAAC,QAAA,eAEzL7G,OAAA;cAAM4G,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAGTjH,OAAA;YACEkH,OAAO,EAAEA,CAAA,KAAM5D,qBAAqB,CAAC,CAAE;YACvCsD,SAAS,EAAC,qGAAqG;YAC/GhG,KAAK,EAAC,qBAAqB;YAAAiG,QAAA,eAE3B7G,OAAA;cAAM4G,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGTjH,OAAA;YAAK4G,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BF,YAAY,CAAC3C,GAAG,CAAC,CAACmD,MAAM,EAAEC,KAAK,kBAC9BpH,OAAA;cAEEqH,GAAG,EAAEF,MAAO;cACZG,GAAG,EAAE,QAAQF,KAAK,GAAG,CAAC,EAAG;cACzBR,SAAS,EAAC;YAAiE,GAHtEQ,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjH,OAAA;UAAK4G,SAAS,EAAC,0IAA0I;UAAAC,QAAA,eAEvJ7G,OAAA;YACEkH,OAAO,EAAEA,CAAA,KAAM;cACbhF,eAAe,CAAC,IAAI,CAAC;cACrBT,cAAc,CAAC,CAACD,WAAW,CAAC;YAC9B,CAAE;YACFoF,SAAS,EAAE,ucACTpF,WAAW,GACP,uBAAuB,GACvB,qDAAqD,EACxD;YAAAqF,QAAA,gBAEH7G,OAAA;cAAM4G,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAE5D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLzF,WAAW,iBACVxB,OAAA,CAACJ,mBAAmB;QAClB2H,QAAQ,EAAEpC,qBAAsB;QAChCqC,QAAQ,EAAEA,CAAA,KAAM;UACd/F,cAAc,CAAC,KAAK,CAAC;UACrBS,eAAe,CAAC,IAAI,CAAC;QACvB,CAAE;QACF9B,iBAAiB,EAAEA,iBAAkB;QACrCC,gBAAgB,EAAEA;MAAiB;QAAAyG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACF,eAEDjH,OAAA;QAAK4G,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnE7G,OAAA;UAAO4G,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACvB7G,OAAA;YAAO4G,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eACpD7G,OAAA;cAAA6G,QAAA,gBACE7G,OAAA;gBAAI4G,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,eACtC7G,OAAA;kBACES,EAAE,EAAC,kBAAkB;kBACrBgH,IAAI,EAAC,UAAU;kBACfC,OAAO,EACLvF,cAAc,CAACwB,MAAM,KAAK5B,gBAAgB,CAAC4B,MAAM,IACjD5B,gBAAgB,CAAC4B,MAAM,GAAG,CAC3B;kBACDgE,QAAQ,EAAEA,CAAA,KAAM;oBACd,IAAIxF,cAAc,CAACwB,MAAM,KAAK5B,gBAAgB,CAAC4B,MAAM,EAAE;sBACrDvB,iBAAiB,CAAC,EAAE,CAAC;oBACvB,CAAC,MAAM;sBACLA,iBAAiB,CAACL,gBAAgB,CAACiC,GAAG,CAAEP,IAAI,IAAKA,IAAI,CAAChD,EAAE,CAAC,CAAC;oBAC5D;kBACF,CAAE;kBACFmG,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,eACnE7G,OAAA;kBACE4G,SAAS,EAAC,4CAA4C;kBACtDM,OAAO,EAAEA,CAAA,KAAMhD,UAAU,CAAC,OAAO,CAAE;kBAAA2C,QAAA,gBAEnC7G,OAAA;oBAAA6G,QAAA,EAAM;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClBjH,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B7G,OAAA;sBACE4G,SAAS,EAAE,WACTrE,UAAU,CAACE,GAAG,KAAK,OAAO,IAC1BF,UAAU,CAACG,SAAS,KAAK,KAAK,GAC1B,eAAe,GACf,eAAe,EAClB;sBAAAmE,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPjH,OAAA;sBACE4G,SAAS,EAAE,WACTrE,UAAU,CAACE,GAAG,KAAK,OAAO,IAC1BF,UAAU,CAACG,SAAS,KAAK,MAAM,GAC3B,eAAe,GACf,eAAe,EAClB;sBAAAmE,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,eACnE7G,OAAA;kBACE4G,SAAS,EAAC,4CAA4C;kBACtDM,OAAO,EAAEA,CAAA,KAAMhD,UAAU,CAAC,UAAU,CAAE;kBAAA2C,QAAA,gBAEtC7G,OAAA;oBAAA6G,QAAA,EAAM;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtBjH,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B7G,OAAA;sBACE4G,SAAS,EAAE,WACTrE,UAAU,CAACE,GAAG,KAAK,UAAU,IAC7BF,UAAU,CAACG,SAAS,KAAK,KAAK,GAC1B,eAAe,GACf,eAAe,EAClB;sBAAAmE,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPjH,OAAA;sBACE4G,SAAS,EAAE,WACTrE,UAAU,CAACE,GAAG,KAAK,UAAU,IAC7BF,UAAU,CAACG,SAAS,KAAK,MAAM,GAC3B,eAAe,GACf,eAAe,EAClB;sBAAAmE,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRjH,OAAA;YAAO4G,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EACxC9E,gBAAgB,CAAC4B,MAAM,KAAK,CAAC,gBAC5B3D,OAAA;cAAA6G,QAAA,eACE7G,OAAA;gBACEqI,OAAO,EAAC,GAAG;gBACXzB,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAChD;cAGD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GAEL7C,cAAc,CAACrC,gBAAgB,CAAC,CAACiC,GAAG,CAAEP,IAAI,iBACxCzD,OAAA;cAAkB4G,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC5C7G,OAAA;gBAAI4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB7G,OAAA;kBACEyH,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAEvF,cAAc,CAAC6C,QAAQ,CAACvB,IAAI,CAAChD,EAAE,CAAE;kBAC1CkH,QAAQ,EAAEA,CAAA,KAAM9C,eAAe,CAACpB,IAAI,CAAChD,EAAE,CAAE;kBACzCmG,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB7G,OAAA;kBAAK4G,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC7G,OAAA;oBAAK4G,SAAS,EAAC,gGAAgG;oBAAAC,QAAA,EAAC;kBAEhH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,EACLhF,YAAY,KAAKwB,IAAI,CAAChD,EAAE,gBACvBT,OAAA;oBACEyH,IAAI,EAAC,MAAM;oBACXI,YAAY,EAAEpE,IAAI,CAAC7C,KAAM;oBACzBgG,SAAS,EAAC,2DAA2D;oBACrEkB,MAAM,EAAGC,CAAC,IAAK;sBACb/F,mBAAmB,CAAEc,IAAI,IACvBA,IAAI,CAACkB,GAAG,CAAEe,CAAC,IACTA,CAAC,CAACtE,EAAE,KAAKgD,IAAI,CAAChD,EAAE,GACZ;wBAAE,GAAGsE,CAAC;wBAAEnE,KAAK,EAAEmH,CAAC,CAACC,MAAM,CAACC;sBAAM,CAAC,GAC/BlD,CACN,CACF,CAAC;oBACH,CAAE;oBACFmD,SAAS,EAAGH,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAACtF,GAAG,KAAK,OAAO,EAAE;wBACrBsF,CAAC,CAACC,MAAM,CAACG,IAAI,CAAC,CAAC;sBACjB;oBACF,CAAE;oBACFC,SAAS;kBAAA;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,gBAEFjH,OAAA;oBAAM4G,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EACxCpD,IAAI,CAAC7C;kBAAK;oBAAAkG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB7G,OAAA;kBAAK4G,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,GAC/B5E,YAAY,KAAKwB,IAAI,CAAChD,EAAE,gBACvBT,OAAA;oBACEyH,IAAI,EAAC,MAAM;oBACXI,YAAY,EAAEpE,IAAI,CAAC3C,QAAS;oBAC5B8F,SAAS,EAAC,oDAAoD;oBAC9DkB,MAAM,EAAGC,CAAC,IAAK;sBACb/F,mBAAmB,CAAEc,IAAI,IACvBA,IAAI,CAACkB,GAAG,CAAEe,CAAC,IACTA,CAAC,CAACtE,EAAE,KAAKgD,IAAI,CAAChD,EAAE,GACZ;wBAAE,GAAGsE,CAAC;wBAAEjE,QAAQ,EAAEiH,CAAC,CAACC,MAAM,CAACC;sBAAM,CAAC,GAClClD,CACN,CACF,CAAC;oBACH,CAAE;oBACFmD,SAAS,EAAGH,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAACtF,GAAG,KAAK,OAAO,EAAE;wBACrBsF,CAAC,CAACC,MAAM,CAACG,IAAI,CAAC,CAAC;sBACjB;oBACF;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEFjH,OAAA;oBAAM4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAEpD,IAAI,CAAC3C;kBAAQ;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACtD,eACDjH,OAAA;oBACEkH,OAAO,EAAEA,CAAA,KAAMnE,eAAe,CAACU,IAAI,CAAC3C,QAAQ,CAAE;oBAC9C8F,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eAElD7G,OAAA;sBAAM4G,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB7G,OAAA;kBAAK4G,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,GAC/B5E,YAAY,KAAKwB,IAAI,CAAChD,EAAE,gBACvBT,OAAA;oBACEyH,IAAI,EAAC,MAAM;oBACXI,YAAY,EAAEpE,IAAI,CAACzC,cAAe;oBAClC4F,SAAS,EAAC,oDAAoD;oBAC9DkB,MAAM,EAAGC,CAAC,IAAK;sBACb/F,mBAAmB,CAAEc,IAAI,IACvBA,IAAI,CAACkB,GAAG,CAAEe,CAAC,IACTA,CAAC,CAACtE,EAAE,KAAKgD,IAAI,CAAChD,EAAE,GACZ;wBAAE,GAAGsE,CAAC;wBAAE/D,cAAc,EAAE+G,CAAC,CAACC,MAAM,CAACC;sBAAM,CAAC,GACxClD,CACN,CACF,CAAC;oBACH,CAAE;oBACFmD,SAAS,EAAGH,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAACtF,GAAG,KAAK,OAAO,EAAE;wBACrBsF,CAAC,CAACC,MAAM,CAACG,IAAI,CAAC,CAAC;sBACjB;oBACF;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEFjH,OAAA;oBAAM4G,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EACjCvF,gBAAgB,CAACmC,IAAI,CAAChD,EAAE,CAAC,GACtBgD,IAAI,CAACzC,cAAc,GACnByC,IAAI,CAAC1C;kBAAQ;oBAAA+F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CACP,eACDjH,OAAA;oBACEkH,OAAO,EAAEA,CAAA,KAAMrE,wBAAwB,CAACY,IAAI,CAAChD,EAAE,CAAE;oBACjDmG,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eAElD7G,OAAA;sBAAM4G,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC/CvF,gBAAgB,CAACmC,IAAI,CAAChD,EAAE,CAAC,GACtB,gBAAgB,GAChB;oBAAY;sBAAAqG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACTjH,OAAA;oBACEkH,OAAO,EAAEA,CAAA,KAAMnE,eAAe,CAACU,IAAI,CAACzC,cAAc,CAAE;oBACpD4F,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,eAE7C7G,OAAA;sBAAM4G,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAEnD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB5E,YAAY,KAAKwB,IAAI,CAAChD,EAAE,gBACvBT,OAAA;kBACEyH,IAAI,EAAC,MAAM;kBACXI,YAAY,EAAEpE,IAAI,CAACxC,IAAK;kBACxB2F,SAAS,EAAC,+CAA+C;kBACzDkB,MAAM,EAAGC,CAAC,IAAK;oBACb/F,mBAAmB,CAAEc,IAAI,IACvBA,IAAI,CAACkB,GAAG,CAAEe,CAAC,IACTA,CAAC,CAACtE,EAAE,KAAKgD,IAAI,CAAChD,EAAE,GACZ;sBAAE,GAAGsE,CAAC;sBAAE9D,IAAI,EAAE8G,CAAC,CAACC,MAAM,CAACC;oBAAM,CAAC,GAC9BlD,CACN,CACF,CAAC;kBACH,CAAE;kBACFmD,SAAS,EAAGH,CAAC,IAAK;oBAChB,IAAIA,CAAC,CAACtF,GAAG,KAAK,OAAO,EAAE;sBACrBsF,CAAC,CAACC,MAAM,CAACG,IAAI,CAAC,CAAC;oBACjB;kBACF;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEFjH,OAAA;kBAAM4G,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEpD,IAAI,CAACxC;gBAAI;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAClD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB5E,YAAY,KAAKwB,IAAI,CAAChD,EAAE,gBACvBT,OAAA;kBACEyH,IAAI,EAAC,MAAM;kBACXI,YAAY,EAAEpE,IAAI,CAACvC,UAAW;kBAC9B0F,SAAS,EAAC,+CAA+C;kBACzDkB,MAAM,EAAGC,CAAC,IAAK;oBACb/F,mBAAmB,CAAEc,IAAI,IACvBA,IAAI,CAACkB,GAAG,CAAEe,CAAC,IACTA,CAAC,CAACtE,EAAE,KAAKgD,IAAI,CAAChD,EAAE,GACZ;sBAAE,GAAGsE,CAAC;sBAAE7D,UAAU,EAAE6G,CAAC,CAACC,MAAM,CAACC;oBAAM,CAAC,GACpClD,CACN,CACF,CAAC;kBACH,CAAE;kBACFmD,SAAS,EAAGH,CAAC,IAAK;oBAChB,IAAIA,CAAC,CAACtF,GAAG,KAAK,OAAO,EAAE;sBACrBsF,CAAC,CAACC,MAAM,CAACG,IAAI,CAAC,CAAC;oBACjB;kBACF;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEFjH,OAAA;kBAAM4G,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEpD,IAAI,CAACvC;gBAAU;kBAAA4F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cACxD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB7G,OAAA;kBACE4G,SAAS,EAAE,8CAA8CnD,IAAI,CAACrC,aAAa,EAAG;kBAAAyF,QAAA,EAE7EpD,IAAI,CAACtC;gBAAQ;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLjH,OAAA;gBAAI4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB7G,OAAA;kBAAK4G,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C7G,OAAA;oBACEkH,OAAO,EAAEA,CAAA,KAAM9D,UAAU,CAACK,IAAI,CAAChD,EAAE,CAAE;oBACnCmG,SAAS,EAAC,mLAAmL;oBAC7LhG,KAAK,EAAC,MAAM;oBAAAiG,QAAA,eAEZ7G,OAAA;sBAAM4G,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACTjH,OAAA;oBACEkH,OAAO,EAAEA,CAAA,KAAM;sBACbrH,iBAAiB,CAAC;wBAChB+D,SAAS,EAAEA,CAAA,KAAM;0BACf5B,mBAAmB,CAAEc,IAAI,IACvBA,IAAI,CAACiB,MAAM,CAAEgB,CAAC,IAAKA,CAAC,CAACtE,EAAE,KAAKgD,IAAI,CAAChD,EAAE,CACrC,CAAC;wBACH;sBACF,CAAC,CAAC;oBACJ,CAAE;oBACFmG,SAAS,EAAC,mLAAmL;oBAC7LhG,KAAK,EAAC,QAAQ;oBAAAiG,QAAA,eAEd7G,OAAA;sBAAM4G,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAhNExD,IAAI,CAAChD,EAAE;cAAAqG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiNZ,CACL;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDjH,OAAA;MAAK4G,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvC7G,OAAA;QACEkH,OAAO,EAAE7D,oBAAqB;QAC9BuD,SAAS,EAAC,0MAA0M;QAAAC,QAAA,gBAEpN7G,OAAA;UAAM4G,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,yBAE5D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3G,EAAA,CAnnCIH,kBAAkB;AAAAmI,EAAA,GAAlBnI,kBAAkB;AAqnCxB,eAAeA,kBAAkB;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}